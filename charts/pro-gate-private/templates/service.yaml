apiVersion: v1
kind: Service
metadata:
  name: {{ include "pro-gate-private.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "pro-gate-private.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - name: grpc
      port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
  selector:
    {{- include "pro-gate-private.selectorLabels" . | nindent 4 }}