apiVersion: v1
kind: Service
metadata:
  name: {{ include "pro-gate.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "pro-gate.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - name: grpc
      port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
    - name: grpc2
      port: {{ .Values.service.grpc2.port }}
      targetPort: {{ .Values.service.grpc2.targetPort }}
      protocol: TCP
  selector:
    {{- include "pro-gate.selectorLabels" . | nindent 4 }}