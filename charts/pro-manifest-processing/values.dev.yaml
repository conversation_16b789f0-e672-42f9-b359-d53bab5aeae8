# Default values for pro-manifest-processing.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: pro-manifest-processing

replicaCount: 1

image:
  repository: pro-manifest-processing
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

env:
  system_user_id: "a223116c-49b4-43eb-8a47-c76a68a4225d"
  client:
    logging:
      enable: true
  kafka:
    servers: "10.4.32.25:9092;10.4.32.140:9092;10.4.32.88:9092"
  zookeeper:
    nodes: "10.4.45.220:2181,10.4.45.19:2181,10.4.45.163:2181"
  service:
    abt_gate: "abt-gate.abt.svc.cluster.local:5000"
