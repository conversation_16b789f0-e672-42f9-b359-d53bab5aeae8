kind: VirtualService
apiVersion: networking.istio.io/v1beta1
metadata:
  name: pro-ui
  namespace: pro
spec:
  hosts:
    - crm-pro-dev.sbertroika.tech
  gateways:
    - istio-ingressgateway/pro-ui-gateway
  http:
    - match:
        - uri:
            prefix: /api/v1/pro/
      route:
        - destination:
            host: pro-controller.pro.svc.cluster.local
            port:
              number: 8080
    - match:
        - uri:
            prefix: /api/v1/abt/
      route:
        - destination:
            host: abt-controller.abt.svc.cluster.local
            port:
              number: 8080
    - match:
        - uri:
            prefix: /api/v1/tms/
      route:
        - destination:
            host: tms-controller.tms.svc.cluster.local
            port:
              number: 8080
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: pro-ui.pro.svc.cluster.local
            port:
              number: 80
status: {}
