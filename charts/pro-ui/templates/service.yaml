apiVersion: v1
kind: Service
metadata:
  name: {{ include "pro-ui.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "pro-ui.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - name: http
      port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
  selector:
    {{- include "pro-ui.selectorLabels" . | nindent 4 }}
