{{- if .Values.gateway.enabled -}}
kind: Gateway
apiVersion: networking.istio.io/v1beta1
metadata:
  name: pro-ui-gateway
  namespace: istio-ingressgateway
spec:
  servers:
    - port:
        number: 443
        protocol: HTTPS
        name: https
      hosts:
        - {{ required "gateway.host is required" .Values.gateway.host }}
      tls:
        mode: SIMPLE
        credentialName: {{ required "gateway.credentialName is required" .Values.gateway.credentialName }}
  selector:
    istio: ingressgateway
status: {}
{{- end }}