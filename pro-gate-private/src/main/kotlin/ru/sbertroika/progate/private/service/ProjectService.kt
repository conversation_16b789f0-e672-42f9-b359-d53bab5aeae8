package ru.sbertroika.progate.private.service

import kotlinx.coroutines.flow.Flow
import org.springframework.stereotype.Service
import ru.sbertroika.progate.private.output.persistance.model.Project
import ru.sbertroika.progate.private.output.persistance.model.ProjectStatus
import ru.sbertroika.progate.private.output.persistance.repository.ProjectRepository
import java.sql.Timestamp
import java.time.LocalDate
import java.util.*

/**
 * Сервис для работы с проектами
 * Содержит бизнес-логику управления проектами
 */
@Service
class ProjectService(
    private val projectRepository: ProjectRepository
) {

    /**
     * Создание нового проекта
     * @param project данные проекта для создания
     * @return созданный проект
     */
    suspend fun createProject(project: Project): Project {
        // Генерируем project ID если не указан
        val projectId = project.projectId ?: UUID.randomUUID()

        // Устанавливаем версию 1 для нового проекта
        val newProject = project.copy(
            id = null, // Автоинкремент
            projectId = projectId,
            version = 1,
            versionCreatedAt = Timestamp(System.currentTimeMillis()),
            versionCreatedBy = project.versionCreatedBy ?: UUID.randomUUID()
        )

        println("🔍 Сохраняем проект в БД: projectId=${newProject.projectId}, name=${newProject.name}")

        val savedProject = projectRepository.save(newProject)

        println("✅ Проект сохранен в БД: id=${savedProject.id}, projectId=${savedProject.projectId}, name=${savedProject.name}")

        return savedProject
    }

    /**
     * Поиск проекта по project ID (последняя версия)
     * @param projectId идентификатор проекта
     * @return проект или null если не найден
     */
    suspend fun findByProjectId(projectId: String): Project? {
        return try {
            val uuid = UUID.fromString(projectId)
            println("🔍 Ищем проект в БД по projectId: $uuid")
            val result = projectRepository.findByProjectIdLatestVersion(uuid)
            println("📋 Результат поиска: ${if (result != null) "найден (id=${result.id}, projectId=${result.projectId})" else "не найден"}")
            result
        } catch (e: IllegalArgumentException) {
            println("❌ Ошибка парсинга UUID: $projectId")
            null
        }
    }

    /**
     * Поиск проекта по project ID (последняя версия)
     * @param projectId идентификатор проекта
     * @return проект или null если не найден
     */
    suspend fun findByProjectId(projectId: UUID): Project? {
        return projectRepository.findByProjectIdLatestVersion(projectId)
    }

    /**
     * Поиск проекта по наименованию (последняя версия)
     * @param name наименование проекта
     * @return проект или null если не найден
     */
    suspend fun findByName(name: String): Project? {
        return projectRepository.findByNameLatestVersion(name)
    }

    /**
     * Получение всех проектов (последние версии)
     * @return поток проектов
     */
    fun findAll(): Flow<Project> {
        return projectRepository.findAllLatestVersions()
    }

    /**
     * Поиск проектов по статусу (последние версии)
     * @param status статус проекта
     * @return поток проектов
     */
    fun findByStatus(status: ProjectStatus): Flow<Project> {
        return projectRepository.findByStatusLatestVersions(status.name)
    }

    /**
     * Поиск проектов по ID договора (последние версии)
     * @param contractId идентификатор договора
     * @return поток проектов
     */
    fun findByContractId(contractId: UUID): Flow<Project> {
        return projectRepository.findByContractIdLatestVersions(contractId)
    }

    /**
     * Обновление проекта (создание новой версии)
     * @param project данные проекта для обновления
     * @return обновленный проект
     */
    suspend fun updateProject(project: Project): Project {
        // Находим текущую версию проекта
        val currentProject = project.projectId?.let { findByProjectId(it) }
            ?: throw IllegalArgumentException("Проект с project ID ${project.projectId} не найден")

        // Создаем новую версию
        val newVersion = (currentProject.version ?: 0) + 1
        val updatedProject = project.copy(
            id = null, // Автоинкремент для новой записи
            version = newVersion,
            versionCreatedAt = Timestamp(System.currentTimeMillis()),
            versionCreatedBy = project.versionCreatedBy ?: UUID.randomUUID()
        )

        return projectRepository.save(updatedProject)
    }

    /**
     * Удаление проекта (мягкое удаление - изменение статуса на ARCHIVE)
     * @param projectId идентификатор проекта
     * @return true если проект был удален, false если не найден
     */
    suspend fun deleteProject(projectId: UUID): Boolean {
        val project = findByProjectId(projectId) ?: return false

        val archivedProject = project.copy(
            id = null, // Автоинкремент для новой записи
            status = ProjectStatus.ARCHIVE.name,
            version = (project.version ?: 0) + 1,
            versionCreatedAt = Timestamp(System.currentTimeMillis())
        )

        projectRepository.save(archivedProject)
        return true
    }

    /**
     * Проверка существования проекта
     * @param projectId идентификатор проекта
     * @return true если проект существует
     */
    suspend fun existsByProjectId(projectId: UUID): Boolean {
        return findByProjectId(projectId) != null
    }

    /**
     * Проверка существования проекта по наименованию
     * @param name наименование проекта
     * @return true если проект существует
     */
    suspend fun existsByName(name: String): Boolean {
        return findByName(name) != null
    }
}
