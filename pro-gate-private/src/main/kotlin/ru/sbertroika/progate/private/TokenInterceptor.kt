package ru.sbertroika.progate.private

import io.grpc.Metadata
import io.grpc.ServerCall
import io.grpc.kotlin.CoroutineContextServerInterceptor
import kotlin.coroutines.CoroutineContext

class TokenInterceptor: CoroutineContextServerInterceptor()  {
    override fun coroutineContext(call: ServerCall<*, *>, headers: Metadata): CoroutineContext {
        if(headers.contains<PERSON>ey(key))
            return TokenElement(headers.get(key))
        return TokenElement(null)
    }
}

class TokenElement(val token: String?) : CoroutineContext.Element {
    companion object Key : CoroutineContext.Key<TokenElement>

    override val key: CoroutineContext.Key<TokenElement>
        get() = Key
}

const val AUTH_HEADER = "Authorization"

val key: Metadata.Key<String> = Metadata.Key.of(AUTH_HEADER, Metadata.ASCII_STRING_MARSHALLER)

fun getAuthMetadata(token: String?): Metadata  {
    val metadata = Metadata()
    metadata.put(key, token)
    return metadata
}