package ru.sbertroika.progate.private.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class KeycloakProperties {
    @Value("\${keycloak.host}")
    var host: String = ""
    @Value("\${keycloak.realm}")
    var realm: String = ""
    @Value("\${keycloak.userRealm}")
    var userRealm: String = ""
    @Value("\${keycloak.username}")
    var username: String = ""
    @Value("\${keycloak.secret}")
    var secret: String = ""
    @Value("\${keycloak.password}")
    var password: String = ""
    @Value("\${keycloak.clientId}")
    var clientId: String = ""
}

@Component
class TmsPrivateGateProperties(
    @Value("\${tms-private-gate.host}")
    var host: String = "",
)