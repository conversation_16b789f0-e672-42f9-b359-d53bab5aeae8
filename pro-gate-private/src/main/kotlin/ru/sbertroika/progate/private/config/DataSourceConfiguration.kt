package ru.sbertroika.progate.private.config

import io.r2dbc.postgresql.codec.EnumCodec
import io.r2dbc.spi.ConnectionFactoryOptions
import io.r2dbc.spi.Option
import org.springframework.boot.autoconfigure.r2dbc.ConnectionFactoryOptionsBuilderCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.convert.CustomConversions
import org.springframework.data.r2dbc.convert.R2dbcCustomConversions
import org.springframework.data.r2dbc.dialect.DialectResolver
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories
import org.springframework.r2dbc.core.DatabaseClient
import ru.sbertroika.tkp3.pro.model.*

@Configuration
@EnableR2dbcRepositories(
    basePackages = ["ru.sbertroika.progate.output.repository"],
)
open class DataSourceConfiguration  {

    @Bean
    open fun connectionFactoryOptionsBuilderCustomizer(): ConnectionFactoryOptionsBuilderCustomizer {
        return ConnectionFactoryOptionsBuilderCustomizer { builder: ConnectionFactoryOptions.Builder ->
            builder.option(
                Option.valueOf("extensions"),
                listOf(
                    EnumCodec.builder()
                        .withEnum("product_status", ProductStatus::class.java)
                        .withEnum("tariff_status", TariffStatus::class.java)
                        .withEnum("route_status", RouteStatus::class.java)
                        .withEnum("station_status", StationStatus::class.java)
                        .withEnum("vehicle_status", VehicleStatus::class.java)
                        .withEnum("vehicle_type", VehicleType::class.java)
                        .withEnum("route_scheme_type", RouteScheme::class.java)
                        .withRegistrationPriority(EnumCodec.Builder.RegistrationPriority.FIRST)
                        .build()
                )
            )
        }
    }

    @Bean
    open fun r2dbcCustomConversions(databaseClient: DatabaseClient): R2dbcCustomConversions? {
        val dialect = DialectResolver.getDialect(databaseClient.connectionFactory)
        val converters: MutableList<Any?> = ArrayList(dialect.converters)
        converters.addAll(R2dbcCustomConversions.STORE_CONVERTERS)
        return R2dbcCustomConversions(
            CustomConversions.StoreConversions.of(dialect.simpleTypeHolder, converters),
            listOf<Any?>(
                ProductStatusConverter(),
                TariffStatusConverter(),
                RouteStatusConverter(),
                StationStatusConverter(),
                VehicleStatusConverter(),
                RouteSchemeTypeConverter(),
                VehicleTypeConverter()
            )
        )
    }
}