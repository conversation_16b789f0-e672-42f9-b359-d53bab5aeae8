package ru.sbertroika.progate.private.input

import com.google.protobuf.Empty
import com.google.protobuf.Timestamp
import org.lognet.springboot.grpc.GRpcService
import org.springframework.stereotype.Service
import ru.sbertroika.pro.gate.v1.*
import ru.sbertroika.common.v1.*
import ru.sbertroika.common.pro.*
import ru.sbertroika.progate.private.output.persistance.model.Project
import ru.sbertroika.progate.private.output.persistance.model.ProjectStatus as DbProjectStatus
import ru.sbertroika.progate.private.service.ProjectService
import java.sql.Timestamp as SqlTimestamp
import java.time.Instant
import java.time.LocalDate
import java.util.*

@GRpcService
@Service
class PROGatePrivateServiceGrpc(
    private val projectService: ProjectService
) : PROGatePrivateServiceGrpcKt.PROGatePrivateServiceCoroutineImplBase() {

    override suspend fun getRoleList(request: Empty): RoleListResponse {
        // TODO: Implement role list
        return RoleListResponse.newBuilder()
            .build()
    }

    override suspend fun getEmployeeList(request: EmployeeListRequest): EmployeeListResponse {
        // TODO: Implement employee list
        return EmployeeListResponse.newBuilder()
            .build()
    }

    override suspend fun registrationEmployee(request: EmployeeRequest): CreateResponse {
        // TODO: Implement employee registration
        return CreateResponse.newBuilder()
            .build()
    }

    override suspend fun updateEmployee(request: EmployeeRequest): EmptyResponse {
        // TODO: Implement employee update
        return EmptyResponse.newBuilder()
            .build()
    }

    // TODO: Fix ByIdRequest conflict and implement getEmployeeById

    // List methods
    override suspend fun getRouteList(request: RouteListRequest): RouteListResponse {
        // TODO: Implement route list
        return RouteListResponse.newBuilder()
            .build()
    }

    override suspend fun getTariffList(request: TariffListRequest): TariffListResponse {
        // TODO: Implement tariff list
        return TariffListResponse.newBuilder()
            .build()
    }

    override suspend fun getProductList(request: ProductListRequest): ProductListResponse {
        // TODO: Implement product list
        return ProductListResponse.newBuilder()
            .build()
    }

    override suspend fun getStationList(request: StationListRequest): StationListResponse {
        // TODO: Implement station list
        return StationListResponse.newBuilder()
            .build()
    }

    override suspend fun getTransportList(request: TransportListRequest): TransportListResponse {
        // TODO: Implement transport list
        return TransportListResponse.newBuilder()
            .build()
    }

    // Create methods
    override suspend fun createRoute(request: Route): CreateResponse {
        // TODO: Implement route create
        return CreateResponse.newBuilder()
            .build()
    }

    override suspend fun createTariff(request: Tariff): CreateResponse {
        // TODO: Implement tariff create
        return CreateResponse.newBuilder()
            .build()
    }

    override suspend fun createProduct(request: Product): EmptyResponse {
        // TODO: Implement product create
        return EmptyResponse.newBuilder()
            .build()
    }

    override suspend fun createStation(request: Station): CreateResponse {
        // TODO: Implement station create
        return CreateResponse.newBuilder()
            .build()
    }

    override suspend fun createTransport(request: Transport): CreateResponse {
        // TODO: Implement transport create
        return CreateResponse.newBuilder()
            .build()
    }

    // Update methods
    override suspend fun updateRoute(request: Route): EmptyResponse {
        // TODO: Implement route update
        return EmptyResponse.newBuilder()
            .build()
    }

    override suspend fun updateTariff(request: Tariff): EmptyResponse {
        // TODO: Implement tariff update
        return EmptyResponse.newBuilder()
            .build()
    }

    override suspend fun updateProduct(request: Product): EmptyResponse {
        // TODO: Implement product update
        return EmptyResponse.newBuilder()
            .build()
    }

    override suspend fun updateStation(request: Station): EmptyResponse {
        // TODO: Implement station update
        return EmptyResponse.newBuilder()
            .build()
    }

    override suspend fun updateTransport(request: Transport): EmptyResponse {
        // TODO: Implement transport update
        return EmptyResponse.newBuilder()
            .build()
    }

    // TODO: Fix ByIdRequest conflict and implement delete methods

    // Project management methods
    override suspend fun createProject(request: CreateProjectRequest): CreateResponse {
        try {
            println("Создание проекта: ${request.name}")
            println("Договор: ${request.contractNumber} (ID: ${request.contractId})")
            println("Период: ${request.startDate} - ${request.endDate}")

            // Проверяем, что проект с таким именем не существует
            val existingProject = projectService.findByName(request.name)
            if (existingProject != null) {
                return CreateResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder()
                            .setCode(1003) // Код ошибки дублирования
                            .setMessage("Проект с таким наименованием региона уже существует")
                            .build()
                    )
                    .build()
            }

            // Создаем entity проекта
            val project = Project(
                id = null, // Автоинкремент
                projectId = UUID.randomUUID(),
                name = request.name,
                status = "TEST", // Начальный статус всегда TEST
                startDate = LocalDate.ofEpochDay(request.startDate.seconds / 86400),
                endDate = LocalDate.ofEpochDay(request.endDate.seconds / 86400),
                contractId = UUID.fromString(request.contractId),
                contractNumber = request.contractNumber,
                description = request.description,
                region = request.region.ifEmpty { request.name },
                tags = ""
            )

            // Сохраняем проект в базу данных
            println("🚀 Вызываем projectService.createProject() для проекта: ${project.projectId}")
            val savedProject = projectService.createProject(project)
            println("🎯 Проект сохранен через сервис: ${savedProject.projectId}")

            println("Проект успешно сохранен в БД с ID: ${savedProject.id}, Project ID: ${savedProject.projectId}")

            return CreateResponse.newBuilder()
                .setId(savedProject.projectId.toString())
                .build()
        } catch (e: Exception) {
            println("Ошибка при создании проекта: ${e.message}")
            e.printStackTrace()
            return CreateResponse.newBuilder()
                .setError(
                    OperationError.newBuilder()
                        .setCode(1001) // Код ошибки создания проекта
                        .setMessage("Ошибка при создании проекта: ${e.message}")
                        .build()
                )
                .build()
        }
    }

    override suspend fun getProjectList(request: ProjectListRequest): ProjectListResponse {
        // TODO: Implement project list retrieval from database
        return ru.sbertroika.pro.gate.v1.ProjectListResponse.newBuilder()
            .setResult(
                ru.sbertroika.pro.gate.v1.ProjectListResult.newBuilder()
                    .addProjects(
                        ru.sbertroika.pro.gate.v1.Project.newBuilder()
                            .setId("test-project-1")
                            .setName("Тестовый проект")
                            .setStatus(ru.sbertroika.pro.gate.v1.ProjectStatus.ACTIVE)
                            .setStartDate(Timestamp.newBuilder().setSeconds(Instant.now().epochSecond).build())
                            .setEndDate(Timestamp.newBuilder().setSeconds(Instant.now().plusSeconds(31536000).epochSecond).build())
                            .setContractId("contract-1")
                            .setContractNumber("СТ-ПРО-TEST-001")
                            .build()
                    )
                    .build()
            )
            .build()
    }

    override suspend fun getProjectById(request: ru.sbertroika.common.v1.ByIdRequest): ru.sbertroika.pro.gate.v1.ProjectResponse {
        // TODO: Implement project retrieval by ID from database
        return ru.sbertroika.pro.gate.v1.ProjectResponse.newBuilder()
            .setResult(
                ru.sbertroika.pro.gate.v1.Project.newBuilder()
                    .setId(request.id)
                    .setName("Тестовый проект")
                    .setStatus(ru.sbertroika.pro.gate.v1.ProjectStatus.ACTIVE)
                    .setStartDate(Timestamp.newBuilder().setSeconds(Instant.now().epochSecond).build())
                    .setEndDate(Timestamp.newBuilder().setSeconds(Instant.now().plusSeconds(31536000).epochSecond).build())
                    .setContractId("contract-1")
                    .setContractNumber("СТ-ПРО-TEST-001")
                    .build()
            )
            .build()
    }

    override suspend fun updateProjectStatus(request: UpdateProjectStatusRequest): EmptyResponse {
        try {
            println("Обновление статуса проекта ${request.projectId} на ${request.newStatus}")
            // TODO: Implement status update in database
            return EmptyResponse.newBuilder().build()
        } catch (e: Exception) {
            println("Ошибка при обновлении статуса проекта: ${e.message}")
            return EmptyResponse.newBuilder()
                .setError(
                    OperationError.newBuilder()
                        .setCode(1002) // Код ошибки обновления статуса проекта
                        .setMessage("Ошибка при обновлении статуса проекта: ${e.message}")
                        .build()
                )
                .build()
        }
    }
}
