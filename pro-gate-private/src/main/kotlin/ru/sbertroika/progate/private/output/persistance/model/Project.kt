package ru.sbertroika.progate.private.output.persistance.model

import org.springframework.data.annotation.*
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.time.LocalDate
import java.util.*

/**
 * Entity для проекта СберТройка ПРО
 * Представляет проект в системе с его основными характеристиками
 */
@Table("project")
data class Project(

    @Id
    @Column("id")
    var id: Long? = null,

    @Column("pr_id")
    var projectId: UUID? = null,
    
    @Column("pr_version")
    var version: Int? = null,
    
    @Column("pr_version_created_at")
    @CreatedDate
    var versionCreatedAt: Timestamp? = null,
    
    @Column("pr_version_created_by")
    @CreatedBy
    var versionCreatedBy: UUID? = null,
    
    @Column("pr_name")
    var name: String? = null,
    
    /**
     * Статус проекта
     * @see ProjectStatus
     */
    @Column("pr_status")
    var status: String? = null,
    
    @Column("pr_start_date")
    var startDate: LocalDate? = null,

    @Column("pr_end_date")
    var endDate: LocalDate? = null,

    @Column("pr_contract_id")
    var contractId: UUID? = null,
    
    @Column("pr_contract_number")
    var contractNumber: String? = null,
    
    @Column("pr_description")
    var description: String? = null,
    
    @Column("pr_region")
    var region: String? = null,
    
    @Column("tags")
    var tags: String? = null
)

/**
 * Составной первичный ключ для проекта (id + version)
 */
data class ProjectPK(
    val prId: UUID? = null,
    val prVersion: Int? = null,
) : Serializable

/**
 * Статусы проекта в системе СберТройка ПРО
 */
enum class ProjectStatus {
    /**
     * Тестовый статус - проект находится в стадии тестирования
     */
    TEST,
    
    /**
     * Демонстрационный статус - проект готов для демонстрации
     */
    DEMO,
    
    /**
     * Активный статус - проект работает в промышленной эксплуатации
     */
    ACTIVE,
    
    /**
     * Архивный статус - проект завершен и заархивирован
     */
    ARCHIVE
}
