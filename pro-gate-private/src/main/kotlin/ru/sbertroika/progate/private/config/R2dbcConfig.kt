package ru.sbertroika.progate.private.config

import org.springframework.context.annotation.Configuration
import org.springframework.data.r2dbc.config.EnableR2dbcAuditing
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories

/**
 * Конфигурация Spring Data R2DBC
 * Включает поддержку репозиториев и аудита
 */
@Configuration
@EnableR2dbcRepositories(basePackages = ["ru.sbertroika.progate.private.output.persistance.repository"])
@EnableR2dbcAuditing
open class R2dbcConfig
