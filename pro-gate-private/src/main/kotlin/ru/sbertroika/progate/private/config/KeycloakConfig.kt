package ru.sbertroika.progate.private.config

import org.jboss.resteasy.client.jaxrs.internal.ResteasyClientBuilderImpl
import org.keycloak.OAuth2Constants
import org.keycloak.admin.client.Keycloak
import org.keycloak.admin.client.KeycloakBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.slf4j.LoggerFactory

@Configuration
open class KeycloakConfig(
    private val properties: KeycloakProperties
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    @Bean
    open fun keycloak(): Keycloak {
        log.info("Keycloak info: serverUrl ${properties.host} \n" +
                " realm: ${properties.realm}\n" +
                " username: ${properties.username}\n" +
                " clientId: ${properties.clientId}")

        return KeycloakBuilder.builder()
            .serverUrl(properties.host)
            .realm(properties.realm)
            .grantType(OAuth2Constants.PASSWORD)
            .username(properties.username)
            .password(properties.password)
            .clientId(properties.clientId)
            .clientSecret(properties.secret)
            .resteasyClient(
                ResteasyClientBuilderImpl()
                    .connectionPoolSize(10)
                    .build())
            .build()
    }
}