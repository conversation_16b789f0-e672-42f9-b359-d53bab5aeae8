package ru.sbertroika.progate.private

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.context.annotation.ComponentScan

@SpringBootApplication
@ComponentScan(basePackages = ["ru.sbertroika.progate.private", "ru.sbertroika.tkp3.pro.model"])
open class ProGatePrivateApplication

fun main(args: Array<String>) {
    runApplication<ProGatePrivateApplication>(*args)
}
