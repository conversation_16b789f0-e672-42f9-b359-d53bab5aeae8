package ru.sbertroika.progate.private.util

import org.bouncycastle.crypto.digests.SHA256Digest
import org.bouncycastle.crypto.generators.PKCS5S2ParametersGenerator
import org.bouncycastle.crypto.params.KeyParameter
import java.security.SecureRandom
import java.util.*
import javax.xml.bind.DatatypeConverter


fun passHashing(password: String): String {
    val gen = PKCS5S2ParametersGenerator(SHA256Digest())
    val random = SecureRandom()
    val salt = ByteArray(16)
    random.nextBytes(salt)
    gen.init(password.toByteArray(), salt, 27500)
    val passwordHash = byteArrayToHexString((gen.generateDerivedParameters(256) as KeyParameter).key)
    val saltStr = byteArrayToHexString(salt)
    return "$passwordHash.$saltStr"
}

fun passVerify(password: String, passHash: String): Boolean {
    val passHashAndSalt = passHash.split(".")
    return if(passHashAndSalt.size == 2) {
        val salt = hexStringToByteArray(passHashAndSalt[1])
        val gen = PKCS5S2ParametersGenerator(SHA256Digest())
        gen.init(password.toByteArray(), salt, 27500)
        val passwordHash = byteArrayToHexString((gen.generateDerivedParameters(256) as KeyParameter).key)
        passwordHash == passHashAndSalt[0]
    } else false
}

fun byteArrayToHexString(byteArray: ByteArray): String? = bytes2HexStr(byteArray)

fun hexStringToByteArray(s: String): ByteArray? = hexStr2Bytes(s)

fun bytes2HexStr(bytes: ByteArray?, lts: Boolean = false): String? {
    if (bytes == null) {
        return null
    }
    var workBytes = bytes
    if (lts) {
        workBytes = bytes.reversedArray()
    }
    val sb = StringBuilder()
    var temp: String
    for (b in workBytes) {
        temp = Integer.toHexString(b.toPositiveInt())
        if (temp.length == 1) {
            temp = "0$temp"
        }
        sb.append(temp)
    }
    return sb.toString().uppercase(Locale.getDefault())
}

fun hexStr2Bytes(hexStr: String?, lts: Boolean = false): ByteArray? {
    if (hexStr.isNullOrBlank()) {
        return null
    }

    var workString = hexStr.lowercase()
    var length = workString.length
    if (length % 2 == 1) {
        workString = "0$workString"
        length++
    }
    val bytes = ByteArray(length shr 1)
    var index = 0

    for (i in 0 until length) {
        if (index > workString.length - 1) {
            return bytes
        }
        val highDit = (Character.digit(workString[index], 16) and 0xFF)
        val lowDit = (Character.digit(workString[index + 1], 16) and 0xFF)
        bytes[i] = (highDit shl 4 or lowDit).toByte()
        index += 2
    }
    if (lts) {
        bytes.reverse()
    }
    return bytes
}

fun Byte.toPositiveInt() = this.toInt() and 0xFF