package ru.sbertroika.progate.private.config

import io.grpc.ManagedChannel
import io.grpc.ManagedChannelBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import ru.sbertroika.tms.gate.v1.TMSGatePrivateServiceGrpcKt
import java.util.concurrent.TimeUnit

@Configuration
open class TmsPrivateGateConfig {

    @Bean
    open fun tmsPrivateGateChannel(properties: TmsPrivateGateProperties): ManagedChannel {
        val channel =  ManagedChannelBuilder.forTarget(properties.host).usePlaintext().build()
        channel.awaitTermination(10, TimeUnit.SECONDS)
        return channel
    }

    @Bean
    open fun tmsGatePrivate(channel: ManagedChannel) : TMSGatePrivateServiceGrpcKt.TMSGatePrivateServiceCoroutineStub {
        return TMSGatePrivateServiceGrpcKt.TMSGatePrivateServiceCoroutineStub(channel)
    }
}