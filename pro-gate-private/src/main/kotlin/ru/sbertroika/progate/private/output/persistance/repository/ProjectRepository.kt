package ru.sbertroika.progate.private.output.persistance.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.progate.private.output.persistance.model.Project
import java.util.*

/**
 * Spring Data R2DBC репозиторий для работы с проектами
 * Использует корутины для асинхронной работы с PostgreSQL
 */
@Repository
interface ProjectRepository : CoroutineCrudRepository<Project, Long> {

    /**
     * Поиск проекта по project ID с учетом версионности
     * Возвращает последнюю версию проекта
     */
    @Query("""
        SELECT * FROM project
        WHERE pr_id = :projectId
        AND pr_version = (SELECT MAX(pr_version) FROM project p2 WHERE p2.pr_id = :projectId)
    """)
    suspend fun findByProjectIdLatestVersion(projectId: UUID): Project?

    /**
     * Поиск проекта по наименованию с учетом версионности
     * Возвращает последнюю версию проекта
     */
    @Query("""
        SELECT * FROM project
        WHERE pr_name = :name
        AND pr_version = (SELECT MAX(pr_version) FROM project p2 WHERE p2.pr_id = project.pr_id)
    """)
    suspend fun findByNameLatestVersion(name: String): Project?

    /**
     * Поиск всех проектов с последними версиями
     */
    @Query("""
        SELECT * FROM project p1
        WHERE p1.pr_version = (SELECT MAX(p2.pr_version) FROM project p2 WHERE p2.pr_id = p1.pr_id)
        ORDER BY p1.pr_name
    """)
    fun findAllLatestVersions(): Flow<Project>

    /**
     * Поиск проектов по статусу с последними версиями
     */
    @Query("""
        SELECT * FROM project p1
        WHERE p1.pr_status = :status
        AND p1.pr_version = (SELECT MAX(p2.pr_version) FROM project p2 WHERE p2.pr_id = p1.pr_id)
        ORDER BY p1.pr_name
    """)
    fun findByStatusLatestVersions(status: String): Flow<Project>

    /**
     * Поиск проектов по ID договора с последними версиями
     */
    @Query("""
        SELECT * FROM project p1
        WHERE p1.pr_contract_id = :contractId
        AND p1.pr_version = (SELECT MAX(p2.pr_version) FROM project p2 WHERE p2.pr_id = p1.pr_id)
        ORDER BY p1.pr_name
    """)
    fun findByContractIdLatestVersions(contractId: UUID): Flow<Project>

}
