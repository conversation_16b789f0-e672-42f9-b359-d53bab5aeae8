spring:
  application:
    name: pro-gate-private
  main:
    allow-bean-definition-overriding: true

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/pro}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_REALM_URL:https://dev-auth.sbertroika.tech/realms/test-asop}
    client_id: ${KEYCLOAK_CLIENT_ID:test-auth}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:false}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://localhost:5432/pro}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

tms-private-gate:
  host: ${TMS_GATE_URL:tms-gate-private.tms-gate.svc.cluster.local:5005}

grpc:
  port: 5005
server:
  port: 8080

keycloak:
  host: ${TMS_KEYCLOAK_HOST:http://localhost:8080/}
  realm: ${TMS_KEYCLOAK_REALM:master}
  secret: ${TMS_KEYCLOAK_SECRET:ev9dboyl6I21wXvOjWHqyk2ag70jlIJp}
  password: ${TMS_KEYCLOAK_PASSWORD:12345}
  username: ${TMS_KEYCLOAK_USERNAME:user}
  clientId: ${TMS_KEYCLOAK_CLIENT_ID:test-client}
  userRealm: ${TMS_KEYCLOAK_USER_REALM:test-realm}

project_id: ${PROJECT_ID:5a097adc-a337-428a-958c-79e85a44ebb8}
project_index: ${PROJECT_INDEX:1}
org_id: ${ORG_ID:43db9de7-72ec-4eae-8978-8aef1c46873a}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness:
          include: '*'
        readiness:
          include: '*'
