package ru.sbertroika.progate.priv.cucumber.testcontainers

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.DisplayName

/**
 * Тест для проверки работы Keycloak testcontainer
 */
class KeycloakTestContainerTest {

    @Test
    @DisplayName("Должен успешно получить JWT токен для администратора")
    fun shouldGetAdminJwtToken() {
        val keycloak = KeycloakTestContainer.getInstance()
        
        // Получаем JWT токен для администратора
        val adminToken = keycloak.getAdminJwtToken()
        
        // Проверяем, что токен получен
        assertNotNull(adminToken, "JWT токен администратора не должен быть null")
        assertTrue(adminToken.isNotEmpty(), "JWT токен администратора не должен быть пустым")
        
        // JWT токен должен содержать точки (разделители частей токена)
        assertTrue(adminToken.contains("."), "JWT токен должен содержать точки")
        
        // JWT токен должен состоять из 3 частей
        val parts = adminToken.split(".")
        assertEquals(3, parts.size, "JWT токен должен состоять из 3 частей")
        
        println("Получен JWT токен администратора: ${adminToken.take(50)}...")
    }

    @Test
    @DisplayName("Должен успешно получить JWT токен для обычного пользователя")
    fun shouldGetUserJwtToken() {
        val keycloak = KeycloakTestContainer.getInstance()
        
        // Получаем JWT токен для пользователя
        val userToken = keycloak.getUserJwtToken()
        
        // Проверяем, что токен получен
        assertNotNull(userToken, "JWT токен пользователя не должен быть null")
        assertTrue(userToken.isNotEmpty(), "JWT токен пользователя не должен быть пустым")
        
        // JWT токен должен содержать точки (разделители частей токена)
        assertTrue(userToken.contains("."), "JWT токен должен содержать точки")
        
        // JWT токен должен состоять из 3 частей
        val parts = userToken.split(".")
        assertEquals(3, parts.size, "JWT токен должен состоять из 3 частей")
        
        println("Получен JWT токен пользователя: ${userToken.take(50)}...")
    }

    @Test
    @DisplayName("Должен возвращать корректную информацию о сервере")
    fun shouldReturnCorrectServerInfo() {
        val keycloak = KeycloakTestContainer.getInstance()
        
        // Проверяем URL сервера
        val authServerUrl = keycloak.getAuthServerUrl()
        assertNotNull(authServerUrl, "URL сервера не должен быть null")
        assertTrue(authServerUrl.startsWith("http"), "URL сервера должен начинаться с http")
        
        // Проверяем имя realm'а
        val realmName = keycloak.getRealmName()
        assertEquals("pro-gate-test", realmName, "Имя realm'а должно быть 'pro-gate-test'")
        
        println("Keycloak сервер: $authServerUrl")
        println("Realm: $realmName")
    }

    @Test
    @DisplayName("Должен получать разные токены для разных пользователей")
    fun shouldGetDifferentTokensForDifferentUsers() {
        val keycloak = KeycloakTestContainer.getInstance()
        
        // Получаем токены для разных пользователей
        val adminToken = keycloak.getAdminJwtToken()
        val userToken = keycloak.getUserJwtToken()
        
        // Токены должны быть разными
        assertNotEquals(adminToken, userToken, "Токены для разных пользователей должны отличаться")
        
        println("Токен администратора: ${adminToken.take(50)}...")
        println("Токен пользователя: ${userToken.take(50)}...")
    }
}
