package ru.sbertroika.progate.priv.cucumber.testcontainers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import dasniko.testcontainers.keycloak.KeycloakContainer
import okhttp3.*
import java.io.IOException
import java.time.Duration

/**
 * Управляет Keycloak testcontainer для интеграционных тестов
 * Предоставляет методы для получения JWT токенов
 */
class KeycloakTestContainer {
    
    companion object {
        private const val KEYCLOAK_VERSION = "22.0.5"
        private const val REALM_NAME = "pro-gate-test"
        private const val CLIENT_ID = "pro-gate-client"
        private const val ADMIN_USERNAME = "admin"
        private const val ADMIN_PASSWORD = "admin123"
        private const val USER_USERNAME = "user"
        private const val USER_PASSWORD = "user123"
        
        @Volatile
        private var INSTANCE: KeycloakTestContainer? = null
        
        fun getInstance(): KeycloakTestContainer {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: KeycloakTestContainer().also { INSTANCE = it }
            }
        }
    }

    private val keycloakContainer: KeycloakContainer
    private val httpClient: OkHttpClient
    private val objectMapper: ObjectMapper
    private var containerStarted: Boolean = false

    init {
        // Инициализируем Keycloak контейнер с упрощенной конфигурацией
        keycloakContainer = KeycloakContainer("quay.io/keycloak/keycloak:$KEYCLOAK_VERSION")
            .withRealmImportFile("keycloak/test-realm.json")
            .withStartupTimeout(Duration.ofMinutes(5))
            .withEnv("KEYCLOAK_ADMIN", ADMIN_USERNAME)
            .withEnv("KEYCLOAK_ADMIN_PASSWORD", ADMIN_PASSWORD)
            .withExposedPorts(8080)
            .waitingFor(
                org.testcontainers.containers.wait.strategy.Wait.forHttp("/health/ready")
                    .forPort(8080)
                    .withStartupTimeout(Duration.ofMinutes(5))
            )
        
        httpClient = OkHttpClient.Builder()
            .connectTimeout(Duration.ofSeconds(30))
            .readTimeout(Duration.ofSeconds(30))
            .build()
        
        objectMapper = ObjectMapper().registerModule(KotlinModule.Builder().build())
    }

    private fun startContainer() {
        if (!containerStarted && !keycloakContainer.isRunning) {
            try {
                println("Запуск Keycloak контейнера...")
                keycloakContainer.start()
                containerStarted = true
                println("Keycloak запущен на: ${keycloakContainer.authServerUrl}")
                println("Realm: $REALM_NAME")
            } catch (e: Exception) {
                println("Ошибка запуска Keycloak: ${e.message}")
                throw e
            }
        }
    }

    /**
     * Получает JWT токен для администратора
     */
    fun getAdminJwtToken(): String {
        startContainer()
        return getUserJwtToken(ADMIN_USERNAME, ADMIN_PASSWORD)
    }
    
    /**
     * Получает JWT токен для обычного пользователя
     */
    fun getUserJwtToken(): String {
        startContainer()
        return getUserJwtToken(USER_USERNAME, USER_PASSWORD)
    }

    /**
     * Получает JWT токен для указанного пользователя
     */
    fun getUserJwtToken(username: String, password: String): String {
        val url = "${keycloakContainer.authServerUrl}/realms/$REALM_NAME/protocol/openid-connect/token"
        
        val formBody = FormBody.Builder()
            .add("grant_type", "password")
            .add("client_id", CLIENT_ID)
            .add("username", username)
            .add("password", password)
            .build()
        
        val request = Request.Builder()
            .url(url)
            .post(formBody)
            .build()
        
        httpClient.newCall(request).execute().use { response ->
            if (!response.isSuccessful) {
                throw IOException("Ошибка получения JWT токена для $username: ${response.code} - ${response.body?.string()}")
            }
            
            val responseBody = response.body?.string() ?: throw IOException("Пустой ответ")
            val tokenResponse = objectMapper.readTree(responseBody)
            return tokenResponse.get("access_token").asText()
        }
    }
    
    /**
     * Возвращает URL Keycloak сервера
     */
    fun getAuthServerUrl(): String = keycloakContainer.authServerUrl
    
    /**
     * Возвращает имя realm'а
     */
    fun getRealmName(): String = REALM_NAME

    /**
     * Запускает контейнер, если он еще не запущен
     */
    fun start() {
        if (!keycloakContainer.isRunning) {
            keycloakContainer.start()
            println("Keycloak testcontainer запущен на: ${getAuthServerUrl()}")
        }
    }

    /**
     * Останавливает контейнер
     */
    fun stop() {
        if (keycloakContainer.isRunning) {
            keycloakContainer.stop()
        }
        httpClient.dispatcher.executorService.shutdown()
    }
}
