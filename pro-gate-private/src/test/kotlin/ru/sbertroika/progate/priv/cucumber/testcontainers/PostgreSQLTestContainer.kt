package ru.sbertroika.progate.priv.cucumber.testcontainers

import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.utility.DockerImageName
import java.time.Duration

/**
 * Управляет PostgreSQL testcontainer для интеграционных тестов
 * Предоставляет настроенную PostgreSQL базу данных для тестов
 */
class PostgreSQLTestContainer {
    
    companion object {
        private const val POSTGRES_VERSION = "15.4"
        private const val DATABASE_NAME = "testdb"
        private const val USERNAME = "test"
        private const val PASSWORD = "test"
        
        @Volatile
        private var INSTANCE: PostgreSQLTestContainer? = null
        
        fun getInstance(): PostgreSQLTestContainer {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PostgreSQLTestContainer().also { INSTANCE = it }
            }
        }
    }

    private val postgresContainer: PostgreSQLContainer<*>
    private var containerStarted: Boolean = false

    init {
        // Инициализируем PostgreSQL контейнер
        postgresContainer = PostgreSQLContainer(DockerImageName.parse("postgres:$POSTGRES_VERSION"))
            .withDatabaseName(DATABASE_NAME)
            .withUsername(USERNAME)
            .withPassword(PASSWORD)
            .withStartupTimeout(Duration.ofMinutes(3))
            .withReuse(true) // Переиспользуем контейнер между тестами для ускорения
    }

    /**
     * Запускает контейнер, если он еще не запущен
     */
    fun start() {
        if (!containerStarted && !postgresContainer.isRunning) {
            try {
                println("Запуск PostgreSQL контейнера...")
                postgresContainer.start()
                containerStarted = true
                println("PostgreSQL запущен на: ${getJdbcUrl()}")
                println("Database: $DATABASE_NAME, User: $USERNAME")
            } catch (e: Exception) {
                println("Ошибка запуска PostgreSQL: ${e.message}")
                throw e
            }
        }
    }

    /**
     * Останавливает контейнер
     */
    fun stop() {
        if (postgresContainer.isRunning) {
            postgresContainer.stop()
            containerStarted = false
        }
    }

    /**
     * Возвращает JDBC URL для подключения к базе данных
     */
    fun getJdbcUrl(): String {
        start()
        return postgresContainer.jdbcUrl
    }

    /**
     * Возвращает имя пользователя для подключения к базе данных
     */
    fun getUsername(): String = USERNAME

    /**
     * Возвращает пароль для подключения к базе данных
     */
    fun getPassword(): String = PASSWORD

    /**
     * Возвращает имя базы данных
     */
    fun getDatabaseName(): String = DATABASE_NAME

    /**
     * Возвращает хост базы данных
     */
    fun getHost(): String {
        start()
        return postgresContainer.host
    }

    /**
     * Возвращает порт базы данных
     */
    fun getPort(): Int {
        start()
        return postgresContainer.getMappedPort(PostgreSQLContainer.POSTGRESQL_PORT)
    }

    /**
     * Проверяет, запущен ли контейнер
     */
    fun isRunning(): Boolean = postgresContainer.isRunning
}
