package ru.sbertroika.progate.priv.cucumber.config

import io.cucumber.spring.CucumberContextConfiguration
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import ru.sbertroika.progate.private.ProGatePrivateApplication
import ru.sbertroika.progate.priv.cucumber.testcontainers.KeycloakTestContainer
import ru.sbertroika.progate.priv.cucumber.testcontainers.PostgreSQLTestContainer
import org.flywaydb.core.Flyway
import javax.sql.DataSource

/**
 * Конфигурация Spring контекста для Cucumber тестов
 * Запускает полный Spring Boot контекст с gRPC сервером
 * Интегрируется с Keycloak testcontainer
 */
@CucumberContextConfiguration
@SpringBootTest(
    classes = [ProGatePrivateApplication::class],
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@ActiveProfiles("test")
class CucumberSpringConfiguration {

    companion object {
        private val keycloakContainer = KeycloakTestContainer.getInstance()
        private val postgresContainer = PostgreSQLTestContainer.getInstance()

        @JvmStatic
        @DynamicPropertySource
        fun configureProperties(registry: DynamicPropertyRegistry) {
            // Запускаем PostgreSQL контейнер перед Spring контекстом
            postgresContainer.start()

            // Запускаем Keycloak контейнер перед Spring контекстом
            keycloakContainer.start()

            // Настраиваем PostgreSQL для Spring
            registry.add("spring.datasource.url") { postgresContainer.getJdbcUrl() }
            registry.add("spring.datasource.username") { postgresContainer.getUsername() }
            registry.add("spring.datasource.password") { postgresContainer.getPassword() }
            registry.add("spring.datasource.driver-class-name") { "org.postgresql.Driver" }

            // Настраиваем R2DBC для реактивного доступа к базе данных
            registry.add("spring.r2dbc.url") {
                "r2dbc:postgresql://${postgresContainer.getHost()}:${postgresContainer.getPort()}/${postgresContainer.getDatabaseName()}"
            }
            registry.add("spring.r2dbc.username") { postgresContainer.getUsername() }
            registry.add("spring.r2dbc.password") { postgresContainer.getPassword() }

            // Настраиваем Spring Security OAuth2 для использования Keycloak testcontainer
            val keycloakUrl = keycloakContainer.getAuthServerUrl()
            val realmName = keycloakContainer.getRealmName()

            registry.add("spring.security.oauth2.resourceserver.jwt.issuer-uri") {
                "$keycloakUrl/realms/$realmName"
            }

            println("Настроен PostgreSQL для Spring: ${postgresContainer.getJdbcUrl()}")
            println("Настроен Keycloak для Spring: $keycloakUrl/realms/$realmName")

            // Запускаем Flyway миграции после настройки testcontainer
            runFlywayMigrations()
        }

        /**
         * Запускает Flyway миграции для testcontainer базы данных
         */
        private fun runFlywayMigrations() {
            try {
                val flyway = Flyway.configure()
                    .dataSource(postgresContainer.getJdbcUrl(), postgresContainer.getUsername(), postgresContainer.getPassword())
                    .locations("classpath:db/migration")
                    .baselineOnMigrate(true)
                    .baselineVersion("0")
                    .load()

                println("Запуск Flyway миграций...")
                flyway.migrate()
                println("Flyway миграции выполнены успешно")
            } catch (e: Exception) {
                println("Ошибка выполнения Flyway миграций: ${e.message}")
                throw e
            }
        }
    }
}
