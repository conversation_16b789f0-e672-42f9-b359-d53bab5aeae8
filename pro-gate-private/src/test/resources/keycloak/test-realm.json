{"realm": "pro-gate-test", "enabled": true, "displayName": "Pro Gate Test Realm", "accessTokenLifespan": 3600, "refreshTokenMaxReuse": 0, "sslRequired": "none", "registrationAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "clients": [{"clientId": "pro-gate-client", "enabled": true, "publicClient": true, "directAccessGrantsEnabled": true, "standardFlowEnabled": true, "implicitFlowEnabled": false, "serviceAccountsEnabled": false, "redirectUris": ["*"], "webOrigins": ["*"], "protocol": "openid-connect"}], "roles": {"realm": [{"name": "admin", "description": "Administrator role"}, {"name": "user", "description": "User role"}]}, "users": [{"username": "admin", "enabled": true, "credentials": [{"type": "password", "value": "admin123", "temporary": false}], "realmRoles": ["admin", "user"]}, {"username": "user", "enabled": true, "credentials": [{"type": "password", "value": "user123", "temporary": false}], "realmRoles": ["user"]}]}