spring:
  application:
    name: pro-gate-private-test

  # PostgreSQL будет настроен через testcontainer в тестах
  datasource:
    url: ***************************************
    driver-class-name: org.postgresql.Driver
    username: test
    password: test

  jpa:
    hibernate:
      ddl-auto: none  # Используем Flyway для создания схемы
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect

  flyway:
    enabled: false  # Отключаем автоматический запуск Flyway
    # Используем миграции из основного микросервиса pro-gate
    locations: classpath:db/migration
    baseline-on-migrate: true
    baseline-version: 0

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8080/auth/realms/test

logging:
  level:
    ru.sbertroika: DEBUG
    org.springframework.security: DEBUG
    io.cucumber: DEBUG

grpc:
  server:
    port: 0  # Случайный порт для тестов

# Тестовые настройки
test:
  data:
    cleanup: true
  security:
    enabled: false
