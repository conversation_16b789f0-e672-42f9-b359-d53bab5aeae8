# language: ru
Функция: Управление проектами СберТройка ПРО
  Как администратор системы СберТройка ПРО
  Я хочу управлять проектами на основе данных договоров
  Чтобы обеспечить эффективную работу с проектами и контроль их жизненного цикла

  Предыстория:
    Дано я авторизован как администратор системы
    И система готова к работе

  Сценарий: Создание проекта на основе данных договора
    Дано пользователь предоставил данные договора:
      | номер_договора     | СТ-ПРО-2024-001 |
      | название_договора  | Правила системы СберТройка ПРО г. Москва |
      | дата_начала       | 2024-01-01 |
      | дата_окончания    | 2024-12-31 |
      | федеральный_id    | DOC_000001_2024 |
    Когда я создаю новый проект на основе предоставленных данных договора с параметрами:
      | наименование | Москва |
    Тогда проект должен быть успешно создан
    И проект должен иметь уникальный идентификатор
    И проект должен быть в статусе "TEST"
    И проект должен иметь наименование "Москва"
    И срок действия проекта должен наследоваться от договора
    И дата начала проекта должна быть "2024-01-01"
    И дата окончания проекта должна быть "2024-12-31"

  Сценарий: Просмотр списка проектов с новыми статусами
    Дано в системе существуют проекты:
      | наименование      | статус        | договор_номер    |
      | Москва           | ACTIVE        | СТ-ПРО-2024-001  |
      | Санкт-Петербург  | TEST          | СТ-ПРО-2024-002  |
      | Казань           | DEMO          | СТ-ПРО-2024-003  |
      | Екатеринбург     | ARCHIVE       | СТ-ПРО-2024-004  |
      | Новосибирск      | NSI_CONFLICT  | СТ-ПРО-2024-005  |
    Когда я запрашиваю список всех проектов
    Тогда я должен получить список из 5 проектов
    И проекты должны быть отсортированы по наименованию

  Сценарий: Фильтрация проектов по статусу
    Дано в системе существуют проекты с разными статусами
    Когда я запрашиваю список проектов со статусом "ACTIVE"
    Тогда я должен получить только активные проекты
    И проекты в других статусах не должны быть в списке

  Сценарий: Переход проекта из статуса TEST в DEMO
    Дано существует проект с наименованием "Тестовый регион" в статусе "TEST"
    Когда я изменяю статус проекта на "DEMO"
    Тогда статус проекта должен быть успешно изменен на "DEMO"
    И проект должен быть доступен для демонстрационных операций

  Сценарий: Переход проекта из статуса TEST в ACTIVE
    Дано существует проект с наименованием "Готовый регион" в статусе "TEST"
    Когда я изменяю статус проекта на "ACTIVE"
    Тогда статус проекта должен быть успешно изменен на "ACTIVE"
    И проект должен быть доступен для всех промышленных операций

  Сценарий: Переход проекта из статуса DEMO в ACTIVE
    Дано существует проект с наименованием "Демо регион" в статусе "DEMO"
    Когда я изменяю статус проекта на "ACTIVE"
    Тогда статус проекта должен быть успешно изменен на "ACTIVE"
    И проект должен быть доступен для всех промышленных операций

  Сценарий: Переход проекта из статуса ACTIVE в ARCHIVE
    Дано существует проект с наименованием "Завершенный регион" в статусе "ACTIVE"
    Когда я изменяю статус проекта на "ARCHIVE"
    Тогда статус проекта должен быть успешно изменен на "ARCHIVE"
    И проект должен быть заблокирован для операций

  Сценарий: Обнаружение конфликта НСИ и переход в статус NSI_CONFLICT
    Дано существует проект с наименованием "Проблемный регион" в статусе "ACTIVE"
    И в системе обнаружен конфликт НСИ для данного проекта
    Когда система автоматически изменяет статус проекта на "NSI_CONFLICT"
    Тогда статус проекта должен быть "NSI_CONFLICT"
    И проект должен быть заблокирован для использования
    И должно быть создано уведомление о конфликте НСИ

  Сценарий: Разрешение конфликта НСИ и возврат в статус TEST
    Дано существует проект с наименованием "Восстановленный регион" в статусе "NSI_CONFLICT"
    И конфликт НСИ был устранен
    Когда я изменяю статус проекта на "TEST"
    Тогда статус проекта должен быть успешно изменен на "TEST"
    И проект должен быть доступен для повторной проверки

  Сценарий: Поиск проектов по наименованию региона
    Дано в системе существуют проекты:
      | наименование      | статус |
      | Москва           | ACTIVE |
      | Московская область| DEMO   |
      | Санкт-Петербург  | TEST   |
    Когда я ищу проекты по запросу "Моск"
    Тогда я должен найти 2 проекта
    И все найденные проекты должны содержать "Моск" в наименовании

  Сценарий: Получение проекта по идентификатору
    Дано существует проект с идентификатором "12345"
    Когда я запрашиваю проект по идентификатору "12345"
    Тогда я должен получить данные проекта
    И данные должны содержать все поля проекта
    И данные должны содержать информацию о связанном договоре

  Сценарий: Попытка недопустимого перехода статуса
    Дано существует проект с наименованием "Тестовый регион" в статусе "TEST"
    Когда я пытаюсь изменить статус проекта на "ARCHIVE"
    Тогда операция должна завершиться ошибкой
    И я должен получить сообщение об ошибке "Недопустимый переход статуса из TEST в ARCHIVE"

  Сценарий: Попытка создания проекта с дублирующимся наименованием региона
    Дано существует проект с наименованием "Москва"
    И пользователь предоставил данные нового договора "СТ-ПРО-2024-006"
    Когда я пытаюсь создать новый проект на основе предоставленных данных с наименованием "Москва"
    Тогда операция должна завершиться ошибкой
    И я должен получить сообщение об ошибке "Проект с таким наименованием региона уже существует"

  Сценарий: Попытка создания проекта без предоставления данных договора
    Когда я пытаюсь создать проект без предоставления данных договора:
      | наименование | Новый регион |
    Тогда операция должна завершиться ошибкой валидации
    И я должен получить сообщение об ошибке "Данные договора являются обязательными для создания проекта"

  Сценарий: Валидация наименования проекта по региону
    Дано пользователь предоставил данные договора "СТ-ПРО-2024-007"
    Когда я пытаюсь создать проект с некорректным наименованием региона:
      | наименование | 123InvalidRegion!@# |
    Тогда операция должна завершиться ошибкой валидации
    И я должен получить сообщение об ошибке "Наименование проекта должно соответствовать названию региона"

  Сценарий: Блокировка операций для проекта в статусе TEST
    Дано существует проект с наименованием "Тестовый регион" в статусе "TEST"
    Когда я пытаюсь выполнить промышленную операцию с проектом
    Тогда операция должна быть заблокирована
    И я должен получить сообщение "Промышленные операции запрещены для проектов в статусе TEST"

  Сценарий: Блокировка операций для проекта в статусе NSI_CONFLICT
    Дано существует проект с наименованием "Конфликтный регион" в статусе "NSI_CONFLICT"
    Когда я пытаюсь выполнить любую операцию с проектом
    Тогда операция должна быть заблокирована
    И я должен получить сообщение "Проект заблокирован из-за конфликта НСИ"

  Сценарий: Обновление проекта при изменении данных договора
    Дано существует проект с наименованием "Обновляемый регион"
    И пользователь предоставил обновленные данные договора с новыми сроками
    Когда я обновляю проект согласно новым данным договора
    Тогда данные проекта должны быть обновлены согласно договору
    И срок действия проекта должен соответствовать обновленному договору
    И должна быть зафиксирована дата последнего обновления

  Сценарий: Создание проекта через пользовательский интерфейс
    Дано пользователь открыл форму создания проекта
    И пользователь ввел данные договора:
      | номер_договора     | СТ-ПРО-2024-008 |
      | название_договора  | Правила системы СберТройка ПРО г. Воронеж |
      | дата_начала       | 2024-06-01 |
      | дата_окончания    | 2025-05-31 |
      | федеральный_id    | DOC_000008_2024 |
    Когда пользователь указывает наименование проекта "Воронеж"
    И пользователь подтверждает создание проекта
    Тогда проект должен быть успешно создан
    И проект должен быть в статусе "TEST"
    И проект должен быть связан с введенными данными договора
