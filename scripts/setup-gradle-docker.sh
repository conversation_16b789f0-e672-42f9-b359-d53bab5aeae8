#!/bin/bash

# Скрипт для настройки авторизации Gradle в CI для сборки образа
# Передает mavenUser и mavenPassword через глобальные настройки Gradle

set -e

echo "Настройка Gradle для docker..."

# Проверяем наличие переменных
if [ -z "$MAVEN_USER" ]; then
    echo "Ошибка: переменная MAVEN_USER не установлена"
    exit 1
fi

if [ -z "$MAVEN_PASSWORD" ]; then
    echo "Ошибка: переменная MAVEN_PASSWORD не установлена"
    exit 1
fi

GRADLE_USER_HOME_DOCKER="$(pwd)/.ci-gradle"
mkdir -p "$GRADLE_USER_HOME_DOCKER"

# Создаем gradle.properties в пользовательской директории Gradle
cat > "$GRADLE_USER_HOME_DOCKER/gradle.properties" << EOF
mavenUser=$MAVEN_USER
mavenPassword=$MAVEN_PASSWORD
EOF

echo "Авторизация Gradle для docker настроена успешно"
echo "Добавлены настройки для пользователя: $MAVEN_USER в $GRADLE_USER_HOME_DOCKER/gradle.properties"