#!/bin/bash

# Скрипт для настройки авторизации Gradle в CI
# Передает mavenUser и mavenPassword через глобальные настройки Gradle

set -e

echo "Настройка авторизации Gradle..."

# Проверяем наличие переменных
if [ -z "$MAVEN_USER" ]; then
    echo "Ошибка: переменная MAVEN_USER не установлена"
    exit 1
fi

if [ -z "$MAVEN_PASSWORD" ]; then
    echo "Ошибка: переменная MAVEN_PASSWORD не установлена"
    exit 1
fi

# Используем GRADLE_USER_HOME или домашнюю директорию текущего пользователя
GRADLE_USER_HOME="${GRADLE_USER_HOME:-$HOME/.gradle}"
echo "Используем GRADLE_USER_HOME: $GRADLE_USER_HOME"

# Создаем директорию, если она не существует
mkdir -p "$GRADLE_USER_HOME"

# Создаем gradle.properties в пользовательской директории Gradle
cat > "$GRADLE_USER_HOME/gradle.properties" << EOF
mavenUser=$MAVEN_USER
mavenPassword=$MAVEN_PASSWORD
EOF

echo "Авторизация Gradle настроена успешно"
echo "Добавлены настройки для пользователя: $MAVEN_USER в $GRADLE_USER_HOME/gradle.properties" 