#!/bin/bash

# Helm validation script
# Usage: helm_validate -n <namespace> -s <stage> -c <chart_name>

# Default values
NAMESPACE=""
STAGE=""
CHART_NAME=""

# Parse command line arguments
while getopts ":n:s:c:" opt; do
  case $opt in
    n) NAMESPACE="$OPTARG"
    ;;
    s) STAGE="$OPTARG"
    ;;
    c) CHART_NAME="$OPTARG"
    ;;
    \?) echo "Invalid option -$OPTARG" >&2
    exit 1
    ;;
  esac

  case $OPTARG in
    -*) echo "Option $opt needs a valid argument"
    exit 1
    ;;
  esac
done

# Validate required parameters
if [[ -z "$NAMESPACE" || -z "$STAGE" || -z "$CHART_NAME" ]]; then
  echo "Error: Missing required arguments"
  echo "Usage: helm_validate -n <namespace> -s <stage> -c <chart_name>"
  exit 1
fi

# Validate chart directory exists
CHART_DIR="./charts/$CHART_NAME"
if [[ ! -d "$CHART_DIR" ]]; then
  echo "Error: Chart directory $CHART_DIR does not exist"
  exit 1
fi

# Validate stage-specific values file exists
STAGE_VALUES_FILE="$CHART_DIR/values.$STAGE.yaml"
if [[ ! -f "$STAGE_VALUES_FILE" ]]; then
  echo "Error: Stage values file $STAGE_VALUES_FILE does not exist"
  exit 1
fi

# Run helm dry-run install
echo "Validating Helm chart $CHART_NAME for stage $STAGE in namespace $NAMESPACE"
helm install --dry-run -n "$NAMESPACE" "$CHART_NAME" "$CHART_DIR" \
  -f "$CHART_DIR/values.yaml" \
  -f "$STAGE_VALUES_FILE"

# Check if helm command succeeded
if [[ $? -ne 0 ]]; then
  echo "Error: Helm validation failed"
  exit 1
fi

echo "Helm validation completed successfully"
exit 0