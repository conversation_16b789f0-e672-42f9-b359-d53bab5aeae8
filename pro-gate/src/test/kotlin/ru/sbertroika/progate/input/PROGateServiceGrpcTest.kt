package ru.sbertroika.progate.input

import com.google.protobuf.Empty
import io.grpc.*
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import ru.sbertroika.common.manifest.v1.manifestRequest
import ru.sbertroika.pro.gate.v1.PROGateServiceGrpcKt
import ru.sbertroika.pro.gate.v1.manifest

class PROGateServiceGrpcTest {

    companion object {
        private const val server = "localhost"
        private const val port = 5010
        private const val isTls = false
    }

    @Test
    fun getManifestTest(): Unit = runBlocking {
        val response = client().getManifest(Empty.getDefaultInstance())
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getManifestByProjectTest(): Unit = runBlocking {
        val response = client().getManifestByProject(
            manifestRequest {
                projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getStationListTest(): Unit = runBlocking {
        val response = client().getStationList(
            manifest {

            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getTariffListTest(): Unit = runBlocking {
        val response = client().getTariffList(
            manifest {

            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getRouteListTest(): Unit = runBlocking {
        val response = client().getRouteList(
            manifest {

            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getProductListTest(): Unit = runBlocking {
        val response = client().getProductList(
            manifest {

            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getTransportListTest(): Unit = runBlocking {
        val response = client().getTransportList(
            manifest {

            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getProductMenuTest(): Unit = runBlocking {
        val response = client().getProductMenu(
            manifest {

            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    private fun client(): PROGateServiceGrpcKt.PROGateServiceCoroutineStub {
        return if (isTls) {
            val credentials: ChannelCredentials = TlsChannelCredentials.newBuilder() //You can use your own certificate here .trustManager(new File("cert.pem"))
                .trustManager(InsecureTrustManagerFactory.INSTANCE.trustManagers[0])
                .build()
            val channel: ManagedChannel = Grpc.newChannelBuilderForAddress(server, port, credentials)
                .build()
            return PROGateServiceGrpcKt.PROGateServiceCoroutineStub(channel)
        } else {
            val channel = ManagedChannelBuilder.forTarget("${server}:${port}")
                .usePlaintext()
                .build()
            PROGateServiceGrpcKt.PROGateServiceCoroutineStub(channel)
        }
    }
}