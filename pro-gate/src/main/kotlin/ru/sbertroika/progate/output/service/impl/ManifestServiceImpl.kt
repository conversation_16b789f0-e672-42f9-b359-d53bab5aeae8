package ru.sbertroika.progate.output.service.impl

import arrow.core.Either
import arrow.core.computations.ResultEffect.bind
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import ru.sbertroika.common.manifest.v1.ManifestRequest
import ru.sbertroika.common.manifest.v1.core.constraint
import ru.sbertroika.common.manifest.v1.core.constraintException
import ru.sbertroika.common.manifest.v1.pro.*
import ru.sbertroika.common.v1.ConstraintBaseRule
import ru.sbertroika.common.v1.ConstraintType
import ru.sbertroika.common.v1.RouteScheme
import ru.sbertroika.common.v1.TransportType
import ru.sbertroika.pro.gate.v1.ManifestResult
import ru.sbertroika.pro.gate.v1.manifestResult
import ru.sbertroika.progate.output.repository.*
import ru.sbertroika.progate.output.service.AbtService
import ru.sbertroika.progate.output.service.ManifestService
import java.util.*

@Service
class ManifestServiceImpl(
    private val routeCrudRepository: RouteCrudRepository,
    private val routeStationRepository: RouteStationRepository,
    private val routeConstraintRepository: RouteConstraintRepository,
    private val routeConstraintExceptionRepository: RouteConstraintExceptionRepository,
    private val dispatchingRouteOrganizationRepository: DispatchingRouteOrganizationRepository,

    private val stationRepository: StationCrudRepository,

    private val productRepository: ProductCrudRepository,
    private val productDictRowRepository: ProductDictRowRepository,
    private val productDictRowTariffMatrixPriceRepository: ProductDictRowTariffMatrixPriceRepository,

    private val tariffRepository: TariffCrudRepository,
    private val tariffConstraintRepository: TariffConstraintRepository,
    private val tariffConstraintExceptionRepository: TariffConstraintExceptionRepository,

    private val vehicleRepository: VehicleCrudRepository,
    private val vehicleConstraintRepository: VehicleConstraintRepository,
    private val vehicleConstraintExceptionRepository: VehicleConstraintExceptionRepository,

    private val abtService: AbtService
) : ManifestService {

    override suspend fun getManifest(request: ManifestRequest): Either<Throwable, ManifestResult> = Either.catch {
        val projectId = UUID.fromString(request.projectId)

        manifestResult {
            manifestAbt = abtService.getManifest(request.projectId).bind()

            manifest = manifestPro {
                dict = manifestProDict {
                    route += routeCrudRepository.findAllByProjectId(projectId)
                        .map { rItem ->
                            route {
                                id = rItem.id.toString()
                                version = rItem.version!!
                                name = rItem.name!!
                                scheme = RouteScheme.valueOf(rItem.scheme!!.name)
                                station += routeStationRepository.findAllByRouteId(rItem.id!!)
                                    .map { rsItem ->
                                        routeStation {
                                            id = rsItem.stationId.toString()
                                            version = rsItem.version!!
                                            pos = rsItem.position!!
                                        }
                                    }.toList()
                                constraint += routeConstraintRepository.findAllByRouteId(rItem.id!!)
                                    .map { rcItem ->
                                        constraint {
                                            type = ConstraintType.valueOf(rcItem.type!!.name)
                                            baseRule = ConstraintBaseRule.valueOf(rcItem.baseRule!!.name)
                                            exception += routeConstraintExceptionRepository.findAllByRouteConstraintId(rcItem.id!!)
                                                .map { rceItem ->
                                                    constraintException {
                                                        id = rceItem.exceptionId.toString()
                                                    }
                                                }.toList()
                                        }
                                    }.toList()
                                routeIndex = rItem.index!!
                                number = rItem.number!!
                                dispatchingOrganization += dispatchingRouteOrganizationRepository.findAllByRouteId(rItem.id!!)
                                    .map { drItem ->
                                        dispatchingRouteOrganization {
                                            id = drItem.id.toString()
                                        }
                                    }.toList()
                            }
                        }.toList()

                    station += stationRepository.findAllByProjectId(projectId)
                        .map { item ->
                            station {
                                id = item.id.toString()
                                version = item.version!!
                                name = item.name!!
                                lat = item.latitude!!
                                lon = item.longitude!!
                            }
                        }.toList()

                    tariff += tariffRepository.findAllByProjectId(projectId)
                        .map { tItem ->
                            tariff {
                                id = tItem.id.toString()
                                version = tItem.version!!
                                name = tItem.name!!
                                constraint += tariffConstraintRepository.findAllByTariffId(tItem.id!!)
                                    .map { tcItem ->
                                        constraint {
                                            type = ConstraintType.valueOf(tcItem.type!!.name)
                                            baseRule = ConstraintBaseRule.valueOf(tcItem.baseRule!!.name)
                                            exception += tariffConstraintExceptionRepository.findAllByTariffConstraintId(tcItem.id!!)
                                                .map { tceItem ->
                                                    constraintException {
                                                        id = tceItem.exceptionId.toString()
                                                    }
                                                }.toList()
                                        }
                                    }.toList()
                            }
                        }.toList()

                    product += productRepository.findAll()
                        .map { pItem ->
                            product {
                                id = pItem.id.toString()
                                name = pItem.name.toString()
                                version = pItem.version!!
                            }
                        }.toList()

                    transport += vehicleRepository.findAllByProjectId(projectId)
                        .map { vItem ->
                            transport {
                                id = vItem.id.toString()
                                version = vItem.version!!
                                number = vItem.number ?: ""
                                type = TransportType.valueOf(vItem.type!!.name)
                                constraint += vehicleConstraintRepository.findAllByVehicleId(vItem.id!!)
                                    .map { vcItem ->
                                        constraint {
                                            type = ConstraintType.valueOf(vcItem.type!!.name)
                                            baseRule = ConstraintBaseRule.valueOf(vcItem.baseRule!!.name)
                                            exception += vehicleConstraintExceptionRepository.findAllByVehicleConstraintId(vcItem.id!!)
                                                .map { vceItem ->
                                                    constraintException {
                                                        id = vceItem.exceptionId.toString()
                                                    }
                                                }.toList()
                                        }
                                    }.toList()
                            }
                        }.toList()

                    menu += productDictRowRepository.findAllByProjectId(projectId).toList()
                        .groupByTo(mutableMapOf()) { "${it.productId}-${it.tariffId}" }
                        .map { group ->
                            val first = group.value.first()
                            productMenu {
                                productId = first.productId.toString()
                                tariffId = first.tariffId.toString()
                                version = first.version!!
                                priceRules += group.value.map { row ->
                                    priceRule {
                                        paymentType = PriceRule.TPaymentType.valueOf(row.paymentMethodType!!.name)
                                        version = row.version!!
                                        if (row.isFixPrice == true) {
                                            price = row.price!!.toInt()
                                        } else {
                                            matrix += productDictRowTariffMatrixPriceRepository.findAllByProductDicRowId(row.id!!)
                                                .map { m ->
                                                    priceRuleMatrixItem {
                                                        stationFrom = m.stationFromId.toString()
                                                        stationTo = m.stationToId.toString()
                                                        price = m.amount!!.toInt()
                                                        version = m.version!!
                                                    }
                                                }.toList()
                                        }
                                    }
                                }.toList()
                            }
                        }.toList()
                }
            }
        }
    }
}