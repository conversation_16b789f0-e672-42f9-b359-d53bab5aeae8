package ru.sbertroika.progate.output.service.impl

import arrow.core.Either
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.abt.gateway.v1.AbtGatewayServiceGrpcKt
import ru.sbertroika.common.manifest.v1.manifestRequest
import ru.sbertroika.common.manifest.v1.pro.ManifestProAbt
import ru.sbertroika.progate.output.service.AbtService
import java.util.concurrent.TimeUnit

@Service
class AbtServiceImpl(
    @Value("\${abt_gateway_url}")
    private val gateUrl: String
) : AbtService {

    private final val channel = ManagedChannelBuilder.forTarget(gateUrl)
        .usePlaintext()
        .idleTimeout(60000, TimeUnit.MILLISECONDS)
        .keepAliveTimeout(60000, TimeUnit.MILLISECONDS)
        .enableRetry()
        .maxRetryAttempts(3)
        .build()

    val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)

    override suspend fun getManifest(projectId: String): Either<Throwable, ManifestProAbt> = Either.catch {
        val response = client.getManifest(
            manifestRequest {
                this.projectId = projectId
            }
        )

        if (response.hasError()) throw Error(response.error.message)

        response.manifest
    }
}