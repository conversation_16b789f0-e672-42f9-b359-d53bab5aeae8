package ru.sbertroika.progate.input

import com.google.protobuf.Empty
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.lognet.springboot.grpc.GRpcService
import org.springframework.beans.factory.annotation.Value
import ru.sbertroika.common.manifest.v1.ManifestRequest
import ru.sbertroika.common.pro.*
import ru.sbertroika.common.toOperationError
import ru.sbertroika.common.toTimestamp
import ru.sbertroika.common.v1.ConstraintBaseRule
import ru.sbertroika.common.v1.ConstraintType
import ru.sbertroika.common.v1.RouteScheme
import ru.sbertroika.common.v1.TransportType
import ru.sbertroika.pro.gate.v1.*
import ru.sbertroika.common.pro.routeStation
import ru.sbertroika.progate.output.repository.*
import ru.sbertroika.progate.output.service.ManifestService
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@GRpcService
class PROGateServiceGrpc(
    @Value("\${project_id}")
    private val projectId: String,
    @Value("\${project_index}")
    private val projectNumber: Int,
    @Value("\${org_id}")
    private val orgId: String,

    private val stationRepository: StationRepository,
    private val tariffRepository: TariffRepository,
    private val tariffConstraintRepository: TariffConstraintRepository,
    private val tariffConstraintExceptionRepository: TariffConstraintExceptionRepository,

    private val routeRepository: RouteRepository,
    private val routeConstraintRepository: RouteConstraintRepository,
    private val routeConstraintExceptionRepository: RouteConstraintExceptionRepository,
    private val routeStationRepository: RouteStationRepository,

    private val productRepository: ProductRepository,
    private val productDictRowRepository: ProductDictRowRepository,
    private val productDictRowTariffMatrixPriceRepository: ProductDictRowTariffMatrixPriceRepository,

    private val vehicleRepository: VehicleRepository,
    private val vehicleConstraintRepository: VehicleConstraintRepository,
    private val vehicleConstraintExceptionRepository: VehicleConstraintExceptionRepository,

    private val manifestService: ManifestService
) : PROGateServiceGrpcKt.PROGateServiceCoroutineImplBase() {

    companion object {
        private val dfmt = DateTimeFormatter.ofPattern("yyyyMMdd")
    }

    override suspend fun getManifestByProject(request: ManifestRequest): ManifestByProjectResponse {
        return manifestService.getManifest(request).fold(
            {
                manifestByProjectResponse {
                    error = toOperationError(Error(it))
                }
            },
            { res ->
                manifestByProjectResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun getManifest(request: Empty): ManifestResponse {
        val dateStart = LocalDate.now(ZoneId.of("UTC"))
        return manifestResponse {
            manifest = manifest {
                id = "$projectId-${dateStart.format(dfmt)}"
                version = 1
                validFrom = dateStart.atStartOfDay(ZoneId.of("UTC")).toTimestamp()
                validTill = dateStart.plusDays(7).atStartOfDay(ZoneId.of("UTC")).minusSeconds(1).toTimestamp()
                projectIndex = projectNumber
                organization = organization {
                    id = orgId
                    name = "ООО Орбита XXI век"
                    inn = "7726283584"
                    address = "115598, город Москва, Ягодная ул., д. 8 к. 3, кв. 214"
                    paymentPlace = "Разъездная торговля"
                }
            }
        }
    }

    override suspend fun getStationList(request: Manifest): StationListResponse {
        return stationListResponse {
            result = stationListResult {
                station += stationRepository.findAll()
                    .map { item ->
                        station {
                            id = item.id.toString()
                            name = item.name!!
                            lat = item.latitude!!
                            lon = item.longitude!!
                        }
                    }.toList()
            }
        }
    }

    override suspend fun getTariffList(request: Manifest): TariffListResponse {
        return tariffListResponse {
            result = tariffListResult {
                tariff += tariffRepository.findAll()
                    .map { tItem ->
                        tariff {
                            id = tItem.id.toString()
                            name = tItem.name!!
                            constrain += tariffConstraintRepository.findAllByTariffId(tItem.id!!)
                                .map { tcItem ->
                                    constraint {
                                        type = ConstraintType.valueOf(tcItem.type!!.name)
                                        baseRule = ConstraintBaseRule.valueOf(tcItem.baseRule!!.name)
                                        exception += tariffConstraintExceptionRepository.findAllByTariffConstraintId(tcItem.id!!)
                                            .map { tceItem ->
                                                constraintException {
                                                    id = tceItem.exceptionId.toString()
                                                }
                                            }.toList()
                                    }
                                }.toList()
                        }
                    }.toList()
            }
        }
    }

    override suspend fun getRouteList(request: Manifest): RouteListResponse {
        return routeListResponse {
            result = routeListResult {
                route += routeRepository.findAll()
                    .map { rItem ->
                        route {
                            id = rItem.id.toString()
                            name = rItem.name!!
                            scheme = RouteScheme.valueOf(rItem.scheme!!.name)
                            station += routeStationRepository.findAllByRouteId(rItem.id!!)
                                .map { rsItem ->
                                    ru.sbertroika.common.pro.routeStation {
                                        id = rsItem.stationId.toString()
                                        pos = rsItem.position!!
                                    }
                                }.toList()
                            constrain += routeConstraintRepository.findAllByRouteId(rItem.id!!)
                                .map { rcItem ->
                                    constraint {
                                        type = ConstraintType.valueOf(rcItem.type!!.name)
                                        baseRule = ConstraintBaseRule.valueOf(rcItem.baseRule!!.name)
                                        exception += routeConstraintExceptionRepository.findAllByRouteConstraintId(rcItem.id!!)
                                            .map { rceItem ->
                                                constraintException {
                                                    id = rceItem.exceptionId.toString()
                                                }
                                            }.toList()
                                    }
                                }.toList()
                            routeIndex = rItem.index!!
                            number = rItem.number!!
                        }
                    }.toList()
            }
        }
    }

    override suspend fun getProductList(request: Manifest): ProductListResponse {
        return productListResponse {
            result = productListResult {
                product += productRepository.findAll()
                    .map { pItem ->
                        product {
                            id = pItem.id.toString()
                            name = pItem.name.toString()
                        }
                    }.toList()
            }
        }
    }

    override suspend fun getTransportList(request: Manifest): TransportListResponse {
        return transportListResponse {
            result = transportListResult {
                transport += vehicleRepository.findAll()
                    .map { vItem ->
                        transport {
                            id = vItem.id.toString()
                            number = vItem.number ?: ""
                            type = TransportType.valueOf(vItem.type!!.name)
                            constrain += vehicleConstraintRepository.findAllByVehicleId(vItem.id!!)
                                .map { vcItem ->
                                    constraint {
                                        type = ConstraintType.valueOf(vcItem.type!!.name)
                                        baseRule = ConstraintBaseRule.valueOf(vcItem.baseRule!!.name)
                                        exception += vehicleConstraintExceptionRepository.findAllByVehicleConstraintId(vcItem.id!!)
                                            .map { vceItem ->
                                                constraintException {
                                                    id = vceItem.exceptionId.toString()
                                                }
                                            }.toList()
                                    }
                                }.toList()
                        }
                    }.toList()
            }
        }
    }

    override suspend fun getProductMenu(request: Manifest): ProductMenuResponse {
        return productMenuResponse {
            result = productMenuResult {
                menu += productDictRowRepository.findAll().toList()
                    .groupByTo(mutableMapOf()) { "${it.productId}-${it.tariffId}" }
                    .map { group ->
                        val first = group.value.first()
                        productMenu {
                            productId = first.productId.toString()
                            tariffId = first.tariffId.toString()
                            priceRules += group.value.map { row ->
                                priceRule {
                                    paymentType = PriceRule.TPaymentType.valueOf(row.paymentMethodType!!.name)
                                    if (row.isFixPrice == true) {
                                        price = row.price!!.toInt()
                                    } else {
                                        matrix += productDictRowTariffMatrixPriceRepository.findAllByProductDicRowId(row.id!!)
                                            .map { m ->
                                                priceRuleMatrixItem {
                                                    stationFrom = m.stationFromId.toString()
                                                    stationTo = m.stationToId.toString()
                                                    price = m.amount!!.toInt()
                                                }
                                            }.toList()
                                    }
                                }
                            }.toList()
                        }
                    }.toList()
            }
        }
    }
}