package ru.sbertroika.progate.config

import org.springframework.data.convert.WritingConverter
import org.springframework.data.r2dbc.convert.EnumWriteSupport
import ru.sbertroika.tkp3.pro.model.*


@WritingConverter
class ProductStatusConverter: EnumWriteSupport<ProductStatus>()

@WritingConverter
class TariffStatusConverter: EnumWriteSupport<TariffStatus>()

@WritingConverter
class RouteStatusConverter: EnumWriteSupport<RouteStatus>()

@WritingConverter
class StationStatusConverter: EnumWriteSupport<StationStatus>()

@WritingConverter
class VehicleStatusConverter: EnumWriteSupport<VehicleStatus>()

@WritingConverter
class VehicleTypeConverter: EnumWriteSupport<VehicleType>()

@WritingConverter
class RouteSchemeTypeConverter: EnumWriteSupport<RouteScheme>()