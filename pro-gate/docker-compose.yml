version: '3.7'

services:
  pro_gate:
    build: .
    container_name: pro_gate
    ports:
      - 5005:5000
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      - DB_URL=postgresql://pro_gate_db/pro
  pro_gate_db:
    image: postgres:14
    container_name: pro_gate_db
    restart: always
    ports:
      - 5432:5432
    volumes:
      - ./data:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: pro
      PGDATA: /var/lib/postgresql/data/pgdata

  postgresql:
    image: docker.io/bitnami/postgresql:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - POSTGRESQL_USERNAME=bn_keycloak
      - POSTGRESQL_DATABASE=bitnami_keycloak
    networks:
      - keycloak_net

  keycloak:
    image: docker.io/bitnami/keycloak:latest
    ports:
      - "8080:8080"
    environment:
      - KEYCLOAK_CREATE_ADMIN_USER=true
      - K<PERSON>YCLOAK_ADMIN_USER=user
      - KEYCLOAK_ADMIN_PASSWORD=12345
    depends_on:
      - postgresql
    networks:
      - keycloak_net

networks:
  keycloak_net: