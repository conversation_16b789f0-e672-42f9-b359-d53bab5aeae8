rootProject.name = "pro"

include(
    ":pro-api",
    ":pro-gate-api",
    ":pro-gate-private-api",
    ":pro-model",
    ":pro-common",
    ":pro-migrations",
    ":pro-gate",
    ":pro-gate-private",
    ":pro-processing",
    ":pro-ui",
    ":pro-controller",

    ":manifest-model",
    ":manifest-starter",
    ":pro-manifest-processing",
)

dependencyResolutionManagement {
    repositories {
        gradlePluginPortal()
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }

    versionCatalogs {
        create("libs") {
            from("ru.sbertroika.sharedcatalog:gradle-common:1.3")
        }
    }
}