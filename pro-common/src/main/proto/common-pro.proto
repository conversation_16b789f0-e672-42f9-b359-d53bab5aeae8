syntax = "proto3";

package ru.sbertroika.common.pro;

import "google/protobuf/empty.proto";
import "common.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.pro";


message ConstraintException {
  string id = 1;
}

message Constraint {
  common.v1.ConstraintType type = 1;
  common.v1.ConstraintBaseRule baseRule = 2;
  repeated ConstraintException exception = 3;
}

message Station {
  optional string id = 1;
  string name = 2;
  double lat = 3;
  double lon = 4;
  optional string tags = 5;
  optional string projectId = 6;
  optional string tariffZoneId = 7;
  common.v1.ModelStatus status = 8;
}

message StationListResult {
  repeated Station station = 1;
  optional common.v1.PaginationResponse pagination = 2;
}

message StationListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    StationListResult result = 2;
  }
}

message Tariff {
  optional string id = 1;
  string name = 2;
  repeated Constraint constrain = 3;
  optional string tags = 4;
  common.v1.ModelStatus status = 5;
  optional string projectId = 6;
}

message TariffListResult {
  repeated Tariff tariff = 1;
  optional common.v1.PaginationResponse pagination = 2;
}

message TariffListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TariffListResult result = 2;
  }
}


message RouteStation {
  optional string id = 1;
  uint32 pos = 2;
}

message Route {
  optional string id = 1;
  string name = 2;
  common.v1.RouteScheme scheme = 3;
  repeated RouteStation station = 4;
  repeated Constraint constrain = 5;
  uint32 routeIndex = 6;
  string number = 7;
  common.v1.ModelStatus status = 8;
  optional string tags = 9;
  optional string orgId = 10;
  optional string projectId = 11;
}

message RouteListResult {
  repeated Route route = 1;
  optional common.v1.PaginationResponse pagination = 2;
}

message RouteListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    RouteListResult result = 2;
  }
}


message Product {
  optional string id = 1;
  string name = 2;
  optional string tags = 3;
  common.v1.ModelStatus status = 4;
  optional string projectId = 5;
}

message ProductListResult {
  repeated Product product = 1;
  optional common.v1.PaginationResponse pagination = 2;
}

message ProductListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    ProductListResult result = 2;
  }
}


message Transport {
  optional string id = 1;
  string number = 2;
  common.v1.TransportType type = 3;
  repeated Constraint constrain = 4;
  optional string tags = 5;
  common.v1.ModelStatus status = 6;
  optional string projectId = 7;
  optional string orgId = 8;
}

message TransportListResult {
  repeated Transport transport = 1;
  optional common.v1.PaginationResponse pagination = 2;
}

message TransportListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TransportListResult result = 2;
  }
}

