plugins {
    idea
    `java-library`
    `maven-publish`
}

group = "ru.sbertroika.pro"
version = "1.0.0"

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
}

dependencies {
    // Flyway для миграций
    api("org.flywaydb:flyway-core:11.10.5")
}

// Настройка публикации артефакта
publishing {
    publications {
        create<MavenPublication>("maven") {
            from(components["java"])
            
            pom {
                name.set("Pro Domain Migrations")
                description.set("Database migrations for Pro Domain")
            }
        }
    }
}

// Настройка JAR для включения миграций
tasks.jar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
