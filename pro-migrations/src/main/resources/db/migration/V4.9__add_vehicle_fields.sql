-- Изменение типов vh_type и vh_status с enum на varchar(200)
-- Версия миграции: 4.9

-- Изменение типа vh_type с enum на varchar(200)
DO $$
BEGIN
    -- Создаем временную колонку с новым типом
    ALTER TABLE vehicle ADD COLUMN vh_type_new VARCHAR(200);
    -- Копируем данные из старой колонки в новую
    UPDATE vehicle SET vh_type_new = vh_type::text;
    -- Удаляем старую колонку
    ALTER TABLE vehicle DROP COLUMN vh_type;
    -- Переименовываем новую колонку
    ALTER TABLE vehicle RENAME COLUMN vh_type_new TO vh_type;
    -- Удаляем старый тип enum, если он больше нигде не используется
    DROP TYPE IF EXISTS vehicle_type;
END $$;

-- Изменение типа vh_status с enum на varchar(200)
DO $$
BEGIN
    -- Создаем временную колонку с новым типом
    ALTER TABLE vehicle ADD COLUMN vh_status_new VARCHAR(200);
    -- Копируем данные из старой колонки в новую
    UPDATE vehicle SET vh_status_new = vh_status::text;
    -- Удаляем старую колонку
    ALTER TABLE vehicle DROP COLUMN vh_status;
    -- Переименовываем новую колонку
    ALTER TABLE vehicle RENAME COLUMN vh_status_new TO vh_status;
    -- Удаляем старый тип enum, если он больше нигде не используется
    DROP TYPE IF EXISTS vehicle_status;
END $$;

-- Добавляем комментарии к колонкам
COMMENT ON COLUMN vehicle.vh_type IS 'Тип транспортного средства (BUS, TRAM, TROLLEYBUS, METRO)';
COMMENT ON COLUMN vehicle.vh_status IS 'Статус транспортного средства (ACTIVE, DISABLED, BLOCKED, IS_DELETED)';

-- Добавляем индексы для оптимизации запросов
CREATE INDEX idx_vehicle_status ON vehicle(vh_status);
CREATE INDEX idx_vehicle_type ON vehicle(vh_type); 