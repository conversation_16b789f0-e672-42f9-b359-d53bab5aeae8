CREATE TABLE employee(
    e_profile_id                  uuid                                       not null,
    e_version                     int              default 1                 not null,
    PRIMARY KEY (e_profile_id, e_version),
    e_version_created_at          TIMESTAMP        default now()             not null,
    e_version_created_by          uuid,
    e_organization_id             varchar(32)                                not null,
    e_role                        varchar(32)                                not null,
    e_surname                     varchar(64)                                not null,
    e_name                        varchar(64)                                not null,
    e_middle_name                 varchar(64)      default null,
    e_phone                       varchar(32)      default null,
    e_email                       varchar(64)      default null,
    e_birth_day                   TIMESTAMP        default null,
    e_series_and_number_passport  varchar(64)      default null,
    e_issue_date_passport         TIMESTAMP        default null,
    e_gender                      boolean,
    e_photo_id                    varchar(32)      default null,
    e_language                    varchar(32)      default null,
    e_personal_number             varchar(32)      default null,
    e_pin_hash                    varchar(128)     default null,
    e_enabled                     boolean          default true,
    e_login                       varchar(64)      default null,
    e_is_deleted                  boolean          default false               not null,
    e_password                    varchar(64)      default null

);
CREATE INDEX idx_organization_id_in_employee ON employee (e_organization_id);

CREATE TABLE role(
    r_id                          uuid          default gen_random_uuid()    not null,
    r_name                        varchar(32)                                not null,
    r_description                 varchar(32)                                not null
);

INSERT INTO role(r_name, r_description)
    VALUES
        ('terminal_user_admin', 'Администратор'),
        ('terminal_user_cashier', 'Кассир'),
        ('terminal_user_collector', 'Инкассатор'),
        ('terminal_user_conductor', 'Кондуктор'),
        ('terminal_user_controler', 'Контролер'),
        ('terminal_user_driver', 'Водитель'),
        ('tkp3_super_admin', 'Супер Админ'),
        ('tkp3_admin', 'Админ'),
        ('tkp3_operator', 'Оператор'),
        ('tkp3_carrier', 'Перевозчик');
