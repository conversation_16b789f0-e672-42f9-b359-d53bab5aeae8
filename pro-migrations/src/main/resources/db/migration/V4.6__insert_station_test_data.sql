-- Вставка тестовых данных для станций
INSERT INTO station (
    st_id, st_version, st_version_created_at, st_version_created_by, p_id, tz_id,
    st_name, st_latin_name, st_status, lat, long, st_city_id, st_district_id, st_region_id, st_country_id,
    tags, st_last_sync_date, st_sync_status
) VALUES 
-- Станции для проекта 1 (Основной проект)
(
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'e4647a84-8dd9-4e36-a800-30000b9bb1d8', '11111111-1111-1111-1111-111111111111',
    'Центральный автовокзал', 'Central Bus Station', 'ACTIVE'::station_status,
    55.7558, 37.6176, '550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440001',
    '["автобус", "центральный"]', NOW(), 'synced'
),
(
    'b2c3d4e5-f6a7-8901-bcde-f23456789012', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'e4647a84-8dd9-4e36-a800-30000b9bb1d8', '11111111-1111-1111-1111-111111111111',
    'Площадь Революции', 'Revolution Square', 'ACTIVE'::station_status,
    55.7567, 37.6231, '550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440032', '550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440001',
    '["метро", "площадь"]', NOW(), 'synced'
),
(
    'c3d4e5f6-a7b8-9012-cdef-345678901234', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'e4647a84-8dd9-4e36-a800-30000b9bb1d8', '11111111-1111-1111-1111-111111111111',
    'Красная площадь', 'Red Square', 'ACTIVE'::station_status,
    NULL, NULL, '550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440032', '550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440001',
    '["исторический", "площадь"]', NOW(), 'synced'
),

-- Станции для проекта 2 (Метрополитен)
(
    'd4e5f6a7-b8c9-0123-defa-456789012345', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'a2b3c4d5-e6f7-8901-bcde-f23456789012', '11111111-1111-1111-1111-111111111111',
    'Невский проспект', 'Nevsky Prospect', 'ACTIVE'::station_status,
    59.9311, 30.3609, '550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440033', '550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440001',
    '["метро", "проспект"]', NOW(), 'synced'
),
(
    'e5f6a7b8-c9d0-1234-efab-567890123456', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'a2b3c4d5-e6f7-8901-bcde-f23456789012', '11111111-1111-1111-1111-111111111111',
    'Дворцовая площадь', 'Palace Square', 'ACTIVE'::station_status,
    59.9387, 30.3162, '550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440033', '550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440001',
    '["исторический", "площадь"]', NOW(), 'synced'
),

-- Станции для проекта 3 (Автобусы)
(
    'f6a7b8c9-d0e1-2345-fabc-678901234567', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'b3c4d5e6-f7a8-9012-cdef-345678901234', '11111111-1111-1111-1111-111111111111',
    'Площадь Ленина', 'Lenin Square', 'ACTIVE'::station_status,
    NULL, NULL, '550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440034', '550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-446655440001',
    '["автобус", "площадь"]', NOW(), 'synced'
),
(
    'a7b8c9d0-e1f2-3456-abcd-789012345678', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'b3c4d5e6-f7a8-9012-cdef-345678901234', '11111111-1111-1111-1111-111111111111',
    'Театральная площадь', 'Theatre Square', 'ACTIVE'::station_status,
    55.0415, 82.9346, '550e8400-e29b-41d4-a716-446655440024', '550e8400-e29b-41d4-a716-446655440035', '550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-446655440001',
    '["автобус", "культурный"]', NOW(), 'synced'
),

-- Дополнительные станции для других проектов
(
    'b8c9d0e1-f2a3-4567-bcde-890123456789', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'c4d5e6f7-a8b9-0123-defa-456789012345', '11111111-1111-1111-1111-111111111111',
    'Вокзальная площадь', 'Station Square', 'ACTIVE'::station_status,
    55.7887, 49.1221, '550e8400-e29b-41d4-a716-446655440025', '550e8400-e29b-41d4-a716-446655440036', '550e8400-e29b-41d4-a716-446655440015', '550e8400-e29b-41d4-a716-446655440001',
    '["железнодорожный", "вокзал"]', NOW(), 'synced'
),
(
    'c9d0e1f2-a3b4-5678-cdef-901234567890', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'd5e6f7a8-b9c0-1234-efab-567890123456', '11111111-1111-1111-1111-111111111111',
    'Площадь Свободы', 'Freedom Square', 'ACTIVE'::station_status,
    56.3287, 44.0020, '550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440034', '550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-446655440001',
    '["автобус", "площадь"]', NOW(), 'synced'
),
(
    'd0e1f2a3-b4c5-6789-defa-012345678901', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'e6f7a8b9-c0d1-2345-fabc-678901234567', '11111111-1111-1111-1111-111111111111',
    'Центральная площадь', 'Central Square', 'ACTIVE'::station_status,
    NULL, NULL, '550e8400-e29b-41d4-a716-446655440024', '550e8400-e29b-41d4-a716-446655440035', '550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-446655440001',
    '["автобус", "центральный"]', NOW(), 'synced'
),
(
    'e1f2a3b4-c5d6-7890-efab-123456789012', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'f7a8b9c0-d1e2-3456-abcd-789012345678', '11111111-1111-1111-1111-111111111111',
    'Привокзальная площадь', 'Railway Station Square', 'ACTIVE'::station_status,
    48.7194, 44.5018, '550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440033', '550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440001',
    '["железнодорожный", "вокзал"]', NOW(), 'synced'
),
(
    'f2a3b4c5-d6e7-8901-fabc-234567890123', 1, NOW(), '11111111-1111-1111-1111-111111111111',
    'a8b9c0d1-e2f3-4567-bcde-890123456789', '11111111-1111-1111-1111-111111111111',
    'Площадь Победы', 'Victory Square', 'ACTIVE'::station_status,
    51.6605, 39.2006, '550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440001',
    '["автобус", "исторический"]', NOW(), 'synced'
); 