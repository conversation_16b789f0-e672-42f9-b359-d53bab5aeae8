alter table public.product
    add p_project_id uuid;

update public.product set p_project_id = '43db9de7-72ec-4eae-8978-8aef1c46873a' where 1=1;

alter table public.product
    alter column p_project_id set not null;

comment on column public.product.p_project_id is 'Идентификатор проекта';

alter table public.tariff
    add t_project_id uuid;

update public.tariff set t_project_id = '43db9de7-72ec-4eae-8978-8aef1c46873a' where 1=1;

alter table public.tariff
    alter column t_project_id set not null;

comment on column public.tariff.t_project_id is 'Идентификатор проекта';

alter table public.route
    add r_project_id uuid;

update public.route set r_project_id = '43db9de7-72ec-4eae-8978-8aef1c46873a' where 1=1;

alter table public.route
    alter column r_project_id set not null;

comment on column public.route.r_project_id is 'Идентификатор проекта';

alter table public.route
    drop column r_org_id;

alter table public.vehicle
    add v_project_id uuid,
    add v_organization_id uuid;

update public.vehicle set v_project_id = '43db9de7-72ec-4eae-8978-8aef1c46873a' where 1=1;
update public.vehicle set v_organization_id = '43db9de7-72ec-4eae-8978-00008aef1c46' where 1=1;

alter table public.vehicle
    alter column v_project_id set not null;
alter table public.vehicle
    alter column v_organization_id set not null;

comment on column public.vehicle.v_project_id is 'Идентификатор проекта';

drop table public.pay_method;
drop type if exists pay_method_type;
create type pay_method_type as enum ('CASH', 'EMV', 'TROIKA_TICKET', 'TROIKA_WALLET', 'ABT_TICKET', 'ABT_WALLET');

alter table public.product_dict_row
    add pdr_method_type pay_method_type;

alter table public.product_dict_row
    add pdr_project_id uuid;

update public.product_dict_row set pdr_project_id = '43db9de7-72ec-4eae-8978-8aef1c46873a' where 1=1;

alter table public.product_dict_row
    alter column pdr_project_id set not null;

comment on column public.product_dict_row.pdr_project_id is 'Идентификатор проекта';

update public.product_dict_row set pdr_method_type = 'CASH'::pay_method_type where pm_id='7a83cb06-bafa-4a89-9355-fe82ea60e2ff';
update public.product_dict_row set pdr_method_type = 'EMV'::pay_method_type where pm_id='eabd1462-7608-4d10-89dc-d16f566df9da';
update public.product_dict_row set pdr_method_type = 'TROIKA_WALLET'::pay_method_type where pm_id='3a28f211-1ee7-4f8b-9cba-1a24a8e72000';
update public.product_dict_row set pdr_method_type = 'TROIKA_WALLET'::pay_method_type where pm_id='1508b258-2ee7-4faa-82e2-7a8f07865acd';

alter table public.product_dict_row
    drop column pm_id;

--
CREATE TYPE dispatching_route_organization_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');

CREATE TABLE public.dispatching_route_organization
(
    ro_id                 uuid      default gen_random_uuid() not null,
    ro_version            integer   default 1                 not null,
    ro_version_created_at timestamp default now()             not null,
    ro_version_created_by uuid                                not null,
    ro_project_id         uuid                                not null,
    ro_route_id           uuid                                not null,
    ro_active_from        timestamp default now()             not null,
    ro_active_till        timestamp,
    ro_status             dispatching_route_organization_status           not null,
    primary key (ro_id, ro_version)
);
