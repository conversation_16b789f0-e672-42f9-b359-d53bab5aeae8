-- Вставка тестовых данных проектов
INSERT INTO public.project (
    pr_id, pr_version, pr_version_created_at, pr_version_created_by,
    pr_name, pr_code, pr_project_type, pr_status, pr_start_date, pr_end_date,
    pr_contract_id, pr_contract_number, pr_contract_name, pr_description,
    pr_total_budget, pr_spent_budget, pr_progress, pr_manager,
    pr_last_sync_date, pr_sync_status
) VALUES 
(
    'a223116c-49b4-43eb-8a47-c76a68a4225d', 1, now(), gen_random_uuid(),
    'СберТройка ПРО', 'ST-PRO-MSK-001', 'transport_system', 'ACTIVE', '2024-01-01', '2025-12-31',
    gen_random_uuid(), 'СТ-ПРО-2024-001', 'Правила системы СберТройка ПРО г. Москва',
    'Комплексная система транспортных карт и платежей для г. Москва',
    25000000, 12500000, 75, 'Транспортов Транспорт Транспортович',
    '2024-01-20 14:30:00', 'synced'
),
(
    'ea94a83e-e361-450f-9b5a-d55fb6018d2c', 1, now(), gen_random_uuid(),
    'СберТройка ПРО г. Москва', 'ST-PRO-MSK-001', 'transport_system', 'ACTIVE', '2024-01-01', '2025-12-31',
    gen_random_uuid(), 'СТ-ПРО-2024-001', 'Правила системы СберТройка ПРО г. Москва',
    'Комплексная система транспортных карт и платежей для г. Москва',
    25000000, 12500000, 75, 'Транспортов Транспорт Транспортович',
    '2024-01-20 14:30:00', 'synced'
),
(
    '8d0afe3f-2383-4704-9b65-123a1c9d54c9', 1, now(), gen_random_uuid(),
    'СберТройка ПРО Метрополитен', 'ST-PRO-METRO-001', 'metro_system', 'ACTIVE', '2024-01-15', '2025-12-31',
    gen_random_uuid(), 'СТ-ПРО-2024-002', 'Правила системы СберТройка ПРО Метрополитен',
    'Система оплаты для метрополитена',
    18000000, 9500000, 65, 'Метров Станция Подземная',
    '2024-01-20 14:25:00', 'synced'
),
(
    '78f847de-df47-4584-9a69-10182238f852', 1, now(), gen_random_uuid(),
    'СберТройка ПРО Автобусы', 'ST-PRO-BUS-001', 'bus_system', 'EXPIRING', '2023-06-01', '2024-05-31',
    gen_random_uuid(), 'СТ-ПРО-2023-045', 'Правила системы СберТройка ПРО Автобусы',
    'Автобусная система оплаты',
    12000000, 11200000, 95, 'Автобусов Маршрут Городской',
    '2024-01-19 16:45:00', 'synced'
),
(
    'b99d4d3f-6a2a-4cdb-9fd6-2203590fed73', 1, now(), gen_random_uuid(),
    'СберТройка ПРО Инновации', 'ST-PRO-INNOV-001', 'innovation_system', 'DRAFT', '2024-03-01', '2025-02-28',
    gen_random_uuid(), 'СТ-ПРО-2024-004', 'Договор на внедрение инноваций',
    'Пилотный проект инновационных решений',
    8000000, 0, 5, 'Инновационов Проект Технологический',
    null, 'pending'
),
(
    'e4647a84-8dd9-4e36-a800-30000b9bb1d8', 1, now(), gen_random_uuid(),
    'СберТройка ПРО Троллейбусы', 'ST-PRO-TROLL-001', 'transport_system', 'ACTIVE', '2023-09-01', '2024-08-31',
    gen_random_uuid(), 'СТ-ПРО-2023-055', 'Правила системы СберТройка ПРО Троллейбусы',
    'Троллейбусная система оплаты',
    9500000, 7200000, 85, 'Троллейбусов Электрический Токовый',
    '2024-01-20 12:15:00', 'synced'
),
(
    '1ce2b0dd-2c5f-4c68-b09b-7b6756827d10', 1, now(), gen_random_uuid(),
    'СберТройка ПРО Пригород', 'ST-PRO-SUBURB-001', 'transport_system', 'ACTIVE', '2024-02-01', '2025-01-31',
    gen_random_uuid(), 'СТ-ПРО-2024-006', 'Договор пригородного сообщения',
    'Пригородные маршруты',
    22000000, 5500000, 25, 'Пригородов Дальний Маршрутный',
    '2024-01-20 16:30:00', 'synced'
),
(
    '027338e1-1985-437f-9473-e2de44002ff8', 1, now(), gen_random_uuid(),
    'СберТройка ПРО Экспресс', 'ST-PRO-EXPRESS-001', 'transport_system', 'COMPLETED', '2023-01-01', '2023-12-31',
    gen_random_uuid(), 'СТ-ПРО-2023-007', 'Договор экспресс сообщения',
    'Экспресс маршруты',
    15000000, 15000000, 100, 'Экспрессов Быстрый Скоростной',
    '2024-01-01 00:00:00', 'synced'
),
(
    '0ed6022b-4ea0-4357-8273-3593a7358cdf', 1, now(), gen_random_uuid(),
    'СберТройка ПРО Речной транспорт', 'ST-PRO-RIVER-001', 'transport_system', 'DRAFT', '2024-05-01', '2025-04-30',
    gen_random_uuid(), 'СТ-ПРО-2024-008', 'Договор речного сообщения',
    'Речные перевозки',
    12000000, 0, 0, 'Речников Водный Плавательный',
    null, 'pending'
)
ON CONFLICT (pr_id, pr_version) DO NOTHING;

-- Вставка данных операторских организаций
INSERT INTO public.project_operator (
    po_project_id, po_project_version, po_operator_id, po_operator_name, po_operator_role
) VALUES 
(
    'a223116c-49b4-43eb-8a47-c76a68a4225d', 1,
    '65bab8b6-5728-4823-b5b6-b5e1efaf1fbf',
    'ООО "СберТройка"',
    'operator'
),
(
    'ea94a83e-e361-450f-9b5a-d55fb6018d2c', 1,
    '7dbf12e7-a707-4d69-a2dc-42b8af0574ce',
    'ООО "Городской транспорт"',
    'operator'
),
(
    '8d0afe3f-2383-4704-9b65-123a1c9d54c9', 1,
    'fd70d52a-8286-4c96-aad8-d38cf91d55b8',
    'АО "Метрополитен"',
    'operator'
),
(
    '78f847de-df47-4584-9a69-10182238f852', 1,
    '32f92886-9c71-4f34-8f92-510a15f57684',
    'ООО "Автобусный парк №1"',
    'operator'
),
(
    'b99d4d3f-6a2a-4cdb-9fd6-2203590fed73', 1,
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'ООО "Инновационные решения"',
    'coordinator'
),
(
    'e4647a84-8dd9-4e36-a800-30000b9bb1d8', 1,
    'b2c3d4e5-a6b7-8901-bcde-f23456789012',
    'ООО "Троллейбусное управление"',
    'operator'
),
(
    '1ce2b0dd-2c5f-4c68-b09b-7b6756827d10', 1,
    'c3d4e5f6-a7b8-9012-cdef-345678901234',
    'ООО "Пригородные перевозки"',
    'operator'
),
(
    '027338e1-1985-437f-9473-e2de44002ff8', 1,
    'd4e5f6a7-b8c9-0123-defa-456789012345',
    'ООО "Экспресс маршруты"',
    'operator'
),
(
    '0ed6022b-4ea0-4357-8273-3593a7358cdf', 1,
    'e5f6a7b8-c9d0-1234-efab-567890123456',
    'ООО "Речные перевозки"',
    'coordinator'
)
ON CONFLICT (po_id) DO NOTHING;

-- Вставка данных участников организаций
INSERT INTO public.participant_organization (
    po_project_id, po_project_version, po_organization_id, po_role
) VALUES 
(
    'a223116c-49b4-43eb-8a47-c76a68a4225d', 1,
    'a6b7c8d9-e0f1-2345-abcd-678901234567',
    'operator'
),
(
    'a223116c-49b4-43eb-8a47-c76a68a4225d', 1,
    'b7c8d9e0-f1a2-3456-bcde-789012345678',
    'carrier'
),
(
    'ea94a83e-e361-450f-9b5a-d55fb6018d2c', 1,
    'c8d9e0f1-a2b3-4567-cdef-890123456789',
    'operator'
),
(
    '8d0afe3f-2383-4704-9b65-123a1c9d54c9', 1,
    'd9e0f1a2-b3c4-5678-defa-901234567890',
    'operator'
),
(
    '78f847de-df47-4584-9a69-10182238f852', 1,
    'e0f1a2b3-c4d5-6789-efab-012345678901',
    'operator'
),
(
    'b99d4d3f-6a2a-4cdb-9fd6-2203590fed73', 1,
    'f1a2b3c4-d5e6-7890-fabc-123456789012',
    'contractor'
),
(
    'e4647a84-8dd9-4e36-a800-30000b9bb1d8', 1,
    'a2b3c4d5-e6f7-8901-abcd-234567890123',
    'operator'
),
(
    '1ce2b0dd-2c5f-4c68-b09b-7b6756827d10', 1,
    'b3c4d5e6-f7a8-9012-bcde-345678901234',
    'operator'
),
(
    '027338e1-1985-437f-9473-e2de44002ff8', 1,
    'c4d5e6f7-a8b9-0123-cdef-456789012345',
    'operator'
),
(
    '0ed6022b-4ea0-4357-8273-3593a7358cdf', 1,
    'd5e6f7a8-b9c0-1234-defa-567890123456',
    'customer'
)
ON CONFLICT (po_id) DO NOTHING; 