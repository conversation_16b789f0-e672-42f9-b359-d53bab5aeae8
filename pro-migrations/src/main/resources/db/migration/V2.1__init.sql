CREATE TABLE tariff
(
    t_id                 uuid      default gen_random_uuid() not null,
    t_version            int       default 1                 not null,
    PRIMARY KEY (t_id, t_version),
    t_version_created_at TIMESTAMP default now()             not null,
    t_version_created_by uuid,
    t_name               var<PERSON>r(300)                        not null,
    tags                 TEXT      default ''                not null
);

CREATE TYPE tariff_constraint_type AS ENUM ('ROUTE', 'TRANSPORT');
CREATE TYPE constraint_base_rule AS ENUM ('ALLOW', 'DENY');

CREATE TABLE tariff_constraint
(
    tc_id                 uuid      default gen_random_uuid() not null,
    tc_version            int       default 1                 not null,
    PRIMARY KEY (tc_id, tc_version),
    tc_version_created_at TIMESTAMP default now()             not null,
    tc_version_created_by uuid,
    t_id                  uuid,
    tc_type               tariff_constraint_type,
    tc_base_rule          constraint_base_rule,
    tags                  TEXT      default ''                not null
);

CREATE TABLE tariff_constraint_ex
(
    tce_id                 uuid      default gen_random_uuid() not null,
    tce_version            int       default 1                 not null,
    PRIMARY KEY (tce_id, tce_version),
    tce_version_created_at TIMESTAMP default now()             not null,
    tce_version_created_by uuid,
    tc_id                  uuid,
    e_id                   uuid,
    tags                   TEXT      default ''                not null
);

CREATE TYPE pay_method_type AS ENUM ('CASH', 'EMV', 'EMV_MIR', 'TROIKA_WALLET');

CREATE TABLE pay_method
(
    pm_id         uuid      default gen_random_uuid() not null,
    PRIMARY KEY (pm_id),
    pm_created_at TIMESTAMP default now()             not null,
    pm_type       pay_method_type,
    tags          TEXT      default ''                not null
);

CREATE TABLE product
(
    p_id                 uuid      default gen_random_uuid() not null,
    p_version            int       default 1                 not null,
    PRIMARY KEY (p_id, p_version),
    p_version_created_at TIMESTAMP default now()             not null,
    p_version_created_by uuid,
    p_name               VARCHAR(300),
    tags                 TEXT      default ''                not null
);

CREATE TABLE product_dict_row
(
    pdr_id                 uuid      default gen_random_uuid() not null,
    pdr_version            int       default 1                 not null,
    PRIMARY KEY (pdr_id, pdr_version),
    pdr_version_created_at TIMESTAMP default now()             not null,
    pdr_version_created_by uuid,
    p_id                   uuid,
    t_id                   uuid,
    pm_id                  uuid,
    pdr_fix_price          boolean   default false,
    pdr_price              bigint,
    tags                   TEXT      default ''                not null
);

CREATE TABLE product_dict_row_tariff_matrix_price
(
    pdrtmp_id                 uuid      default gen_random_uuid() not null,
    pdrtmp_version            int       default 1                 not null,
    PRIMARY KEY (pdrtmp_id, pdrtmp_version),
    pdrtmp_version_created_at TIMESTAMP default now()             not null,
    pdrtmp_version_created_by uuid,
    pdr_id                    uuid,
    st_from_id                uuid,
    st_to_id                  uuid,
    amount                    bigint,
    tags                      TEXT      default ''                not null
);

CREATE TYPE route_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');

CREATE TABLE route
(
    r_id                 uuid      default gen_random_uuid() not null,
    r_version            int       default 1                 not null,
    PRIMARY KEY (r_id, r_version),
    r_version_created_at TIMESTAMP default now()             not null,
    r_version_created_by uuid,
    r_active_from        TIMESTAMP default now()             not null,
    r_active_till        TIMESTAMP,
    r_index              integer,
    r_name               VARCHAR(500),
    r_status             route_status,
    tags                 TEXT      default ''                not null
);

CREATE TYPE route_constraint_type AS ENUM ('TRANSPORT');

CREATE TABLE route_constraint
(
    rc_id                 uuid      default gen_random_uuid() not null,
    rc_version            int       default 1                 not null,
    PRIMARY KEY (rc_id, rc_version),
    rc_version_created_at TIMESTAMP default now()             not null,
    rc_version_created_by uuid,
    r_id                  uuid,
    rc_type               route_constraint_type,
    rc_base_rule          constraint_base_rule,
    tags                  TEXT      default ''                not null
);

CREATE TABLE route_constraint_ex
(
    rce_id                 uuid      default gen_random_uuid() not null,
    rce_version            int       default 1                 not null,
    PRIMARY KEY (rce_id, rce_version),
    rce_version_created_at TIMESTAMP default now()             not null,
    rce_version_created_by uuid,
    rc_id                  uuid,
    e_id                   uuid,
    tags                   TEXT      default ''                not null
);

CREATE TABLE route_station
(
    rs_id                 uuid      default gen_random_uuid() not null,
    rs_version            int       default 1                 not null,
    PRIMARY KEY (rs_id, rs_version),
    rs_version_created_at TIMESTAMP default now()             not null,
    rs_version_created_by uuid,
    rs_active_from        TIMESTAMP default now()             not null,
    rs_active_till        TIMESTAMP,
    r_id                  uuid,
    st_id                 uuid,
    rs_pos                integer,
    rs_is_del             boolean   default false,
    tags                  TEXT      default ''                not null
);

CREATE TYPE station_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');

CREATE TABLE station
(
    st_id                 uuid      default gen_random_uuid() not null,
    st_version            int       default 1                 not null,
    PRIMARY KEY (st_id, st_version),
    st_version_created_at TIMESTAMP default now()             not null,
    st_version_created_by uuid,
    p_id                  uuid,
    tz_id                 uuid,
    st_name               VARCHAR(250),
    st_status             station_status,
    lat                   double precision,
    long                  double precision,
    tags                  TEXT      default ''                not null
);

CREATE TYPE vehicle_type AS ENUM ('BUS', 'TRAM', 'TROLLEYBUS', 'METRO');

CREATE TABLE vehicle
(
    vh_id                 uuid      default gen_random_uuid() not null,
    vh_version            int       default 1                 not null,
    PRIMARY KEY (vh_id, vh_version),
    vh_version_created_at TIMESTAMP default now()             not null,
    vh_version_created_by uuid,
    vh_type               vehicle_type,
    vh_number             VARCHAR(150),
    tags                  TEXT      default ''                not null
);

CREATE TYPE vehicle_constraint_type AS ENUM ('ROUTE');

CREATE TABLE vehicle_constraint
(
    vhc_id                 uuid      default gen_random_uuid() not null,
    vhc_version            int       default 1                 not null,
    PRIMARY KEY (vhc_id, vhc_version),
    vhc_version_created_at TIMESTAMP default now()             not null,
    vhc_version_created_by uuid,
    vh_id                  uuid,
    vhc_type               vehicle_constraint_type,
    vhc_base_rule          constraint_base_rule,
    tags                   TEXT      default ''                not null
);

CREATE TABLE vehicle_constraint_ex
(
    vhce_id                 uuid      default gen_random_uuid() not null,
    vhce_version            int       default 1                 not null,
    PRIMARY KEY (vhce_id, vhce_version),
    vhce_version_created_at TIMESTAMP default now()             not null,
    vhce_version_created_by uuid,
    vhc_id                  uuid,
    e_id                    uuid,
    tags                    TEXT      default ''                not null
);