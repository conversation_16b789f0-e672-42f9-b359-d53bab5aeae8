-- Изменение типа поля tc_type с TariffConstraintType на varchar(200)
-- для решения проблем с r2dbc при работе с enum типами

ALTER TABLE tariff_constraint
ALTER COLUMN tc_type TYPE varchar(200);

-- Обновляем существующие записи, если они есть
UPDATE tariff_constraint
SET tc_type = 'ROUTE'
WHERE tc_type IS NULL OR tc_type = '';

-- Добавляем ограничение для проверки допустимых значений
ALTER TABLE tariff_constraint
ADD CONSTRAINT chk_tariff_constraint_type
CHECK (tc_type IN ('ROUTE', 'TRANSPORT'));

-- Изменение типа поля tc_base_rule с BaseRule на varchar(200)
-- для решения проблем с r2dbc при работе с enum типами

ALTER TABLE tariff_constraint
ALTER COLUMN tc_base_rule TYPE varchar(200);

-- Обновляем существующие записи, если они есть
UPDATE tariff_constraint
SET tc_base_rule = 'ALLOW'
WHERE tc_base_rule IS NULL OR tc_base_rule = '';

-- Добавляем ограничение для проверки допустимых значений
ALTER TABLE tariff_constraint
ADD CONSTRAINT chk_tariff_constraint_base_rule
CHECK (tc_base_rule IN ('ALLOW', 'DENY'));

-- Изменение типа поля pdr_method_type с PayMethodType на varchar(200)
-- для решения проблем с r2dbc при работе с enum типами

ALTER TABLE product_dict_row
ALTER COLUMN pdr_method_type TYPE varchar(200);

-- Обновляем существующие записи, если они есть
UPDATE product_dict_row
SET pdr_method_type = 'CASH'
WHERE pdr_method_type IS NULL OR pdr_method_type = '';

-- Добавляем ограничение для проверки допустимых значений
ALTER TABLE product_dict_row
ADD CONSTRAINT chk_product_dict_row_method_type
CHECK (pdr_method_type IN ('CASH', 'EMV', 'TROIKA_TICKET', 'TROIKA_WALLET', 'ABT_TICKET', 'ABT_WALLET', 'PROSTOR_TICKET', 'QR_TICKET', 'QR_WALLET'));
