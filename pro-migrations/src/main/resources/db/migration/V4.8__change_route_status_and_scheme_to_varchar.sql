-- Изменение типа route_status с enum на varchar(200)
DO $$
BEGIN
    -- Создаем временную колонку с новым типом
    ALTER TABLE route ADD COLUMN r_status_new VARCHAR(200);
    
    -- Копируем данные из старой колонки в новую
    UPDATE route SET r_status_new = r_status::text;
    
    -- Удаляем старую колонку
    ALTER TABLE route DROP COLUMN r_status;
    
    -- Переименовываем новую колонку
    ALTER TABLE route RENAME COLUMN r_status_new TO r_status;
    
    -- Удаляем старый тип enum, если он больше нигде не используется
    DROP TYPE IF EXISTS route_status;
END $$;

-- Добавляем комментарий к колонке
COMMENT ON COLUMN route.r_status IS 'Статус маршрута (ACTIVE, DISABLED, BLOCKED, IS_DELETED)';

-- Изменение типа route_scheme с enum на varchar(200)
DO $$
BEGIN
    -- Создаем временную колонку с новым типом
    ALTER TABLE route ADD COLUMN r_scheme_new VARCHAR(200);
    
    -- Копируем данные из старой колонки в новую
    UPDATE route SET r_scheme_new = r_scheme::text;
    
    -- Удаляем старую колонку
    ALTER TABLE route DROP COLUMN r_scheme;
    
    -- Переименовываем новую колонку
    ALTER TABLE route RENAME COLUMN r_scheme_new TO r_scheme;
    
    -- Удаляем старый тип enum, если он больше нигде не используется
    DROP TYPE IF EXISTS route_scheme_type;
END $$;

-- Добавляем комментарий к колонке
COMMENT ON COLUMN route.r_scheme IS 'Схема маршрута (DIRECTIONAL, CIRCLE)'; 