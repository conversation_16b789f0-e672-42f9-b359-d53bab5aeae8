CREATE TYPE product_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');
CREATE TYPE tariff_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');
CREATE TYPE vehicle_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');

ALTER TABLE product ADD COLUMN p_status product_status;
ALTER TABLE vehicle ADD COLUMN vh_status vehicle_status;
ALTER TABLE tariff ADD COLUMN t_status tariff_status;
