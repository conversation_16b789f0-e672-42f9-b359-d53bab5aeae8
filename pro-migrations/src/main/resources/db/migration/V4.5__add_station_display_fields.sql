-- Добавление полей для синхронизации в таблице station
DO $$ 
BEGIN 
    -- Добавляем поле для даты последней синхронизации
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'station' AND column_name = 'st_last_sync_date'
    ) THEN
        ALTER TABLE station ADD COLUMN st_last_sync_date TIMESTAMP;
        COMMENT ON COLUMN station.st_last_sync_date IS 'Дата последней синхронизации';
    END IF;

    -- Добавляем поле для статуса синхронизации
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'station' AND column_name = 'st_sync_status'
    ) THEN
        ALTER TABLE station ADD COLUMN st_sync_status VARCHAR(50);
        COMMENT ON COLUMN station.st_sync_status IS 'Статус синхронизации';
    END IF;
END $$; 