-- Миграция V4.14: Изменение типа поля pr_status с enum на varchar для совместимости с R2DBC

-- Создаем временную колонку с новым типом
ALTER TABLE public.project 
ADD COLUMN pr_status_new varchar(200);

-- Копируем данные из старой колонки в новую
UPDATE public.project 
SET pr_status_new = pr_status::text;

-- Удаляем старую колонку
ALTER TABLE public.project 
DROP COLUMN pr_status;

-- Переименовываем новую колонку
ALTER TABLE public.project 
RENAME COLUMN pr_status_new TO pr_status;

-- Добавляем ограничение NOT NULL
ALTER TABLE public.project 
ALTER COLUMN pr_status SET NOT NULL;

-- До<PERSON><PERSON><PERSON><PERSON>я<PERSON><PERSON> комментарий
COMMENT ON COLUMN public.project.pr_status IS 'Статус проекта (DRAFT, ACTIVE, COMPLETED, etc.)'; 