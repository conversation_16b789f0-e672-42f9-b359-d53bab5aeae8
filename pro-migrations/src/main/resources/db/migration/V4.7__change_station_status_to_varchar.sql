-- Изменение типа station_status с enum на varchar(200)
DO $$
BEGIN
    -- Создаем временную колонку с новым типом
    ALTER TABLE station ADD COLUMN st_status_new VARCHAR(200);
    
    -- Копируем данные из старой колонки в новую
    UPDATE station SET st_status_new = st_status::text;
    
    -- Удаляем старую колонку
    ALTER TABLE station DROP COLUMN st_status;
    
    -- Переименовываем новую колонку
    ALTER TABLE station RENAME COLUMN st_status_new TO st_status;
    
    -- Удаляем старый тип enum, если он больше нигде не используется
    DROP TYPE IF EXISTS station_status;
END $$;

-- Д<PERSON><PERSON><PERSON>вляем комментарий к колонке
COMMENT ON COLUMN station.st_status IS 'Статус станции (ACTIVE, DISABLED, BLOCKED, IS_DELETED)'; 