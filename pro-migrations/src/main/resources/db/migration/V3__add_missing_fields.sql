-- Миграция V3: Добавление недостающих полей в таблицы Project, Manifest и ProjectFunction

-- Добавление недостающих полей в таблицу project
ALTER TABLE public.project
ADD COLUMN IF NOT EXISTS p_index integer;

-- Создание таблицы manifest
CREATE TABLE IF NOT EXISTS public.manifest (
    id                       bigserial primary key,
    m_id                     uuid      default gen_random_uuid() not null,
    m_version                integer   default 1                 not null,
    m_version_created_at     timestamp default now()             not null,
    m_version_created_by     uuid                                not null,
    m_project_id             uuid                                not null,
    m_project_version        integer                             not null,
    m_active_from            timestamp,
    m_active_till            timestamp,
    m_path                   varchar(500),
    m_status                 project_status                      not null default 'ACTIVE',
    tags                     text,
    unique (m_id, m_version)
);

-- Создание таблицы project_function
CREATE TABLE IF NOT EXISTS public.project_function (
    id                       bigserial primary key,
    pf_id                    uuid      default gen_random_uuid() not null,
    pf_version               integer   default 1                 not null,
    pf_version_created_at    timestamp default now()             not null,
    pf_version_created_by    uuid                                not null,
    pf_type                  varchar(50)                         not null,
    pf_project_id            uuid                                not null,
    pf_project_version       integer                             not null,
    pf_active_from           timestamp,
    pf_active_till           timestamp,
    pf_status                project_status                      not null default 'ACTIVE',
    tags                     text,
    unique (pf_id, pf_version)
);

-- Создание индексов для таблицы manifest
CREATE INDEX IF NOT EXISTS idx_manifest_project_id ON public.manifest (m_project_id);

-- Добавление внешних ключей
ALTER TABLE public.manifest 
ADD CONSTRAINT fk_manifest_project 
FOREIGN KEY (m_project_id, m_project_version) 
REFERENCES public.project(pr_id, pr_version);

ALTER TABLE public.project_function 
ADD CONSTRAINT fk_project_function_project 
FOREIGN KEY (pf_project_id, pf_project_version) 
REFERENCES public.project(pr_id, pr_version);

-- Комментарии к таблице manifest
COMMENT ON TABLE public.manifest IS 'Таблица манифестов проектов СберТройка ПРО';
COMMENT ON COLUMN public.manifest.id IS 'Технический первичный ключ (автоинкремент)';
COMMENT ON COLUMN public.manifest.m_id IS 'Уникальный идентификатор манифеста';
COMMENT ON COLUMN public.manifest.m_version IS 'Версия записи манифеста';
COMMENT ON COLUMN public.manifest.m_version_created_at IS 'Дата и время создания версии';
COMMENT ON COLUMN public.manifest.m_version_created_by IS 'Идентификатор пользователя, создавшего версию';
COMMENT ON COLUMN public.manifest.m_project_id IS 'Идентификатор проекта';
COMMENT ON COLUMN public.manifest.m_project_version IS 'Версия проекта';
COMMENT ON COLUMN public.manifest.m_active_from IS 'Дата начала действия манифеста';
COMMENT ON COLUMN public.manifest.m_active_till IS 'Дата окончания действия манифеста';
COMMENT ON COLUMN public.manifest.m_path IS 'Путь к файлу манифеста';
COMMENT ON COLUMN public.manifest.m_status IS 'Статус манифеста';
COMMENT ON COLUMN public.manifest.tags IS 'Теги манифеста в формате JSON';

-- Комментарии к таблице project_function
COMMENT ON TABLE public.project_function IS 'Таблица функций проектов СберТройка ПРО';
COMMENT ON COLUMN public.project_function.id IS 'Технический первичный ключ (автоинкремент)';
COMMENT ON COLUMN public.project_function.pf_id IS 'Уникальный идентификатор функции проекта';
COMMENT ON COLUMN public.project_function.pf_version IS 'Версия записи функции проекта';
COMMENT ON COLUMN public.project_function.pf_version_created_at IS 'Дата и время создания версии';
COMMENT ON COLUMN public.project_function.pf_version_created_by IS 'Идентификатор пользователя, создавшего версию';
COMMENT ON COLUMN public.project_function.pf_type IS 'Тип функции (PRO, PASIV)';
COMMENT ON COLUMN public.project_function.pf_project_id IS 'Идентификатор проекта';
COMMENT ON COLUMN public.project_function.pf_project_version IS 'Версия проекта';
COMMENT ON COLUMN public.project_function.pf_active_from IS 'Дата начала действия функции';
COMMENT ON COLUMN public.project_function.pf_active_till IS 'Дата окончания действия функции';
COMMENT ON COLUMN public.project_function.pf_status IS 'Статус функции проекта';
COMMENT ON COLUMN public.project_function.tags IS 'Теги функции проекта в формате JSON'; 