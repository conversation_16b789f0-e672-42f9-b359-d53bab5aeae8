-- Обновление enum для статусов проектов
ALTER TYPE project_status ADD VALUE IF NOT EXISTS 'DRAFT';
ALTER TYPE project_status ADD VALUE IF NOT EXISTS 'EXPIRING';
ALTER TYPE project_status ADD VALUE IF NOT EXISTS 'COMPLETED';

-- Добавление новых полей в таблицу project
ALTER TABLE public.project 
ADD COLUMN IF NOT EXISTS pr_code varchar(255),
ADD COLUMN IF NOT EXISTS pr_project_type varchar(100),
ADD COLUMN IF NOT EXISTS pr_contract_name varchar(500),
ADD COLUMN IF NOT EXISTS pr_total_budget bigint DEFAULT 0,
ADD COLUMN IF NOT EXISTS pr_spent_budget bigint DEFAULT 0,
ADD COLUMN IF NOT EXISTS pr_progress integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS pr_manager varchar(255),
ADD COLUMN IF NOT EXISTS pr_last_sync_date timestamp,
ADD COLUMN IF NOT EXISTS pr_sync_status varchar(50) DEFAULT 'pending';

-- Изменение типа contract_id на UUID
ALTER TABLE public.project 
ALTER COLUMN pr_contract_id TYPE uuid USING pr_contract_id::uuid;

-- Создание таблицы для связи проекта с операторской организацией
CREATE TABLE IF NOT EXISTS public.project_operator (
    po_id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    po_project_id uuid NOT NULL,
    po_project_version integer NOT NULL DEFAULT 1,
    po_created_at timestamp DEFAULT now() NOT NULL,
    po_operator_id uuid NOT NULL,
    po_operator_name varchar(500) NOT NULL,
    po_operator_role varchar(100) NOT NULL,
    
    CONSTRAINT fk_project_operator_project 
        FOREIGN KEY (po_project_id, po_project_version) 
        REFERENCES public.project(pr_id, pr_version) 
        ON DELETE CASCADE
);

-- Создание таблицы для участников организаций
CREATE TABLE IF NOT EXISTS public.participant_organization (
    po_id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    po_project_id uuid NOT NULL,
    po_project_version integer NOT NULL DEFAULT 1,
    po_organization_id uuid NOT NULL,
    po_role varchar(100) NOT NULL,
    po_created_at timestamp DEFAULT now() NOT NULL,
    po_updated_at timestamp DEFAULT now() NOT NULL,
    
    CONSTRAINT fk_participant_organization_project 
        FOREIGN KEY (po_project_id, po_project_version) 
        REFERENCES public.project(pr_id, pr_version) 
        ON DELETE CASCADE
);

-- Индексы для оптимизации запросов
CREATE INDEX IF NOT EXISTS idx_project_operator_project_id ON public.project_operator (po_project_id, po_project_version);
CREATE INDEX IF NOT EXISTS idx_project_operator_operator_id ON public.project_operator (po_operator_id);
CREATE INDEX IF NOT EXISTS idx_participant_organization_project_id ON public.participant_organization (po_project_id, po_project_version);
CREATE INDEX IF NOT EXISTS idx_participant_organization_org_id ON public.participant_organization (po_organization_id);

-- Комментарии к новым полям таблицы project
COMMENT ON COLUMN public.project.pr_code IS 'Код проекта';
COMMENT ON COLUMN public.project.pr_project_type IS 'Тип проекта';
COMMENT ON COLUMN public.project.pr_contract_name IS 'Наименование договора';
COMMENT ON COLUMN public.project.pr_total_budget IS 'Общий бюджет проекта';
COMMENT ON COLUMN public.project.pr_spent_budget IS 'Потраченный бюджет';
COMMENT ON COLUMN public.project.pr_progress IS 'Прогресс выполнения проекта (0-100)';
COMMENT ON COLUMN public.project.pr_manager IS 'Менеджер проекта';
COMMENT ON COLUMN public.project.pr_last_sync_date IS 'Дата последней синхронизации';
COMMENT ON COLUMN public.project.pr_sync_status IS 'Статус синхронизации';

-- Комментарии к новым таблицам
COMMENT ON TABLE public.project_operator IS 'Связь проекта с операторской организацией';
COMMENT ON COLUMN public.project_operator.po_id IS 'Уникальный идентификатор записи';
COMMENT ON COLUMN public.project_operator.po_project_id IS 'Идентификатор проекта';
COMMENT ON COLUMN public.project_operator.po_project_version IS 'Версия проекта';
COMMENT ON COLUMN public.project_operator.po_created_at IS 'Дата создания связи';
COMMENT ON COLUMN public.project_operator.po_operator_id IS 'Идентификатор оператора';
COMMENT ON COLUMN public.project_operator.po_operator_name IS 'Наименование оператора';
COMMENT ON COLUMN public.project_operator.po_operator_role IS 'Роль оператора в проекте';

COMMENT ON TABLE public.participant_organization IS 'Участники организаций в проекте';
COMMENT ON COLUMN public.participant_organization.po_id IS 'Уникальный идентификатор записи';
COMMENT ON COLUMN public.participant_organization.po_project_id IS 'Идентификатор проекта';
COMMENT ON COLUMN public.participant_organization.po_project_version IS 'Версия проекта';
COMMENT ON COLUMN public.participant_organization.po_organization_id IS 'Идентификатор организации';
COMMENT ON COLUMN public.participant_organization.po_role IS 'Роль организации в проекте';
COMMENT ON COLUMN public.participant_organization.po_created_at IS 'Дата создания записи';
COMMENT ON COLUMN public.participant_organization.po_updated_at IS 'Дата обновления записи'; 