-- Обновление таблицы station
DO $$
BEGIN
    -- Добавляем колонку st_latin_name если её нет
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'station' AND column_name = 'st_latin_name') THEN
        ALTER TABLE public.station ADD COLUMN st_latin_name varchar(500);
    END IF;
    
    -- Добавляем колонку st_city_id если её нет
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'station' AND column_name = 'st_city_id') THEN
        ALTER TABLE public.station ADD COLUMN st_city_id uuid;
    END IF;
    
    -- Добавляем колонку st_district_id если её нет
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'station' AND column_name = 'st_district_id') THEN
        ALTER TABLE public.station ADD COLUMN st_district_id uuid;
    END IF;
    
    -- Добавляем колонку st_region_id если её нет
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'station' AND column_name = 'st_region_id') THEN
        ALTER TABLE public.station ADD COLUMN st_region_id uuid;
    END IF;
    
    -- Добавляем колонку st_country_id если её нет
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'station' AND column_name = 'st_country_id') THEN
        ALTER TABLE public.station ADD COLUMN st_country_id uuid;
    END IF;
END $$;

-- Комментарии к новым полям
COMMENT ON COLUMN public.station.st_latin_name IS 'Латинское наименование станции';
COMMENT ON COLUMN public.station.st_city_id IS 'Идентификатор города';
COMMENT ON COLUMN public.station.st_district_id IS 'Идентификатор района';
COMMENT ON COLUMN public.station.st_region_id IS 'Идентификатор региона';
COMMENT ON COLUMN public.station.st_country_id IS 'Идентификатор страны';

-- Создание таблицы стран
CREATE TABLE IF NOT EXISTS public.country (
    c_id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    c_name varchar(200) NOT NULL UNIQUE,
    c_code varchar(2),
    c_created_at timestamp DEFAULT now() NOT NULL,
    c_updated_at timestamp DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.country IS 'Справочник стран';
COMMENT ON COLUMN public.country.c_id IS 'Уникальный идентификатор страны';
COMMENT ON COLUMN public.country.c_name IS 'Наименование страны';
COMMENT ON COLUMN public.country.c_code IS 'Код страны (ISO 3166-1 alpha-2)';
COMMENT ON COLUMN public.country.c_created_at IS 'Дата и время создания записи';
COMMENT ON COLUMN public.country.c_updated_at IS 'Дата и время последнего обновления записи';

-- Создание таблицы регионов
CREATE TABLE IF NOT EXISTS public.region (
    r_id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    r_name varchar(200) NOT NULL,
    r_country_id uuid NOT NULL,
    r_code varchar(10),
    r_created_at timestamp DEFAULT now() NOT NULL,
    r_updated_at timestamp DEFAULT now() NOT NULL,
    
    CONSTRAINT fk_region_country
        FOREIGN KEY (r_country_id)
        REFERENCES public.country(c_id)
        ON DELETE CASCADE,
    
    CONSTRAINT uk_region_name_country
        UNIQUE (r_name, r_country_id)
);

COMMENT ON TABLE public.region IS 'Справочник регионов';
COMMENT ON COLUMN public.region.r_id IS 'Уникальный идентификатор региона';
COMMENT ON COLUMN public.region.r_name IS 'Наименование региона';
COMMENT ON COLUMN public.region.r_country_id IS 'Идентификатор страны, к которой относится регион';
COMMENT ON COLUMN public.region.r_code IS 'Код региона';
COMMENT ON COLUMN public.region.r_created_at IS 'Дата и время создания записи';
COMMENT ON COLUMN public.region.r_updated_at IS 'Дата и время последнего обновления записи';

-- Создание таблицы городов
CREATE TABLE IF NOT EXISTS public.city (
    city_id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    city_name varchar(200) NOT NULL,
    city_region_id uuid NOT NULL,
    city_code varchar(10),
    city_created_at timestamp DEFAULT now() NOT NULL,
    city_updated_at timestamp DEFAULT now() NOT NULL,
    
    CONSTRAINT fk_city_region
        FOREIGN KEY (city_region_id)
        REFERENCES public.region(r_id)
        ON DELETE CASCADE,
    
    CONSTRAINT uk_city_name_region
        UNIQUE (city_name, city_region_id)
);

COMMENT ON TABLE public.city IS 'Справочник городов';
COMMENT ON COLUMN public.city.city_id IS 'Уникальный идентификатор города';
COMMENT ON COLUMN public.city.city_name IS 'Наименование города';
COMMENT ON COLUMN public.city.city_region_id IS 'Идентификатор региона, к которому относится город';
COMMENT ON COLUMN public.city.city_code IS 'Код города';
COMMENT ON COLUMN public.city.city_created_at IS 'Дата и время создания записи';
COMMENT ON COLUMN public.city.city_updated_at IS 'Дата и время последнего обновления записи';

-- Создание таблицы районов
CREATE TABLE IF NOT EXISTS public.district (
    d_id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    d_name varchar(200) NOT NULL,
    d_city_id uuid NOT NULL,
    d_code varchar(10),
    d_created_at timestamp DEFAULT now() NOT NULL,
    d_updated_at timestamp DEFAULT now() NOT NULL,
    
    CONSTRAINT fk_district_city
        FOREIGN KEY (d_city_id)
        REFERENCES public.city(city_id)
        ON DELETE CASCADE,
    
    CONSTRAINT uk_district_name_city
        UNIQUE (d_name, d_city_id)
);

COMMENT ON TABLE public.district IS 'Справочник районов';
COMMENT ON COLUMN public.district.d_id IS 'Уникальный идентификатор района';
COMMENT ON COLUMN public.district.d_name IS 'Наименование района';
COMMENT ON COLUMN public.district.d_city_id IS 'Идентификатор города, к которому относится район';
COMMENT ON COLUMN public.district.d_code IS 'Код района';
COMMENT ON COLUMN public.district.d_created_at IS 'Дата и время создания записи';
COMMENT ON COLUMN public.district.d_updated_at IS 'Дата и время последнего обновления записи';

-- Создание индексов для улучшения производительности
CREATE INDEX IF NOT EXISTS idx_station_city_id ON public.station(st_city_id);
CREATE INDEX IF NOT EXISTS idx_station_district_id ON public.station(st_district_id);
CREATE INDEX IF NOT EXISTS idx_station_region_id ON public.station(st_region_id);
CREATE INDEX IF NOT EXISTS idx_station_country_id ON public.station(st_country_id);

CREATE INDEX IF NOT EXISTS idx_region_country_id ON public.region(r_country_id);
CREATE INDEX IF NOT EXISTS idx_city_region_id ON public.city(city_region_id);
CREATE INDEX IF NOT EXISTS idx_district_city_id ON public.district(d_city_id);

-- Добавление внешних ключей для станций
DO $$
BEGIN
    -- Добавляем внешний ключ fk_station_city если его нет
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE table_name = 'station' AND constraint_name = 'fk_station_city') THEN
        ALTER TABLE public.station 
        ADD CONSTRAINT fk_station_city
            FOREIGN KEY (st_city_id)
            REFERENCES public.city(city_id)
            ON DELETE SET NULL;
    END IF;
    
    -- Добавляем внешний ключ fk_station_district если его нет
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE table_name = 'station' AND constraint_name = 'fk_station_district') THEN
        ALTER TABLE public.station 
        ADD CONSTRAINT fk_station_district
            FOREIGN KEY (st_district_id)
            REFERENCES public.district(d_id)
            ON DELETE SET NULL;
    END IF;
    
    -- Добавляем внешний ключ fk_station_region если его нет
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE table_name = 'station' AND constraint_name = 'fk_station_region') THEN
        ALTER TABLE public.station 
        ADD CONSTRAINT fk_station_region
            FOREIGN KEY (st_region_id)
            REFERENCES public.region(r_id)
            ON DELETE SET NULL;
    END IF;
    
    -- Добавляем внешний ключ fk_station_country если его нет
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE table_name = 'station' AND constraint_name = 'fk_station_country') THEN
        ALTER TABLE public.station 
        ADD CONSTRAINT fk_station_country
            FOREIGN KEY (st_country_id)
            REFERENCES public.country(c_id)
            ON DELETE SET NULL;
    END IF;
END $$; 