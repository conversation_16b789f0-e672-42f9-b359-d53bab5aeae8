-- Создание таблицы проектов
DO $$ BEGIN
    CREATE TYPE project_status AS ENUM ('TEST', 'DEMO', 'ACTIVE', 'ARCHIVE', 'NSI_CONFLICT');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

CREATE TABLE IF NOT EXISTS public.project (
    id                       bigserial primary key,              -- Простой автоинкрементный первичный ключ
    pr_id                    uuid      default gen_random_uuid() not null,
    pr_version               integer   default 1                 not null,
    pr_version_created_at    timestamp default now()             not null,
    pr_version_created_by    uuid                                not null,
    pr_name                  varchar(255)                        not null,
    pr_status                project_status                      not null,
    pr_start_date            date                                not null,
    pr_end_date              date                                not null,
    pr_contract_id           uuid                                not null,
    pr_contract_number       varchar(255)                        not null,
    pr_description           text,
    pr_region                varchar(255),
    tags                     text,
    unique (pr_id, pr_version)                                   -- Уникальность по бизнес-ключу
);

-- Индексы для оптимизации запросов
CREATE INDEX idx_project_name ON public.project (pr_name);
CREATE INDEX idx_project_status ON public.project (pr_status);
CREATE INDEX idx_project_contract_id ON public.project (pr_contract_id);
CREATE INDEX idx_project_contract_number ON public.project (pr_contract_number);

-- Комментарии к полям
COMMENT ON TABLE public.project IS 'Таблица проектов СберТройка ПРО';
COMMENT ON COLUMN public.project.id IS 'Технический первичный ключ (автоинкремент)';
COMMENT ON COLUMN public.project.pr_id IS 'Уникальный идентификатор проекта';
COMMENT ON COLUMN public.project.pr_version IS 'Версия записи проекта';
COMMENT ON COLUMN public.project.pr_version_created_at IS 'Дата и время создания версии';
COMMENT ON COLUMN public.project.pr_version_created_by IS 'Идентификатор пользователя, создавшего версию';
COMMENT ON COLUMN public.project.pr_name IS 'Наименование проекта (региона)';
COMMENT ON COLUMN public.project.pr_status IS 'Статус проекта';
COMMENT ON COLUMN public.project.pr_start_date IS 'Дата начала проекта';
COMMENT ON COLUMN public.project.pr_end_date IS 'Дата окончания проекта';
COMMENT ON COLUMN public.project.pr_contract_id IS 'Идентификатор связанного договора';
COMMENT ON COLUMN public.project.pr_contract_number IS 'Номер связанного договора';
COMMENT ON COLUMN public.project.pr_description IS 'Описание проекта';
COMMENT ON COLUMN public.project.pr_region IS 'Регион проекта';
COMMENT ON COLUMN public.project.tags IS 'Теги проекта в формате JSON';
