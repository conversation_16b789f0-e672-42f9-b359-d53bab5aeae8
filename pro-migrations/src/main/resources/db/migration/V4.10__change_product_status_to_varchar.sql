-- Изменение типа p_status с enum на varchar(200) и добавление типовых данных
-- Версия миграции: 4.10

-- Изменение типа p_status с enum на varchar(200)
DO $$
BEGIN
    -- Создаем временную колонку с новым типом
    ALTER TABLE product ADD COLUMN p_status_new VARCHAR(200);
    -- Копируем данные из старой колонки в новую
    UPDATE product SET p_status_new = p_status::text;
    -- Удаляем старую колонку
    ALTER TABLE product DROP COLUMN p_status;
    -- Переименовываем новую колонку
    ALTER TABLE product RENAME COLUMN p_status_new TO p_status;
    -- Удаляем старый тип enum, если он больше нигде не используется
    DROP TYPE IF EXISTS product_status;
END $$;

-- Добавляем комментарий к колонке
COMMENT ON COLUMN product.p_status IS 'Статус продукта (ACTIVE, DISABLED, BLOCKED, IS_DELETED)';

-- Добавляем индекс для оптимизации запросов
CREATE INDEX idx_product_status ON product(p_status);

-- Добавляем типовые данные продуктов
INSERT INTO product (p_id, p_version, p_version_created_at, p_project_id, p_name, p_status, tags) VALUES
(gen_random_uuid(), 1, now(), 'a223116c-49b4-43eb-8a47-c76a68a4225d', 'Билет на автобус', 'ACTIVE', ''),
(gen_random_uuid(), 1, now(), 'a223116c-49b4-43eb-8a47-c76a68a4225d', 'Билет на трамвай', 'ACTIVE', ''),
(gen_random_uuid(), 1, now(), 'a223116c-49b4-43eb-8a47-c76a68a4225d', 'Билет на троллейбус', 'ACTIVE', ''),
(gen_random_uuid(), 1, now(), 'a223116c-49b4-43eb-8a47-c76a68a4225d', 'Билет на метро', 'ACTIVE', ''); 