-- Изменение типа поля t_status с TariffStatus на varchar(200)
-- для решения проблем с r2dbc при работе с enum типами

ALTER TABLE tariff 
ALTER COLUMN t_status TYPE varchar(200);

-- Обновляем существующие записи, если они есть
UPDATE tariff 
SET t_status = 'ACTIVE' 
WHERE t_status IS NULL OR t_status = '';

-- Добавляем ограничение для проверки допустимых значений
ALTER TABLE tariff 
ADD CONSTRAINT chk_tariff_status 
CHECK (t_status IN ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED')); 