# PRO Domain - Ядро системы СберТройка ПРО

## Обзор

Проект представляет собой ядро системы СберТройка ПРО, включающее в себя:
- **Управление НСИ** (нормативно-справочной информацией)
- **Хранение и первичная обработка фактов проходов**
- **Маршрутизация операций в другие части системы**

Система состоит из:
- **Библиотечных модулей** для переиспользования логики
- **Микросервисов** для обработки и управления данными
- **API сервисов** для внешнего взаимодействия

Система построена на Kotlin/Spring Boot с автоматическим CI/CD pipeline для разработки, тестирования и развертывания.

## Архитектура

### Библиотечные модули
- `pro-common` - Общая логика и утилиты для работы с данными
- `pro-api` - Публичное API для работы с НСИ и фактами проходов
- `pro-gate-api` - API для внешнего взаимодействия
- `pro-gate-private-api` - Приватное API для внутренних сервисов
- `pro-model` - Модели данных и DTO
- `manifest-model` - Модели для манифестов
- `manifest-starter` - Стартер для работы с манифестами

### Микросервисы
- `pro-gate` - API Gateway для внешних запросов и маршрутизации
- `pro-processing` - Сервис обработки фактов проходов и НСИ

## Структура проекта

```
pro-domain/
├── pro-common/                 # Общий модуль
├── pro-api/                    # Публичное API
├── pro-gate-api/               # API для внешнего взаимодействия
├── pro-gate-private-api/       # Приватное API
├── pro-model/                  # Модели данных
├── manifest-model/             # Модели манифестов
├── manifest-starter/           # Стартер для манифестов
├── pro-gate/                   # API Gateway микросервис
├── pro-processing/             # Сервис обработки фактов проходов и НСИ
├── charts/                     # Helm чарты для Kubernetes
│   ├── pro-gate/
│   └── pro-processing/
├── scripts/                    # Скрипты для CI/CD
├── data/                       # Данные для локальной разработки
├── docker-compose.yml          # Локальное окружение
├── build.gradle.kts            # Корневая конфигурация Gradle
├── .gitlab-ci.yml              # GitLab CI/CD pipeline
├── Makefile                    # Команды для управления Docker
└── gradle.properties           # Версия проекта
```

## Технологический стек

- **Язык**: Kotlin
- **Фреймворк**: Spring Boot
- **База данных**: PostgreSQL
- **Очереди**: Apache Kafka
- **Аутентификация**: Keycloak
- **Координация**: Apache ZooKeeper
- **Хранилище**: MinIO (S3-совместимое)
- **Контейнеризация**: Docker
- **Оркестрация**: Kubernetes + Helm
- **CI/CD**: GitLab CI/CD
- **Артефакты**: Nexus Repository

## Быстрый старт

### Предварительные требования

- Java 17
- Docker & Docker Compose
- Gradle 8.x (или используйте gradlew)
- Git

### Локальная разработка

1. **Клонирование репозитория**
```bash
git clone <repository-url>
cd pro-domain
```

2. **Сборка библиотечных модулей**
```bash
./gradlew build
```

3. **Запуск локального окружения**
```bash
# Запуск инфраструктуры (PostgreSQL, Keycloak, ZooKeeper, Kafka, S3)
docker-compose up -d pro_db keycloak zoo1 zoo2 zoo3 kafka s3
```

4. **Сборка и запуск микросервисов**
```bash
# Сборка Docker образов
make docker-build

# Запуск микросервисов
make start
```

### Основные функции системы

- **Управление НСИ**: Хранение и управление нормативно-справочной информацией (терминалы, маршруты, тарифы и т.д.)
- **Обработка фактов проходов**: Прием, валидация и первичная обработка данных о проходах пассажиров
- **Маршрутизация**: Перенаправление операций в соответствующие подсистемы СберТройка ПРО

### Доступные сервисы

- **API Gateway**: http://localhost:5010
- **Keycloak**: http://localhost:8081
- **PostgreSQL**: localhost:5432
- **ZooKeeper**: localhost:2181, 2182, 2183
- **Kafka**: localhost:9092
- **MinIO (S3)**: http://localhost:9000

## CI/CD Pipeline

Проект использует GitLab CI/CD с Docker executor для автоматизации:

### Сценарии pipeline:

1. **Любая ветка (кроме develop/master)**
   - Сборка и тестирование библиотечных модулей

2. **Ветка develop**
   - Сборка и тестирование
   - Публикация SNAPSHOT версий в Nexus
   - Сборка и деплой микросервисов в тестовое окружение

3. **Ветка master / Теги**
   - Выпуск релизных версий
   - Публикация в Nexus
   - Сборка и деплой микросервисов в продакшн

### Особенности:
- Использует Docker executor с тегом `docker`
- Автоматическое создание `gradle.properties` из переменных окружения
- Кэширование Gradle для ускорения сборок
- Helm чарты для Kubernetes деплоя
- Валидация Kubernetes манифестов с помощью kubeval

## Разработка

### Структура веток
- `master` - основная ветка для релизов
- `develop` - ветка для разработки и тестирования
- `feature/*` - ветки для новых функций
- `release/*` - ветки для подготовки релизов
- `hotfix/*` - ветки для критических исправлений

### Процесс разработки
1. Создать feature ветку от `develop`
2. Разработать функциональность
3. Создать MR в `develop`
4. После тестирования создать MR из `develop` в `master`

### Полезные команды

```bash
# Сборка проекта
./gradlew build

# Запуск тестов
./gradlew test

# Публикация SNAPSHOT (локально)
./gradlew publishToNexusSnapshot

# Публикация релиза (локально)
./gradlew publishToNexus

# Выпуск релиза
./gradlew release

# Docker команды
make docker-build          # Сборка образов
make start                 # Запуск сервисов
make stop                  # Остановка сервисов
make restart               # Перезапуск сервисов
make rebuild-and-restart   # Пересборка и перезапуск
```

## Конфигурация

### Переменные окружения для микросервисов

**pro-gate:**
- `DB_URL` - URL PostgreSQL базы данных
- `DB_USER` - Пользователь базы данных
- `DB_PASSWORD` - Пароль базы данных
- `DB_MIGRATION_ENABLE` - Включение миграций
- `R2DB_URL` - URL для R2DBC подключения
- `TMS_KEYCLOAK_HOST` - Хост Keycloak
- `TMS_KEYCLOAK_REALM` - Realm в Keycloak
- `TMS_KEYCLOAK_SECRET` - Секрет клиента
- `TMS_KEYCLOAK_USERNAME` - Имя пользователя
- `TMS_KEYCLOAK_PASSWORD` - Пароль пользователя
- `TMS_KEYCLOAK_CLIENT_ID` - ID клиента
- `TMS_KEYCLOAK_USER_REALM` - Realm пользователя

**pro-processing:**
- `DB_URL` - URL PostgreSQL базы данных
- `DB_USER` - Пользователь базы данных
- `DB_PASSWORD` - Пароль базы данных
- `R2DB_URL` - URL для R2DBC подключения
- `ZOOKEEPER_NODES` - Адреса ZooKeeper узлов
- `S3_URL` - URL S3-совместимого хранилища
- `S3_ACCESS_KEY_ID` - Ключ доступа S3
- `S3_SECRET_ACCESS_KEY` - Секретный ключ S3
- `S3_BUCKET` - Имя bucket в S3
- `KAFKA_SERVERS` - Адреса Kafka серверов

### Настройка CI/CD

Для работы pipeline необходимо настроить переменные в GitLab:

1. Перейти в **Settings** → **CI/CD** → **Variables**
2. Добавить переменные:
   - `MAVEN_USER` (Protected)
   - `MAVEN_PASSWORD` (Protected + Masked)
   - `DOCKER_REPOSITORY_ADDR` - Адрес Docker registry
   - `KUBECONFIG_DEVELOP` - Kubeconfig для тестового кластера

## Публикация в Nexus

Проект настроен для публикации в Nexus репозиторий:

- **SNAPSHOT**: `https://nexus.sbertroika.tech/repository/maven-snapshots/`
- **Releases**: `https://nexus.sbertroika.tech/repository/maven-releases/`

### Артефакты:
- `ru.sbertroika.pro:pro-api:${version}`
- `ru.sbertroika.pro:pro-gate-api:${version}`
- `ru.sbertroika.pro:pro-gate-private-api:${version}`
- `ru.sbertroika.pro:pro-common:${version}`
- `ru.sbertroika.pro:pro-model:${version}`
- `ru.sbertroika.pro:manifest-model:${version}`
- `ru.sbertroika.pro:manifest-starter:${version}`

## Развертывание

### Kubernetes

Микросервисы развертываются в Kubernetes с помощью Helm чартов:

```bash
# Деплой в тестовое окружение
helm upgrade pro-gate charts/pro-gate \
  --install \
  --set namespace=pro \
  --set image.tag=develop

# Деплой в продакшн
helm upgrade pro-gate charts/pro-gate \
  --install \
  --set namespace=pro \
  --set image.tag=v1.0.0
```

### Docker

Для локального развертывания используйте docker-compose или Makefile:

```bash
# Полное окружение
make docker-build-and-run

# Только инфраструктура
docker-compose up -d pro_db keycloak zoo1 zoo2 zoo3 kafka s3
```

## Troubleshooting

### Ошибки сборки
- Убедиться, что используется Java 17
- Проверить, что все зависимости доступны в Nexus
- Проверить, что gradle.properties содержит корректные учетные данные

### Ошибки CI/CD
- Проверить настройки переменных окружения в GitLab
- Убедиться, что GitLab runner с тегом `docker` доступен

### Ошибки локального запуска
- Проверить, что Docker и Docker Compose установлены
- Убедиться, что порты не заняты другими сервисами
- Проверить логи контейнеров: `docker-compose logs <service-name>`

### Ошибки публикации
- Проверить учетные данные Nexus
- Убедиться, что версия корректна и не конфликтует

## Релиз

### Подготовка к релизу

Проверить:
- Не должно быть uncommitted данных
- Данные должны соответствовать репозиторию (git push)
- Корректно указанная версия в gradle.properties
- Если релиз нужно сделать повторно, например был релиз версии 1.0.0 и нужно заново сделать релиз на версию 1.0.0, то необходимо очистить информацию в тегах (из корня проекта):
  ```bash
  git tag -d v1.0.0-SNAPSHOT
  git push origin :refs/tags/v1.0.0-SNAPSHOT
  ```

### Выполнение релиза

```bash
# Из корня проекта (pro-domain)
./gradlew release
```

## Обновления архитектуры

### Разделение pro-gate на два модуля

Модуль `pro-gate` был разделен на два независимых сервиса:

#### pro-gate (Публичный API)
- **Порт**: 5000 (локально 5010)
- **Назначение**: Публичные API для работы с проектами
- **Содержит**: Только публичные методы из `PROGateService`
- **Зависимости**: `pro-gate-api`, `pro-common`, `pro-model`

#### pro-gate-private (Приватный API)
- **Порт**: 5005 (локально 5015)
- **Назначение**: Административные операции и управление данными
- **Содержит**: Все методы из `PROGatePrivateService`
- **Зависимости**: `pro-gate-private-api`, `pro-common`, `pro-model`
- **Дополнительно**: Интеграция с Keycloak, TMS, управление пользователями

#### pro-ui (Веб-интерфейс)
- **Порт**: 80 (локально 3000)
- **Технологии**: Vue.js 3, PrimeVue, Vite
- **Назначение**: Административный интерфейс для управления проектами
- **Прокси**: Автоматическая маршрутизация запросов к pro-gate и pro-gate-private

### Локальный запуск новой архитектуры

```bash
# Сборка и запуск всех сервисов
make docker-build-and-run

# Доступные сервисы:
# - pro-gate: http://localhost:5010
# - pro-gate-private: http://localhost:5015
# - pro-ui: http://localhost:3000
# - pro-processing: http://localhost:8081
```

### Helm Charts

Добавлены новые Helm чарты:
- `charts/pro-gate-private/` - Для приватного API
- `charts/pro-ui/` - Для веб-интерфейса

### CI/CD

Обновлен `.gitlab-ci.yml` для поддержки:
- Автоматической сборки Docker образов для новых модулей
- Деплоя в Kubernetes через Helm
- Валидации конфигураций

## Контакты

Для вопросов по проекту обращайтесь к команде разработки.
