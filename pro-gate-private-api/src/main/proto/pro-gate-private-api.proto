syntax = "proto3";

package ru.sbertroika.pro.gate.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common.proto";
import "common-pro.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.pro.gate.v1";

service PROGatePrivateService {
  rpc getRoleList(google.protobuf.Empty) returns(RoleListResponse);
  rpc getEmployeeList(EmployeeListRequest) returns (EmployeeListResponse);
  rpc registrationEmployee(EmployeeRequest) returns (common.v1.CreateResponse);
  rpc updateEmployee(EmployeeRequest) returns (common.v1.EmptyResponse);
  rpc getEmployeeById(common.v1.ByIdRequest) returns (EmployeeResponse);

  rpc getRouteList(RouteListRequest) returns (common.pro.RouteListResponse);
  rpc getTariffList(TariffListRequest) returns (common.pro.TariffListResponse);
  rpc getProductList(ProductListRequest) returns (common.pro.ProductListResponse);
  rpc getStationList(StationListRequest) returns (common.pro.StationListResponse);
  rpc getTransportList(TransportListRequest) returns (common.pro.TransportListResponse);

  rpc createRoute(common.pro.Route) returns (common.v1.CreateResponse);
  rpc createTariff(common.pro.Tariff) returns (common.v1.CreateResponse);
  rpc createProduct(common.pro.Product) returns (common.v1.EmptyResponse);
  rpc createStation(common.pro.Station) returns (common.v1.CreateResponse);
  rpc createTransport(common.pro.Transport) returns (common.v1.CreateResponse);

  rpc updateRoute(common.pro.Route) returns (common.v1.EmptyResponse);
  rpc updateTariff(common.pro.Tariff) returns (common.v1.EmptyResponse);
  rpc updateProduct(common.pro.Product) returns (common.v1.EmptyResponse);
  rpc updateStation(common.pro.Station) returns (common.v1.EmptyResponse);
  rpc updateTransport(common.pro.Transport) returns (common.v1.EmptyResponse);

  rpc deleteRoute(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc deleteTariff(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc deleteProduct(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc deleteStation(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc deleteTransport(ByIdRequest) returns (common.v1.EmptyResponse);

  rpc addRouteStation(RouteStation) returns (common.v1.EmptyResponse);
  rpc deleteRouteStation(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc updateRouteStation(RouteStation) returns (common.v1.EmptyResponse);
  rpc getRouteStationList(RouteStationListRequest) returns (RouteStationListResponse);

  rpc addRouteConstraint(common.pro.Constraint) returns (common.v1.EmptyResponse);
  rpc deleteRouteConstraint(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc updateRouteConstraint(common.pro.Constraint) returns (common.v1.EmptyResponse);
  rpc getRouteConstraintList(RouteConstraintListRequest) returns (RouteConstraintListResponse);

  rpc addVehicleConstraint(common.pro.Constraint) returns (common.v1.EmptyResponse);
  rpc deleteVehicleConstraint(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc updateVehicleConstraint(common.pro.Constraint) returns (common.v1.EmptyResponse);
  rpc getVehicleConstraintList(VehicleConstraintListRequest) returns (VehicleConstraintListResponse);

  rpc addTariffConstraint(common.pro.Constraint) returns (common.v1.EmptyResponse);
  rpc deleteTariffConstraint(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc updateTariffConstraint(common.pro.Constraint) returns (common.v1.EmptyResponse);
  rpc getTariffConstraintList(TariffConstraintListRequest) returns (TariffConstraintListResponse);

  rpc addProductDictRow(ProductDictRow) returns (common.v1.EmptyResponse);
  rpc deleteProductDictRow(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc updateProductDictRow(ProductDictRow) returns (common.v1.EmptyResponse);
  rpc getProductDictRowList(ProductDictRowListRequest) returns (ProductDictRowListResponse);

  rpc addProductDictRowTariffMatrixPrice(ProductDictRowTariffMatrixPrice) returns (common.v1.EmptyResponse);
  rpc deleteProductDictRowTariffMatrixPrice(ByIdRequest) returns (common.v1.EmptyResponse);
  rpc updateProductDictRowTariffMatrixPrice(ProductDictRowTariffMatrixPrice) returns (common.v1.EmptyResponse);
  rpc getProductDictRowTariffMatrixPriceList(ProductDictRowTariffMatrixPriceListRequest) returns (ProductDictRowTariffMatrixPriceListResponse);

  rpc getRouteById(ByIdRequest) returns (RouteResponse);
  rpc getTariffById(ByIdRequest) returns (TariffResponse);
  rpc getProductById(ByIdRequest) returns (ProductResponse);
  rpc getStationById(ByIdRequest) returns (StationResponse);
  rpc getTransportById(ByIdRequest) returns (TransportResponse);

  // Project management methods
  rpc createProject(CreateProjectRequest) returns (common.v1.CreateResponse);
  rpc getProjectList(ProjectListRequest) returns (ProjectListResponse);
  rpc getProjectById(common.v1.ByIdRequest) returns (ProjectResponse);
  rpc updateProjectStatus(UpdateProjectStatusRequest) returns (common.v1.EmptyResponse);
}

message RoleListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    RoleResult result = 2;
  }
}

message RoleResult {
  repeated Role roles = 1;
}

message Role{
  string id = 1;
  string name = 2;
  string description = 3;
}

message EmployeeFilters{
  optional string name = 1;
  optional string surname = 2;
  optional string middleName = 3;
  optional string fio = 4;
  optional string organizationId = 5;
  repeated string employeeId = 6;
}

message EmployeeListRequest{
  optional EmployeeFilters filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message EmployeeListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    EmployeeList result = 2;
  }
}

message EmployeeList{
  optional common.v1.PaginationResponse pagination = 1;
  repeated Employee employee = 2;
}

message Employee{
  optional string profileId = 1;
  string organizationId = 2;          // id организации
  string role = 3;                    // роль
  string surname = 4;                 // Фамилия
  string name = 5;                    // Имя
  optional string middleName = 6;     // Отчество
  optional string phone = 7;
  optional string email = 8;
  google.protobuf.Timestamp birthDay = 9;
  optional string seriesAndNumberPassport = 10;
  optional google.protobuf.Timestamp issueDatePassport = 11;
  bool gender = 12;
  optional string photoId = 13;
  optional string language = 14;
  optional string personalNumber = 15;// Табельный номер
  optional string pin = 16;       // Пин
  bool enabled = 17;                  // активный
  optional string login = 18;
  optional string password = 19;
}

message EmployeeRequest{
  Employee employee = 1;
}

message ByIdRequest {
  string id = 1;
}

message RouteFilter {
  optional string orgId = 1;
  optional string name = 2;
  optional string number = 4;
}

message RouteListRequest {
  optional RouteFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message TariffFilter {
}

message TariffListRequest {
  optional TariffFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}


message ProductFilter{}

message ProductListRequest {
  optional ProductFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}


message StationFilter{
  optional string name = 1;
}

message StationListRequest {
  optional StationFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}


message TransportFilter {
  optional string number = 2;
}

message TransportListRequest {
  optional TransportFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message RouteStation {
  optional string id = 1;
  string routeId = 2;
  string stationId = 3;
  int64 pos = 4;
}

message RouteStationFilter{
  optional string routeId = 1;
  optional string stationId = 2;
}

message RouteStationListRequest {
  optional RouteStationFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message RouteStationListResult{
  optional RouteStationFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
  repeated RouteStation stations = 3;
}

message RouteStationListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    RouteStationListResult result = 2;
  }
}

message RouteConstraintFilter{
  optional string routeId = 1;
}

message RouteConstraintListRequest{
  optional RouteConstraintFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message RouteConstraintListResult{
  optional RouteConstraintFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
  repeated common.pro.Constraint constraint = 3;
}

message RouteConstraintListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    RouteConstraintListResult result = 2;
  }
}

message TariffConstraintFilter{
  optional string tariffId = 1;
}

message TariffConstraintListRequest{
  optional TariffConstraintFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message TariffConstraintListResult{
  optional TariffConstraintFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
  repeated common.pro.Constraint constraint = 3;
}

message TariffConstraintListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    TariffConstraintListResult result = 2;
  }
}

message VehicleConstraintFilter{
  optional string vehicleId = 1;
}

message VehicleConstraintListRequest{
  optional VehicleConstraintFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message VehicleConstraintListResult{
  optional VehicleConstraintFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
  repeated common.pro.Constraint constraint = 3;
}

message VehicleConstraintListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    VehicleConstraintListResult result = 2;
  }
}

message ProductDictRow {
  optional string id = 1;
  string productId = 2;
  string tariffId = 3;
  string paymentMethodId = 4;
  bool isFixPrice = 5;
  optional int64 price = 6;
  optional string tags = 7;
  repeated ProductDictRowTariffMatrixPrice matrix = 8;
}

message ProductDictRowFilter{
  string productId = 1;
  string tariffId = 2;
  bool isFixPrice = 3;
}

message ProductDictRowListRequest{
  optional ProductDictRowFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message ProductDictRowListResult{
  optional ProductDictRowFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
  repeated common.pro.Constraint constraint = 3;
}

message ProductDictRowListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    ProductDictRowListResult result = 2;
  }
}


message ProductDictRowTariffMatrixPrice {
  optional string id = 1;
  string stationFromId = 2;
  string stationToId = 3;
  int64 amount = 4;

}

message ProductDictRowTariffMatrixPriceFilter{
  optional string stationFromId = 1;
  optional string stationToId = 2;
}

message ProductDictRowTariffMatrixPriceListRequest{
  optional ProductDictRowTariffMatrixPriceFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message ProductDictRowTariffMatrixPriceListResult{
  optional ProductDictRowTariffMatrixPriceFilter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
  repeated common.pro.Constraint constraint = 3;
}

message ProductDictRowTariffMatrixPriceListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    ProductDictRowTariffMatrixPriceListResult result = 2;
  }
}

message RouteResponse{
  oneof response {
    common.v1.OperationError error = 1;
    common.pro.Route result = 2;
  }
}

message TariffResponse{
  oneof response {
    common.v1.OperationError error = 1;
    common.pro.Tariff result = 2;
  }
}

message ProductResponse{
  oneof response {
    common.v1.OperationError error = 1;
    common.pro.Product result = 2;
  }
}

message StationResponse{
  oneof response {
    common.v1.OperationError error = 1;
    common.pro.Station result = 2;
  }
}

message TransportResponse{
  oneof response {
    common.v1.OperationError error = 1;
    common.pro.Transport result = 2;
  }
}

message EmployeeResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Employee result = 2;
  }
}

// Project management messages
enum ProjectStatus {
  PROJECT_STATUS_UNSPECIFIED = 0;
  TEST = 1;
  DEMO = 2;
  ACTIVE = 3;
  ARCHIVE = 4;
}

message Project {
  optional string id = 1;
  string name = 2;
  ProjectStatus status = 3;
  google.protobuf.Timestamp startDate = 4;
  google.protobuf.Timestamp endDate = 5;
  string contractId = 6;
  string contractNumber = 7;
  optional string description = 8;
  optional string region = 9;
}

message CreateProjectRequest {
  string name = 1;
  google.protobuf.Timestamp startDate = 2;
  google.protobuf.Timestamp endDate = 3;
  string contractId = 4;
  string contractNumber = 5;
  optional string description = 6;
  optional string region = 7;
}

message ProjectFilters {
  optional string name = 1;
  optional ProjectStatus status = 2;
  optional string region = 3;
  optional string contractId = 4;
}

message ProjectListRequest {
  optional ProjectFilters filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message ProjectListResult {
  repeated Project projects = 1;
  optional common.v1.PaginationResponse pagination = 2;
}

message ProjectListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    ProjectListResult result = 2;
  }
}

message ProjectResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Project result = 2;
  }
}

message UpdateProjectStatusRequest {
  string projectId = 1;
  ProjectStatus newStatus = 2;
}