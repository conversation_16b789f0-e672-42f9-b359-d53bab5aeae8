package ru.sbertroika.tkp3.pro.processing.input

import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import ru.sbertroika.tkp3.pro.api.model.Operation
import ru.sbertroika.tkp3.pro.processing.util.mapper

class OperationProcessingConsumerTest {

    val mapper = mapper()

    @Test
    fun testParseOperation() {
        val raw = "{\n" +
                "\t\"type\": \"PASS_EMV\",\n" +
                "\t\"terminalSerial\": \"1003352070040230\",\n" +
                "\t\"ern\": 11,\n" +
                "\t\"raw\": \"cGFuPTQzNzc3MjM3NDM0NDc2MTE7cGFyPW51bGw7cnJuPTQzNzc3MjM3NDM0NDc2MTE9MjkxMDIwMTE0MTIxMjA0MDAwMDA7dGFncz05RjI2MDhFOEJGMDMyNTY2NkQwRTUyOUYyNzAxODA5RjEwMTcwNjAxMTIwM0EwMDAwMDBGMDMwMDAwMTAwMDAwMDAwMDAwMDAwMEYxMEQ4NDgwOUYzNjAyMDVFMDgyMDIwMDIwOUYzNzA0NzlFNTQyMkU5NTA1MDAwMDAwMDAwMDlBMDMyMzEyMDY5QzAxMDA5RjAyMDYwMDAwMDAwMDMwMDA1RjJBMDIwNjQzOUYxQTAyMDY0MzVGMzQwMTAyOUYzMzAzMDAyOEM4OUYzNDAzMUYwMDAyOUY2RTA0MjA3MDAwMDA5RjAzMDYwMDAwMDAwMDAwMDA4NDA3QTAwMDAwMDAwMzEwMTA1NzEzNDM3NzcyMzc0MzQ0NzYxMUQyOTEwMjAxMTQxMjEyMDQwMDAwMEY7ZXhwPTEwLzI5\",\n" +
                "\t\"rawType\": \"CB64\",\n" +
                "\t\"tickets\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"ticketSeries\": \"0-1-1-1\",\n" +
                "\t\t\t\"ticketNumber\": \"1701-8255-9915-9\",\n" +
                "\t\t\t\"createdAt\": \"2023-12-06T01:19:59Z\",\n" +
                "\t\t\t\"manifest\": \"test-2023-10-23\",\n" +
                "\t\t\t\"manifestVersion\": 1,\n" +
                "\t\t\t\"shiftNumber\": 1,\n" +
                "\t\t\t\"serviceId\": \"\",\n" +
                "\t\t\t\"tariffId\": \"54538da7-2468-4faf-beb1-b1a885d7160c\"\n" +
                "\t\t}\n" +
                "\t]\n" +
                "}"
        try {
            val operation = mapper.readValue<Operation>(raw)
            println(operation)
            Assertions.assertNotNull(operation)
        } catch (e: Exception) {
            Assertions.fail("Error parse", e)
        }
    }
}