package ru.sbertroika.tkp3.pro.processing.input

import arrow.core.Either
import arrow.core.left
import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.producer.ProducerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.core.ProducerFactory
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.pro.api.model.Operation
import ru.sbertroika.tkp3.pro.api.model.OperationType
import ru.sbertroika.tkp3.pro.processing.ShiftOpenNotFound
import ru.sbertroika.tkp3.pro.processing.service.OperationService
import ru.sbertroika.tkp3.pro.processing.util.mapper
import java.time.Duration

@Component
class OperationProcessingConsumer(
    kafkaProducerFactory: ProducerFactory<String, Any>,

    @Value("\${spring.kafka.pro_error_out_topic}")
    private val errorTopic: String,

    @Qualifier("emv")
    private val emvOperationService: OperationService,
    @Qualifier("troika")
    private val troikaOperationService: OperationService,
    @Qualifier("cash")
    private val cashOperationService: OperationService,
    @Qualifier("abt")
    private val abtOperationService: OperationService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()

    @KafkaListener(groupId = "\${spring.kafka.pro_tickets_processing_in_group}", topics = ["\${spring.kafka.pro_in_topic}"])
    fun receive(record: ConsumerRecord<String, String>, acknowledgment: Acknowledgment) = runBlocking {
        try {
            toOperationMessage(record.value())
                .fold(
                    {
                        logger.error("error process operation ${record.key()}: ${it.message}")
                        val out = ProducerRecord<String, Any>(errorTopic, record.key(), mapper.writeValueAsString(it.message))
                        producer.send(out)
                        acknowledgment.acknowledge()
                    },
                    { operation ->
                        when (operation.type) {
                            OperationType.PASS_CASH -> cashOperationService.pass(operation)

                            OperationType.PASS_EMV -> emvOperationService.pass(operation)

                            OperationType.PASS_TROIKA_WALLET,
                            OperationType.PASS_TROIKA_TICKET -> troikaOperationService.pass(operation)

                            OperationType.PASS_ABT_WALLET,
                            OperationType.PASS_ABT_TICKET -> abtOperationService.pass(operation)

                            else -> Error("unknown operation type").left()
                        }.fold(
                            { e ->
                                if (e is ShiftOpenNotFound) {
                                    logger.error("Error find shift open operation ${record.key()}", e)
                                    acknowledgment.nack(Duration.ofSeconds(20))
                                }
                            },
                            {
                                acknowledgment.acknowledge()
                            }
                        )
                    })
        } catch (e: Exception) {
            logger.error("error process transaction ${record.key()}", e)
            acknowledgment.acknowledge()
        }
    }

    private fun toOperationMessage(data: String): Either<Throwable, Operation> = Either.catch {
        mapper.readValue<Operation>(data)
    }
}