package ru.sbertroika.tkp3.pro.processing.service.impl

import arrow.core.Either
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.domain.Sort
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.pro.api.model.ShiftOperation
import ru.sbertroika.tkp3.pro.api.model.ShiftOperationType
import ru.sbertroika.tkp3.pro.processing.ShiftOpenNotFound
import ru.sbertroika.tkp3.pro.processing.service.ShiftService
import java.util.*

@Service
class ShiftServiceImpl(
    @Qualifier("clickhouseR2dbcEntityOperations")
    private val entityTemplate: R2dbcEntityOperations
) : ShiftService {

    override suspend fun findOpenShift(terminalId: UUID, shiftNumber: Long): Either<Throwable, ShiftOperation> = Either.catch {
        val search = mutableListOf<Criteria>()

        search.add(Criteria.where("terminalId").`is`(terminalId))
        search.add(Criteria.where("shiftNumber").`is`(shiftNumber))
        search.add(Criteria.where("type").`is`(ShiftOperationType.OPEN))

        withContext(Dispatchers.IO) {
            entityTemplate.select(ShiftOperation::class.java)
                .matching(
                    Query.query(Criteria.from(search))
                        .sort(Sort.by("createdAt").descending())
                        .limit(1)
                ).all()
                .blockFirst()
        } ?: throw ShiftOpenNotFound()
    }
}