package ru.sbertroika.tkp3.pro.processing.config

import io.r2dbc.spi.ConnectionFactories
import io.r2dbc.spi.ConnectionFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.r2dbc.core.DefaultReactiveDataAccessStrategy
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.data.r2dbc.dialect.PostgresDialect
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories
import org.springframework.r2dbc.core.DatabaseClient

@Configuration
@EnableR2dbcRepositories(
    basePackages = ["ru.sbertroika.tkp3.pro.processing.output.repository"],
    entityOperationsRef = "mainR2dbcEntityOperations"
)
open class MainDataSourceConfiguration(
    @Value("\${spring.r2dbc.url}")
    private val url: String
) {

    @Bean
    @Primary
    @Qualifier("mainConnectionFactory")
    open fun mainConnectionFactory(): ConnectionFactory = ConnectionFactories.get(url)

    @Bean
    @Primary
    @Qualifier("mainR2dbcEntityOperations")
    open fun mainR2dbcEntityOperations(@Qualifier("mainConnectionFactory") connectionFactory: ConnectionFactory): R2dbcEntityOperations {
        val strategy = DefaultReactiveDataAccessStrategy(PostgresDialect.INSTANCE)
        val databaseClient = DatabaseClient.builder()
            .connectionFactory(connectionFactory)
            .build()

        return R2dbcEntityTemplate(databaseClient, strategy)
    }

    @Bean
    @Primary
    @Qualifier("mainDatabaseClient")
    open fun mainDatabaseClient(@Qualifier("mainConnectionFactory") connectionFactory: ConnectionFactory): DatabaseClient {
        return DatabaseClient.builder()
            .connectionFactory(connectionFactory)
            .bindMarkers(PostgresDialect.INSTANCE.bindMarkersFactory)
            .namedParameters(true)
            .build()
    }
}