package ru.sbertroika.tkp3.pro.processing.service.impl

import arrow.core.Either
import org.apache.kafka.clients.producer.ProducerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.abt.api.model.AbtTap
import ru.sbertroika.tkp3.abt.api.model.AbtType
import ru.sbertroika.tkp3.abt.api.model.TransportType
import ru.sbertroika.tkp3.manifest.starter.ManifestService
import ru.sbertroika.tkp3.pro.api.model.Operation
import ru.sbertroika.tkp3.pro.api.model.OperationType
import ru.sbertroika.tkp3.pro.model.Employee
import ru.sbertroika.tkp3.pro.model.PayMethodType
import ru.sbertroika.tkp3.pro.model.TerminalRole
import ru.sbertroika.tkp3.pro.model.Ticket
import ru.sbertroika.tkp3.pro.processing.ShiftOpenNotFound
import ru.sbertroika.tkp3.pro.processing.output.repository.EmployeeRepository
import ru.sbertroika.tkp3.pro.processing.service.OperationService
import ru.sbertroika.tkp3.pro.processing.service.ShiftService
import ru.sbertroika.tkp3.pro.processing.util.mapper
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

@Service("abt")
class AbtOperationService(
    kafkaProducerFactory: ProducerFactory<String, Any>,
    private val shiftService: ShiftService,
    private val employeeRepository: EmployeeRepository,
    private val manifestService: ManifestService,

    @Value("\${spring.kafka.pro_abt_in_topic}")
    private val abtTopic: String,
    @Value("\${spring.kafka.pro_ticket_in_topic}")
    private val ticketTopic: String
) : OperationService {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()

    override suspend fun pass(operation: Operation): Either<Throwable, Unit> = Either.catch {
        //TODO вытащить тип препроцессинга из настроек проекта и другие данные
        val trxId = UUID.randomUUID()

        val firstTicket = operation.tickets.first()
        val shiftOpenOperation = shiftService.findOpenShift(operation.terminalId, firstTicket.shiftNumber).fold(
            { e ->
                logger.error("Error find shift", e)
                throw ShiftOpenNotFound()
            },
            { it }
        )

        val employee = employeeRepository.findById(shiftOpenOperation.userId)
        if (employee == null) {
            logger.error("Error process operation $operation: employee is null")
            return@catch
        }

        var driver: Employee? = null
        var conductor: Employee? = null
        var cashier: Employee? = null

        when (TerminalRole.findByRole(employee.role)) {
            TerminalRole.DRIVER -> driver = employee
            TerminalRole.CASHIER -> cashier = employee
            TerminalRole.CONDUCTOR -> conductor = employee
            else -> {}
        }

        if (shiftOpenOperation.routeId.isNullOrEmpty()) {
            logger.error("Error process operation $operation: routeId is null")
            return@catch
        }

        if (shiftOpenOperation.vehicleId.isNullOrEmpty()) {
            logger.error("Error process operation $operation: vehicleId is null")
            return@catch
        }

        val manifest = manifestService.getManifest(firstTicket.manifest, firstTicket.manifestVersion.toInt()).fold(
            {
                logger.error("Error get manifest id=${firstTicket.manifest}, ver=${firstTicket.manifestVersion}", it)
                throw it
            },
            { it }
        )

        operation.tickets.forEach { t ->
            pushTicket(
                Ticket(
                    ticketId = UUID.randomUUID(),
                    projectId = manifest.projectId,
                    orgId = shiftOpenOperation.orgId,
                    createdAt = t.createdAt,
                    recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
                    ticketSeries = t.ticketSeries,
                    ticketNumber = t.ticketNumber,
                    productId = if (t.productId.isEmpty()) null else UUID.fromString(t.productId),
                    productVersion = manifest.service.pro?.dict?.product?.firstOrNull { it.id.toString() == t.productId }?.version ?: 0,
                    tariffId = UUID.fromString(t.tariffId),
                    tariffVersion = manifest.service.pro?.dict?.tariff?.firstOrNull { it.id.toString() == t.tariffId }?.version ?: 0,
                    routeId = UUID.fromString(shiftOpenOperation.routeId),
                    routeVersion = shiftOpenOperation.routeVersion,
                    vehicleId = UUID.fromString(shiftOpenOperation.vehicleId),
                    vehicleVersion = shiftOpenOperation.vehicleVersion,
                    driverId = driver?.profileId,
                    conductorId = conductor?.profileId,
                    cashierId = cashier?.profileId,
                    stationFromId = if (t.stationFromId.isNullOrEmpty()) null else UUID.fromString(t.stationFromId),
                    stationFromVersion = manifest.service.pro?.dict?.station?.firstOrNull { it.id.toString() == t.stationFromId }?.version ?: 0,
                    stationToId = if (t.stationToId.isNullOrEmpty()) null else UUID.fromString(t.stationToId),
                    stationToVersion = manifest.service.pro?.dict?.station?.firstOrNull { it.id.toString() == t.stationToId }?.version ?: 0,

                    trxId = trxId,
                    amount = t.amount.toInt(),
                    terminalSerial = operation.terminalSerial,
                    terminalId = operation.terminalId,
                    shiftNum = t.shiftNumber.toInt(),
                    ern = operation.ern,
                    payMethodType = if (operation.type == OperationType.PASS_TROIKA_WALLET) PayMethodType.TROIKA_WALLET else PayMethodType.TROIKA_TICKET,
                    tags = "abt"
                )
            )
        }

        pushAbtTap(
            AbtTap(
                trxId = trxId,
                projectId = manifest.projectId,
                orgId = shiftOpenOperation.orgId,
                createdAt = operation.tickets.first().createdAt,
                type = when (operation.type) {
                    OperationType.PASS_ABT_WALLET -> AbtType.WALLET
                    OperationType.PASS_ABT_TICKET -> AbtType.TICKET
                    else -> AbtType.TICKET
                },
                ern = operation.ern,
                terminalId = operation.terminalId,
                terminalSerial = operation.terminalSerial,
                amount = operation.tickets.sumOf { it.amount }.toInt(),
                shiftNum = operation.tickets.first().shiftNumber.toInt(),
                raw = operation.raw!!,
                templateId = operation.templateId!!,
                templateVersion = manifest.service.proAbt?.dict?.templates?.first { it.id == operation.templateId!! }?.version ?: 0,
                cardUid = operation.cardUid,
                cardHash = operation.cardHash,
                cardHashType = operation.cardHashType,
                transportType = TransportType.valueOf(manifest.service.pro?.dict?.transport!!.first { it.id.toString() == shiftOpenOperation.vehicleId }.type.name)
            )
        )
    }

    private fun pushAbtTap(tap: AbtTap) = Either.catch {
        val out = ProducerRecord<String, Any>(abtTopic, tap.trxId.toString(), mapper.writeValueAsString(tap))
        producer.send(out)
    }

    private fun pushTicket(ticket: Ticket) = Either.catch {
        val out = ProducerRecord<String, Any>(ticketTopic, ticket.ticketId.toString(), mapper.writeValueAsString(ticket))
        producer.send(out)
    }
}