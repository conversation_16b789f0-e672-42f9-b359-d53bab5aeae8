package ru.sbertroika.tkp3.pro.processing.output.repository

import io.r2dbc.spi.Readable
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOne
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.pro.model.Employee
import ru.sbertroika.tkp3.pro.model.EmployeePK
import java.util.*

interface EmployeeCRUDRepository : CoroutineCrudRepository<Employee, EmployeePK>

@Component
class EmployeeRepository(
    @Qualifier("mainDatabaseClient")
    override val dbClient: DatabaseClient,
    override val repository: EmployeeCRUDRepository
) : AbstractSettingsRepository<Employee, EmployeePK>(dbClient, repository) {
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) \n"
    else "SELECT * \n") +
            "FROM employee o\n" +
            "INNER JOIN (\n" +
            "    SELECT e_profile_id, MAX(e_version) vers\n" +
            "    FROM employee\n" +
            "    GROUP BY e_profile_id\n" +
            ") o2 ON o.e_profile_id = o2.e_profile_id AND o.e_version = o2.vers AND  o.e_is_deleted = false"

    override fun toEntity(t: Readable): Employee = Employee(
        profileId = t.get("e_profile_id") as UUID,
        version = t.get("e_version") as Int,
        organizationId = t.get("e_profile_id") as UUID,
        role = t.get("e_role") as String,
        name = t.get("e_name") as String,
        surname = t.get("e_surname") as String,
        middleName = t.get("e_middle_name") as String?,
        gender = t.get("e_gender", Boolean::class.java)
    )

    override suspend fun findById(id: String): Employee? {
        return dbClient.sql("${getQuery()} AND o.e_profile_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }
}

abstract class AbstractSettingsRepository<E, K>(
    open val dbClient: DatabaseClient,
    open val repository: CoroutineCrudRepository<E, K>
) {
    abstract fun getQuery(isCount: Boolean = false): String
    abstract fun toEntity(t: Readable): E
    protected fun getPageRequest(page: Int, limit: Int): String = "${getQuery()} OFFSET ${page * limit} LIMIT $limit"
    abstract suspend fun findById(id: String): E?
    suspend fun countAll(): Int {
        return dbClient.sql(getQuery(true)).map { t ->
            (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    suspend fun save(entity: E) = repository.save(entity)
}