CREATE TABLE ticket
(
    project_id      UUID comment 'Идентификатор проекта',
    org_id          UUID comment 'Идентификатор организации',
    ticket_id       UUID comment 'Идентификатор билета',
    created_at      DateTime('UTC') comment 'Время формирования билета на терминале',
    record_at       DateTime('UTC') comment 'Время формирования билета на сервере',

    ticket_series   String comment 'Серия билета',
    ticket_number   String comment 'Номер билета',

    pr_id           UUID comment 'Идентификатор продукта',
    pr_ver          UInt16 comment 'Версия записи о продукте',
    t_id            UUID comment 'Идентификатор тарифа',
    t_ver           UInt16 comment 'Версия записи о тарифе',
    r_id            UUID comment 'Идентификатор маршрута',
    r_ver           UInt16 comment 'Версия записи о маршруте',
    v_id            UUID comment 'Идентификатор Т/С',
    v_ver           UInt16 comment 'Версия записи о Т/С',

    driver_id       Nullable(UUID) comment 'Идентификатор водителя',
    conductor_id    Nullable(UUID) comment 'Идентификатор кондуктора',
    cashier_id      Nullable(UUID) comment 'Идентификатор кассира',

    st_from_id      Nullable(UUID) comment 'Станция входа',
    st_from_ver     Nullable(UInt16) comment 'Версия записи о станции входа',
    st_to_id        Nullable(UUID) comment 'Станция выхода',
    st_to_ver       Nullable(UInt16) comment 'Версия записи о станции выхода',

    a_id            Nullable(UUID) comment 'Идентификатор абонемента',
    a_ver           Nullable(UInt16) comment 'Версия записи об абонементе',
    wa_id           Nullable(UUID) comment 'Идентификатор кошелька',
    wa_ver          Nullable(UInt16) comment 'Версия записи о кошельке',

    trx_id          Nullable(String) comment 'Идентификатор транзакции (заказа)',

    amount          UInt16 comment 'Cтоимость билета',

    terminal_id     UUID comment 'Идентификатор терминала',
    terminal_serial String comment 'Заводской номер терминала',
    tid             Nullable(String) comment 'Банковский идентификатор терминала',
    shift_num       UInt16 comment 'Номер смены на терминале',
    ern             UInt16 comment 'Единый номер операции на терминале (в рамках смены)',

    tags            Nullable(String) comment 'Тэги'
) engine = MergeTree PARTITION BY (project_id, org_id, toYYYYMM(created_at))
--         ORDER BY (created_at)
        PRIMARY KEY (ticket_series, ticket_number)
        SETTINGS index_granularity = 8192;

CREATE TABLE kafka_ticket
(
    ticketId       String,
    projectId      String,
    orgId          String,
    createdAt      String,
    recordAt       String,
    ticketSeries   String,
    ticketNumber   String,
    productId      String,
    productVersion UInt16,
    tariffId       String,
    tariffVersion  UInt16,
    routeId        String,
    routeVersion   UInt16,
    vehicleId      String,
    vehicleVersion UInt16,
    driverId            Nullable(String),
    conductorId         Nullable(String),
    cashierId           Nullable(String),
    stationFromId       Nullable(String),
    stationFromVersion  Nullable(UInt16),
    stationToId         Nullable(String),
    stationToVersion    Nullable(UInt16),
    trxId               Nullable(String),
    amount              UInt32,
    terminalId          String,
    terminalSerial      String,
    tid                 Nullable(String),
    shiftNum            UInt32,
    ern                 UInt32,
    tags                Nullable(String),
    payMethodType       String
)
    ENGINE = Kafka('************:9092,***********:9092,************:9092', 'PRO.INTERNAL.TICKET', 'pro_internal_ticket_loader',
             'JSONEachRow') settings kafka_thread_per_consumer = 1, kafka_num_consumers = 1, kafka_skip_broken_messages = 1;

CREATE MATERIALIZED VIEW ticket_mv TO ticket as
SELECT toUUID(projectId)                         as project_id,
       toUUID(orgId)                             as org_id,
       toUUID(ticketId)                          as ticket_id,
       parseDateTimeBestEffort(createdAt, 'UTC') as created_at,
       parseDateTimeBestEffort(recordAt, 'UTC')  as record_at,
       ticketSeries                              as ticket_series,
       ticketNumber                              as ticket_number,
       productId                                 as pr_id,
       productVersion                            as pr_ver,
       tariffId                                  as t_id,
       tariffVersion                             as t_ver,
       routeId                                   as r_id,
       routeVersion                              as r_ver,
       vehicleId                                 as v_id,
       vehicleVersion                            as v_ver,
       toUUIDOrNull(driverId)                    as driver_id,
       toUUIDOrNull(conductorId)                 as conductor_id,
       toUUIDOrNull(cashierId)                   as cashier_id,
       toUUIDOrNull(stationFromId)               as st_from_id,
       stationFromVersion                        as st_from_ver,
       toUUIDOrNull(stationToId)                 as st_to_id,
       stationToVersion                          as st_to_ver,
       toUUIDOrNull(trxId)                       as trx_id,
       amount                                    as amount,
       toUUIDOrNull(terminalId)                  as terminal_id,
       terminalSerial                            as terminal_serial,
       tid                                       as tid,
       shiftNum                                  as shift_num,
       ern                                       as ern,
       tags                                      as tags,
       payMethodType                             as pay_method_type
FROM kafka_ticket;

create table shift_operation
(
    created_at      DateTime('UTC') comment 'Время формирования транзакции на терминале',
    record_at       DateTime('UTC') comment 'Время формирования транзакции на сервере',
    type            Enum8('OPEN' = 0, 'CLOSE' = 1) comment 'Тип операции',
    terminal_serial String comment 'Заводской номер терминала',
    terminal_id      UUID comment 'Идентификатор терминала',
    shift_num       UInt16 comment 'Номер смены на терминале',
    ern             UInt16 comment 'Единый номер операции на терминале (в рамках смены)',
    user_id         UUID comment 'Идентификатор пользователя открывшего/закрывшего смену',
    manifest        Nullable(String) comment 'Идентификатор манифеста',
    manifest_ver    Nullable(UInt16) comment 'Версия манифеста',
    r_id            Nullable(UUID) comment 'Идентификатор маршрута',
    r_ver           Nullable(UInt16) comment 'Версия записи о маршруте',
    v_id            Nullable(UUID) comment 'Идентификатор Т/С',
    v_ver           Nullable(UInt16) comment 'Версия записи о Т/С'
)
    engine = MergeTree PARTITION BY toYYYYMM(record_at)
        ORDER BY (terminal_serial, shift_num, created_at)
        SETTINGS index_granularity = 8192;

CREATE TABLE kafka_shift_operation
(
    recordAt        String,
    createdAt       String,
    projectId       String,
    orgId           String,
    type            String,
    terminalId      String,
    terminalSerial  String,
    shiftNumber     UInt16,
    ern             UInt16,
    userId          String,
    manifest        Nullable(String),
    manifestVersion Nullable(String),
    routeId         Nullable(String),
    routeVersion    Nullable(UInt16),
    vehicleId       Nullable(String),
    vehicleVersion  Nullable(UInt16)
)
    ENGINE = Kafka('************:9092,***********:9092,************:9092', 'PRO.OPERATION.SHIFT.IN', 'shift_operation_loader',
             'JSONEachRow') settings kafka_thread_per_consumer = 1, kafka_num_consumers = 1;

CREATE MATERIALIZED VIEW shift_operation_mv TO shift_operation as
SELECT parseDateTimeBestEffort(recordAt, 'UTC')  as record_at,
       parseDateTimeBestEffort(createdAt, 'UTC') as created_at,
       type,
       toUUID(terminalId)                        as terminal_id,
       toUUID(projectId)                         as project_id,
       toUUID(orgId)                             as org_id,
       terminalSerial                            as terminal_serial,
       shiftNumber                               as shift_num,
       ern,
       toUUID(userId)                            as user_id,
       manifest,
       manifestVersion                           as manifest_ver,
       routeId                                   as r_id,
       routeVersion                              as r_ver,
       vehicleId                                 as v_id,
       vehicleVersion                            as v_ver
FROM kafka_shift_operation;

CREATE DICTIONARY vehicle_dict(
    vh_id UUID,
    vh_version UInt16,
    vh_number String,
    vh_type String
    )
    PRIMARY KEY vh_id, vh_version
    SOURCE(PostgreSQL(
        port 5432
        host '************'
        user 'clickhouse'
        password ''
        db 'pro'
        query 'SELECT vh_id, vh_version, vh_number, vh_type FROM vehicle'
        ))
    LAYOUT(COMPLEX_KEY_HASHED())
    LIFETIME(MIN 300 MAX 360);

CREATE DICTIONARY product_dict(
    p_id UUID,
    p_version UInt16,
    p_name String
    )
    PRIMARY KEY p_id, p_version
    SOURCE(PostgreSQL(
        port 5432
        host '************'
        user 'clickhouse'
        password ''
        db 'pro'
        query 'SELECT p_id, p_version, p_name FROM product'
        ))
    LAYOUT(COMPLEX_KEY_HASHED())
    LIFETIME(MIN 300 MAX 360);

CREATE DICTIONARY employee_dict(
    e_id UUID,
    e_version UInt16,
    e_role String,
    e_surname String,
    e_name String,
    e_middle_name String,
    e_organization_id UUID,
    e_phone String,
    e_email String,
    e_enabled Bool,
    e_is_deleted Bool
    )
    PRIMARY KEY e_id, e_version
    SOURCE(PostgreSQL(
        port 5432
        host '************'
        user 'clickhouse'
        password ''
        db 'pro'
        query 'SELECT e_profile_id as e_id, e_version, e_role, e_surname, e_name, e_middle_name, e_organization_id, e_phone, e_email, e_enabled, e_is_deleted FROM employee'
        ))
    LAYOUT(COMPLEX_KEY_HASHED())
    LIFETIME(MIN 300 MAX 360);

CREATE DICTIONARY tariff_dict(
    t_id UUID,
    t_version UInt16,
    t_name String
    )
    PRIMARY KEY t_id, t_version
    SOURCE(PostgreSQL(
        port 5432
        host '************'
        user 'clickhouse'
        password ''
        db 'pro'
        query 'SELECT t_id, t_version, t_name FROM tariff'
        ))
    LAYOUT(COMPLEX_KEY_HASHED())
    LIFETIME(MIN 300 MAX 360);

CREATE DICTIONARY prod.route_dict(
    r_id UUID,
    r_version UInt16,
    r_index UInt32,
    r_name String,
    r_number String,
    r_type String,
    r_scheme String
    )
    PRIMARY KEY r_id, r_version
    SOURCE(PostgreSQL(
        port 5432
        host '************'
        user 'clickhouse'
        password ''
        db 'pro'
        query 'SELECT r_id, r_version, r_index, r_name, r_number, r_type, r_scheme FROM route'
        ))
    LAYOUT(COMPLEX_KEY_HASHED())
    LIFETIME(MIN 300 MAX 360);

CREATE DICTIONARY station_dict(
    st_id UUID,
    st_version UInt16,
    p_id Nullable(UUID),
    tz_id Nullable(UUID),
    st_name String,
    lat Float64,
    long Float64
    )
    PRIMARY KEY st_id, st_version
    SOURCE(PostgreSQL(
        port 5432
        host '************'
        user 'clickhouse'
        password ''
        db 'pro'
        query 'SELECT st_id, st_version, p_id, tz_id, st_name, lat, long FROM station'
        ))
    LAYOUT(COMPLEX_KEY_HASHED())
    LIFETIME(MIN 300 MAX 360);

alter table prod.stop_list_journal modify column hash_type Enum8('SHA256' = 0, 'PAR' = 1, 'HMAC_SHA1' = 2, 'HMAC_SHA256' = 3, 'HMAC_SHA256_SHA256' = 4, 'STRIBOK512' = 5, 'BIN' = 6);
alter table prod.emv_trx_auth modify column adjustment_amount Nullable(UInt32);
alter table ticket modify column trx_id UUID;

alter table prod.stop_list_journal modify column hash_type Enum8('SHA256' = 0, 'PAR' = 1, 'HMAC_SHA1' = 2, 'HMAC_SHA256' = 3, 'HMAC_SHA256_SHA256' = 4, 'STRIBOK512' = 5, 'BIN' = 6);

alter table prod.ticket add column pay_method_type Enum8('CASH' = 0, 'EMV' = 1, 'TROIKA_TICKET' = 2, 'TROIKA_WALLET' = 3, 'ABT_TICKET' = 4, 'ABT_WALLET' = 5) comment 'Тип способа оплаты';

alter table prod.ticket update pay_method_type = 'EMV' where tags = 'emv';
alter table prod.ticket update pay_method_type = 'TROIKA_WALLET' where tags = 'troika';

alter table shift_operation add column project_id UUID;
alter table shift_operation add column org_id UUID;

alter table shift_operation update project_id = '5a097adc-a337-428a-958c-79e85a44ebb8' where 1=1;
alter table shift_operation update org_id = '43db9de7-72ec-4eae-8978-8aef1c46873a' where 1=1;

