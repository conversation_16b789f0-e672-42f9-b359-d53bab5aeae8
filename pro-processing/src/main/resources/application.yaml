spring:
  application:
    name: pro-processing
  main:
    allow-bean-definition-overriding: true

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    pro_in_topic: ${PRO_IN_TOPIC:PRO.OPERATION.IN}
    pro_emv_in_topic: ${PRO_INTERNAL_EMV_TOPIC:PRO.INTERNAL.EMV}
    pro_troika_in_topic: ${PRO_INTERNAL_TROIKA_TOPIC:PRO.INTERNAL.TROIKA}
    pro_cash_in_topic: ${PRO_INTERNAL_CASH_TOPIC:PRO.INTERNAL.CASH}
    pro_abt_in_topic: ${PRO_INTERNAL_ABT_TOPIC:PRO.INTERNAL.ABT}
    pro_ticket_in_topic: ${PRO_INTERNAL_EMV_TOPIC:PRO.INTERNAL.TICKET}
    pro_error_out_topic: ${PRO_ERROR_OUT_TOPIC:PRO.ERROR.OUT}
    pro_tickets_processing_in_group: ${PRO_TICKETS_PROCESSING_IN_GROUP:pro_operation_processing_in_group}

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/pro}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  clickhouse:
    url: r2dbc:clickhouse:${CLICKHOUSE_URL:http://click-0.tkp2.prod:8123/dev}

server:
  port: 8080

s3:
  url: ${S3_URL}
  access_key_id: ${S3_ACCESS_KEY_ID}
  secret_access_key: ${S3_SECRET_ACCESS_KEY}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest}

zookeeper:
  nodes: ${ZOOKEEPER_NODES:localhost:2181,localhost:2182,localhost:2183}
  namespace: ${ZOOKEEPER_NAMESPACE:tkp3}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness:
          include: '*'
        readiness:
          include: '*'
