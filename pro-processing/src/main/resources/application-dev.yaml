spring:
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:10.4.32.25:9092;10.4.32.140:9092;10.4.32.88:9092}
#    bootstrap-servers: ${KAFKA_SERVERS:10.4.130.168:9092,10.4.130.86:9092,10.4.130.132:9092} #prod
  r2dbc:
    url: r2dbc:pool:${R2DB_URL:**************************************************************/develop_pro}

s3:
  url: ${S3_URL:https://obs.ru-moscow-1.hc.sbercloud.ru}
  access_key_id: ${S3_ACCESS_KEY_ID:XSRWBOPX5DDYCOYFQJUZ}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:CSUFFlymFtfovFI0hlyEyyyqX5isVcYFHvGKsHjV}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest-dev}

zookeeper:
  nodes: ${ZOOKEEPER_NODES:10.4.45.220:2181,10.4.45.19:2181,10.4.45.163:2181}
  namespace: ${ZOOKEEPER_NAMESPACE:tkp3-dev}

server:
  port: 8886