{"version": 3, "sources": ["../../node_modules/src/toasteventbus/ToastEventBus.js", "../../node_modules/src/usetoast/UseToast.js", "../../node_modules/src/toastservice/ToastService.js"], "sourcesContent": ["import { EventBus } from '@primeuix/utils/eventbus';\n\nexport default EventBus();\n", "import { inject } from 'vue';\n\nexport const PrimeVueToastSymbol = Symbol();\n\nexport function useToast() {\n    const PrimeVueToast = inject(PrimeVueToastSymbol);\n\n    if (!PrimeVueToast) {\n        throw new Error('No PrimeVue Toast provided!');\n    }\n\n    return PrimeVueToast;\n}\n", "import ToastEventBus from 'primevue/toasteventbus';\nimport { PrimeVueToastSymbol } from 'primevue/usetoast';\n\nexport default {\n    install: (app) => {\n        const ToastService = {\n            add: (message) => {\n                ToastEventBus.emit('add', message);\n            },\n            remove: (message) => {\n                ToastEventBus.emit('remove', message);\n            },\n            removeGroup: (group) => {\n                ToastEventBus.emit('remove-group', group);\n            },\n            removeAllGroups: () => {\n                ToastEventBus.emit('remove-all-groups');\n            }\n        };\n\n        app.config.globalProperties.$toast = ToastService;\n        app.provide(PrimeVueToastSymbol, ToastService);\n    }\n};\n"], "mappings": ";;;;;;AAEA,IAAA,gBAAeA,SAAQ;;;ACAVC,IAAAA,sBAAsBC,OAAM;;;ACCzC,IAAA,eAAe;EACXC,SAAS,SAATA,QAAUC,KAAQ;AACd,QAAMC,gBAAe;MACjBC,KAAK,SAALA,IAAMC,SAAY;AACdC,sBAAcC,KAAK,OAAOF,OAAO;;MAErCG,QAAQ,SAARA,OAASH,SAAY;AACjBC,sBAAcC,KAAK,UAAUF,OAAO;;MAExCI,aAAa,SAAbA,YAAcC,OAAU;AACpBJ,sBAAcC,KAAK,gBAAgBG,KAAK;;MAE5CC,iBAAiB,SAAjBA,kBAAuB;AACnBL,sBAAcC,KAAK,mBAAmB;MAC1C;;AAGJL,QAAIU,OAAOC,iBAAiBC,SAASX;AACrCD,QAAIa,QAAQC,qBAAqBb,aAAY;EACjD;AACJ;", "names": ["EventBus", "PrimeVueToastSymbol", "Symbol", "install", "app", "ToastService", "add", "message", "ToastEventBus", "emit", "remove", "removeGroup", "group", "removeAllGroups", "config", "globalProperties", "$toast", "provide", "PrimeVueToastSymbol"]}