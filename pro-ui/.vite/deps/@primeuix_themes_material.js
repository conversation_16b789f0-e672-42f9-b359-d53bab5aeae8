// node_modules/@primeuix/themes/material/accordion/index.mjs
var o = { transitionDuration: "{transition.duration}" };
var r = { borderWidth: "0", borderColor: "{content.border.color}" };
var t = { color: "{text.color}", hoverColor: "{text.color}", activeColor: "{text.color}", padding: "1.25rem", fontWeight: "600", borderRadius: "0", borderWidth: "0", borderColor: "{content.border.color}", background: "{content.background}", hoverBackground: "{content.hover.background}", activeBackground: "{content.background}", activeHoverBackground: "{content.background}", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" }, toggleIcon: { color: "{text.muted.color}", hoverColor: "{text.muted.color}", activeColor: "{text.muted.color}", activeHoverColor: "{text.muted.color}" }, first: { topBorderRadius: "{content.border.radius}", borderWidth: "0" }, last: { bottomBorderRadius: "{content.border.radius}", activeBottomBorderRadius: "0" } };
var n = { borderWidth: "0", borderColor: "{content.border.color}", background: "{content.background}", color: "{text.color}", padding: "0 1.25rem 1.25rem 1.25rem" };
var css = ({ dt: o88 }) => `
.p-accordionpanel {
    box-shadow: 0 3px 1px -2px rgba(0,0,0,.2), 0 2px 2px 0 rgba(0,0,0,.14), 0 1px 5px 0 rgba(0,0,0,.12);
    transition: margin ${o88("accordion.transition.duration")};
}

.p-accordionpanel-active {
    margin: 1rem 0;
}

.p-accordionpanel:first-child {
    border-top-left-radius: ${o88("content.border.radius")};
    border-top-right-radius: ${o88("content.border.radius")};
    margin-top: 0;
}

.p-accordionpanel:last-child {
    border-bottom-left-radius: ${o88("content.border.radius")};
    border-bottom-right-radius: ${o88("content.border.radius")};
    margin-bottom: 0;
}

.p-accordionpanel:not(.p-disabled) .p-accordionheader:focus-visible {
    background: ${o88("navigation.item.active.background")};
}
`;
var e = { root: o, panel: r, header: t, content: n, css };

// node_modules/@primeuix/themes/material/autocomplete/index.mjs
var o2 = { background: "{form.field.background}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", filledHoverBackground: "{form.field.filled.hover.background}", filledFocusBackground: "{form.field.filled.focus.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", color: "{form.field.color}", disabledColor: "{form.field.disabled.color}", placeholderColor: "{form.field.placeholder.color}", shadow: "{form.field.shadow}", paddingX: "{form.field.padding.x}", paddingY: "{form.field.padding.y}", borderRadius: "{form.field.border.radius}", focusRing: { width: "{form.field.focus.ring.width}", style: "{form.field.focus.ring.style}", color: "{form.field.focus.ring.color}", offset: "{form.field.focus.ring.offset}", shadow: "{form.field.focus.ring.shadow}" }, transitionDuration: "{form.field.transition.duration}" };
var r2 = { background: "{overlay.select.background}", borderColor: "{overlay.select.border.color}", borderRadius: "{overlay.select.border.radius}", color: "{overlay.select.color}", shadow: "{overlay.select.shadow}" };
var e2 = { padding: "{list.padding}", gap: "{list.gap}" };
var d = { focusBackground: "{list.option.focus.background}", selectedBackground: "{list.option.selected.background}", selectedFocusBackground: "{list.option.selected.focus.background}", color: "{list.option.color}", focusColor: "{list.option.focus.color}", selectedColor: "{list.option.selected.color}", selectedFocusColor: "{list.option.selected.focus.color}", padding: "{list.option.padding}", borderRadius: "{list.option.border.radius}" };
var l = { background: "{list.option.group.background}", color: "{list.option.group.color}", fontWeight: "{list.option.group.font.weight}", padding: "{list.option.group.padding}" };
var t2 = { width: "3rem", sm: { width: "2.5rem" }, lg: { width: "3.5rem" }, borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.border.color}", activeBorderColor: "{form.field.border.color}", borderRadius: "{form.field.border.radius}", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var a = { borderRadius: "{border.radius.sm}" };
var n2 = { padding: "{list.option.padding}" };
var c = { light: { chip: { focusBackground: "{surface.300}", focusColor: "{surface.950}" }, dropdown: { background: "{surface.100}", hoverBackground: "{surface.200}", activeBackground: "{surface.300}", color: "{surface.600}", hoverColor: "{surface.700}", activeColor: "{surface.800}" } }, dark: { chip: { focusBackground: "{surface.600}", focusColor: "{surface.0}" }, dropdown: { background: "{surface.800}", hoverBackground: "{surface.700}", activeBackground: "{surface.600}", color: "{surface.300}", hoverColor: "{surface.200}", activeColor: "{surface.100}" } } };
var css2 = ({ dt: o88 }) => `
.p-autocomplete-dropdown:focus-visible {
    background: ${o88("autocomplete.dropdown.hover.background")};
    border-color: ${o88("autocomplete.dropdown.hover.border.color")};
    color: ${o88("autocomplete.dropdown.hover.color")};
}

.p-variant-filled.p-autocomplete-input-multiple {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o88("autocomplete.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("autocomplete.focus.border.color")}, ${o88("autocomplete.focus.border.color")}), linear-gradient(to bottom, ${o88("autocomplete.border.color")}, ${o88("autocomplete.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-autocomplete:not(.p-disabled):hover .p-variant-filled.p-autocomplete-input-multiple {
    background: ${o88("autocomplete.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("autocomplete.focus.border.color")}, ${o88("autocomplete.focus.border.color")}), linear-gradient(to bottom, ${o88("autocomplete.hover.border.color")}, ${o88("autocomplete.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-autocomplete:not(.p-disabled).p-focus .p-variant-filled.p-autocomplete-input-multiple {
    outline: 0 none;
    background: ${o88("autocomplete.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("autocomplete.focus.border.color")}, ${o88("autocomplete.focus.border.color")}), linear-gradient(to bottom, ${o88("autocomplete.border.color")}, ${o88("autocomplete.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-autocomplete:not(.p-disabled).p-focus:hover .p-variant-filled.p-autocomplete-input-multiple {
    background-image: linear-gradient(to bottom, ${o88("autocomplete.focus.border.color")}, ${o88("autocomplete.focus.border.color")}), linear-gradient(to bottom, ${o88("autocomplete.hover.border.color")}, ${o88("autocomplete.hover.border.color")});
}

.p-autocomplete.p-invalid .p-autocomplete-input-multiple {
    background-image: linear-gradient(to bottom, ${o88("autocomplete.invalid.border.color")}, ${o88("autocomplete.invalid.border.color")}), linear-gradient(to bottom, ${o88("autocomplete.invalid.border.color")}, ${o88("autocomplete.invalid.border.color")});
}

.p-autocomplete.p-invalid.p-focus .p-autocomplete-input-multiple  {
    background-image: linear-gradient(to bottom, ${o88("autocomplete.invalid.border.color")}, ${o88("autocomplete.invalid.border.color")}), linear-gradient(to bottom, ${o88("autocomplete.invalid.border.color")}, ${o88("autocomplete.invalid.border.color")});
}

.p-autocomplete-option {
    transition: none;
}
`;
var i = { root: o2, overlay: r2, list: e2, option: d, optionGroup: l, dropdown: t2, chip: a, emptyMessage: n2, colorScheme: c, css: css2 };

// node_modules/@primeuix/themes/material/avatar/index.mjs
var e3 = { width: "2rem", height: "2rem", fontSize: "1rem", background: "{content.border.color}", color: "{content.color}", borderRadius: "{content.border.radius}" };
var r3 = { size: "1rem" };
var o3 = { borderColor: "{content.background}", offset: "-0.75rem" };
var t3 = { width: "3rem", height: "3rem", fontSize: "1.5rem", icon: { size: "1.5rem" }, group: { offset: "-1rem" } };
var i2 = { width: "4rem", height: "4rem", fontSize: "2rem", icon: { size: "2rem" }, group: { offset: "-1.5rem" } };
var n3 = { root: e3, icon: r3, group: o3, lg: t3, xl: i2, css: "" };

// node_modules/@primeuix/themes/material/badge/index.mjs
var r4 = { borderRadius: "{border.radius.md}", padding: "0 0.5rem", fontSize: "0.75rem", fontWeight: "700", minWidth: "1.5rem", height: "1.5rem" };
var o4 = { size: "0.5rem" };
var e4 = { fontSize: "0.625rem", minWidth: "1.25rem", height: "1.25rem" };
var c2 = { fontSize: "0.875rem", minWidth: "1.75rem", height: "1.75rem" };
var a2 = { fontSize: "1rem", minWidth: "2rem", height: "2rem" };
var n4 = { light: { primary: { background: "{primary.color}", color: "{primary.contrast.color}" }, secondary: { background: "{surface.100}", color: "{surface.600}" }, success: { background: "{green.500}", color: "{surface.0}" }, info: { background: "{sky.500}", color: "{surface.0}" }, warn: { background: "{orange.500}", color: "{surface.0}" }, danger: { background: "{red.500}", color: "{surface.0}" }, contrast: { background: "{surface.950}", color: "{surface.0}" } }, dark: { primary: { background: "{primary.color}", color: "{primary.contrast.color}" }, secondary: { background: "{surface.800}", color: "{surface.300}" }, success: { background: "{green.400}", color: "{green.950}" }, info: { background: "{sky.400}", color: "{sky.950}" }, warn: { background: "{orange.400}", color: "{orange.950}" }, danger: { background: "{red.400}", color: "{red.950}" }, contrast: { background: "{surface.0}", color: "{surface.950}" } } };
var d2 = { root: r4, dot: o4, sm: e4, lg: c2, xl: a2, colorScheme: n4, css: "" };

// node_modules/@primeuix/themes/material/base/index.mjs
var o5 = { borderRadius: { none: "0", xs: "2px", sm: "4px", md: "6px", lg: "8px", xl: "12px" }, emerald: { 50: "#E8F6F1", 100: "#C5EBE1", 200: "#9EDFCF", 300: "#76D3BD", 400: "#58C9AF", 500: "#3BBFA1", 600: "#35AF94", 700: "#2D9B83", 800: "#268873", 900: "#1A6657", 950: "#0d3329" }, green: { 50: "#E8F5E9", 100: "#C8E6C9", 200: "#A5D6A7", 300: "#81C784", 400: "#66BB6A", 500: "#4CAF50", 600: "#43A047", 700: "#388E3C", 800: "#2E7D32", 900: "#1B5E20", 950: "#0e2f10" }, lime: { 50: "#F9FBE7", 100: "#F0F4C3", 200: "#E6EE9C", 300: "#DCE775", 400: "#D4E157", 500: "#CDDC39", 600: "#C0CA33", 700: "#AFB42B", 800: "#9E9D24", 900: "#827717", 950: "#413c0c" }, red: { 50: "#FFEBEE", 100: "#FFCDD2", 200: "#EF9A9A", 300: "#E57373", 400: "#EF5350", 500: "#F44336", 600: "#E53935", 700: "#D32F2F", 800: "#C62828", 900: "#B71C1C", 950: "#5c0e0e" }, orange: { 50: "#FFF3E0", 100: "#FFE0B2", 200: "#FFCC80", 300: "#FFB74D", 400: "#FFA726", 500: "#FF9800", 600: "#FB8C00", 700: "#F57C00", 800: "#EF6C00", 900: "#E65100", 950: "#732900" }, amber: { 50: "#FFF8E1", 100: "#FFECB3", 200: "#FFE082", 300: "#FFD54F", 400: "#FFCA28", 500: "#FFC107", 600: "#FFB300", 700: "#FFA000", 800: "#FF8F00", 900: "#FF6F00", 950: "#803800" }, yellow: { 50: "#FFFDE7", 100: "#FFF9C4", 200: "#FFF59D", 300: "#FFF176", 400: "#FFEE58", 500: "#FFEB3B", 600: "#FDD835", 700: "#FBC02D", 800: "#F9A825", 900: "#F57F17", 950: "#7b400c" }, teal: { 50: "#E0F2F1", 100: "#B2DFDB", 200: "#80CBC4", 300: "#4DB6AC", 400: "#26A69A", 500: "#009688", 600: "#00897B", 700: "#00796B", 800: "#00695C", 900: "#004D40", 950: "#002720" }, cyan: { 50: "#E0F7FA", 100: "#B2EBF2", 200: "#80DEEA", 300: "#4DD0E1", 400: "#26C6DA", 500: "#00BCD4", 600: "#00ACC1", 700: "#0097A7", 800: "#00838F", 900: "#006064", 950: "#003032" }, sky: { 50: "#E1F5FE", 100: "#B3E5FC", 200: "#81D4FA", 300: "#4FC3F7", 400: "#29B6F6", 500: "#03A9F4", 600: "#039BE5", 700: "#0288D1", 800: "#0277BD", 900: "#01579B", 950: "#012c4e" }, blue: { 50: "#E3F2FD", 100: "#BBDEFB", 200: "#90CAF9", 300: "#64B5F6", 400: "#42A5F5", 500: "#2196F3", 600: "#1E88E5", 700: "#1976D2", 800: "#1565C0", 900: "#0D47A1", 950: "#072451" }, indigo: { 50: "#E8EAF6", 100: "#C5CAE9", 200: "#9FA8DA", 300: "#7986CB", 400: "#5C6BC0", 500: "#3F51B5", 600: "#3949AB", 700: "#303F9F", 800: "#283593", 900: "#1A237E", 950: "#0d123f" }, violet: { 50: "#EDE7F6", 100: "#D1C4E9", 200: "#B39DDB", 300: "#9575CD", 400: "#7E57C2", 500: "#673AB7", 600: "#5E35B1", 700: "#512DA8", 800: "#4527A0", 900: "#311B92", 950: "#190e49" }, purple: { 50: "#F3E5F5", 100: "#E1BEE7", 200: "#CE93D8", 300: "#BA68C8", 400: "#AB47BC", 500: "#9C27B0", 600: "#8E24AA", 700: "#7B1FA2", 800: "#6A1B9A", 900: "#4A148C", 950: "#250a46" }, fuchsia: { 50: "#FDE6F3", 100: "#FBC1E3", 200: "#F897D1", 300: "#F56DBF", 400: "#F34DB2", 500: "#F12DA5", 600: "#E0289D", 700: "#CC2392", 800: "#B81E88", 900: "#951777", 950: "#4b0c3c" }, pink: { 50: "#FCE4EC", 100: "#F8BBD0", 200: "#F48FB1", 300: "#F06292", 400: "#EC407A", 500: "#E91E63", 600: "#D81B60", 700: "#C2185B", 800: "#AD1457", 900: "#880E4F", 950: "#440728" }, rose: { 50: "#FFF0F0", 100: "#FFD9D9", 200: "#FFC0C0", 300: "#FFA7A7", 400: "#FF8E8E", 500: "#FF7575", 600: "#FF5252", 700: "#FF3838", 800: "#F71C1C", 900: "#D50000", 950: "#3E0000" }, slate: { 50: "#f8fafc", 100: "#f1f5f9", 200: "#e2e8f0", 300: "#cbd5e1", 400: "#94a3b8", 500: "#64748b", 600: "#475569", 700: "#334155", 800: "#1e293b", 900: "#0f172a", 950: "#020617" }, gray: { 50: "#f9fafb", 100: "#f3f4f6", 200: "#e5e7eb", 300: "#d1d5db", 400: "#9ca3af", 500: "#6b7280", 600: "#4b5563", 700: "#374151", 800: "#1f2937", 900: "#111827", 950: "#030712" }, zinc: { 50: "#fafafa", 100: "#f4f4f5", 200: "#e4e4e7", 300: "#d4d4d8", 400: "#a1a1aa", 500: "#71717a", 600: "#52525b", 700: "#3f3f46", 800: "#27272a", 900: "#18181b", 950: "#09090b" }, neutral: { 50: "#fafafa", 100: "#f5f5f5", 200: "#e5e5e5", 300: "#d4d4d4", 400: "#a3a3a3", 500: "#737373", 600: "#525252", 700: "#404040", 800: "#262626", 900: "#171717", 950: "#0a0a0a" }, stone: { 50: "#fafaf9", 100: "#f5f5f4", 200: "#e7e5e4", 300: "#d6d3d1", 400: "#a8a29e", 500: "#78716c", 600: "#57534e", 700: "#44403c", 800: "#292524", 900: "#1c1917", 950: "#0c0a09" } };
var r5 = { transitionDuration: "0.2s", focusRing: { width: "0", style: "none", color: "unset", offset: "0" }, disabledOpacity: "0.38", iconSize: "1rem", anchorGutter: "0", primary: { 50: "{emerald.50}", 100: "{emerald.100}", 200: "{emerald.200}", 300: "{emerald.300}", 400: "{emerald.400}", 500: "{emerald.500}", 600: "{emerald.600}", 700: "{emerald.700}", 800: "{emerald.800}", 900: "{emerald.900}", 950: "{emerald.950}" }, formField: { paddingX: "0.75rem", paddingY: "0.75rem", sm: { fontSize: "0.875rem", paddingX: "0.625rem", paddingY: "0.625rem" }, lg: { fontSize: "1.125rem", paddingX: "0.825rem", paddingY: "0.825rem" }, borderRadius: "{border.radius.sm}", focusRing: { width: "2px", style: "solid", color: "{primary.color}", offset: "-2px", shadow: "none" }, transitionDuration: "{transition.duration}" }, list: { padding: "0.5rem 0", gap: "0", header: { padding: "0.75rem 1rem" }, option: { padding: "0.75rem 1rem", borderRadius: "{border.radius.none}" }, optionGroup: { padding: "0.75rem 1rem", fontWeight: "700" } }, content: { borderRadius: "{border.radius.sm}" }, mask: { transitionDuration: "0.15s" }, navigation: { list: { padding: "0.5rem 0", gap: "0" }, item: { padding: "0.75rem 1rem", borderRadius: "{border.radius.none}", gap: "0.5rem" }, submenuLabel: { padding: "0.75rem 1rem", fontWeight: "700" }, submenuIcon: { size: "0.875rem" } }, overlay: { select: { borderRadius: "{border.radius.sm}", shadow: "0 5px 5px -3px rgba(0,0,0,.2), 0 8px 10px 1px rgba(0,0,0,.14), 0 3px 14px 2px rgba(0,0,0,.12)" }, popover: { borderRadius: "{border.radius.sm}", padding: "1rem", shadow: "0 11px 15px -7px rgba(0,0,0,.2), 0 24px 38px 3px rgba(0,0,0,.14), 0 9px 46px 8px rgba(0,0,0,.12)" }, modal: { borderRadius: "{border.radius.sm}", padding: "1.5rem", shadow: "0 11px 15px -7px rgba(0,0,0,.2), 0 24px 38px 3px rgba(0,0,0,.14), 0 9px 46px 8px rgba(0,0,0,.12)" }, navigation: { shadow: "0 2px 4px -1px rgba(0,0,0,.2), 0 4px 5px 0 rgba(0,0,0,.14), 0 1px 10px 0 rgba(0,0,0,.12)" } }, colorScheme: { light: { focusRing: { shadow: "0 0 1px 4px {surface.200}" }, surface: { 0: "#ffffff", 50: "{slate.50}", 100: "{slate.100}", 200: "{slate.200}", 300: "{slate.300}", 400: "{slate.400}", 500: "{slate.500}", 600: "{slate.600}", 700: "{slate.700}", 800: "{slate.800}", 900: "{slate.900}", 950: "{slate.950}" }, primary: { color: "{primary.500}", contrastColor: "#ffffff", hoverColor: "{primary.400}", activeColor: "{primary.300}" }, highlight: { background: "color-mix(in srgb, {primary.color}, transparent 88%)", focusBackground: "color-mix(in srgb, {primary.color}, transparent 76%)", color: "{primary.700}", focusColor: "{primary.800}" }, mask: { background: "rgba(0,0,0,0.32)", color: "{surface.200}" }, formField: { background: "{surface.0}", disabledBackground: "{surface.300}", filledBackground: "{surface.100}", filledHoverBackground: "{surface.200}", filledFocusBackground: "{surface.100}", borderColor: "{surface.400}", hoverBorderColor: "{surface.900}", focusBorderColor: "{primary.color}", invalidBorderColor: "{red.800}", color: "{surface.900}", disabledColor: "{surface.600}", placeholderColor: "{surface.600}", invalidPlaceholderColor: "{red.800}", floatLabelColor: "{surface.600}", floatLabelFocusColor: "{primary.600}", floatLabelActiveColor: "{surface.600}", floatLabelInvalidColor: "{form.field.invalid.placeholder.color}", iconColor: "{surface.600}", shadow: "none" }, text: { color: "{surface.900}", hoverColor: "{surface.900}", mutedColor: "{surface.600}", hoverMutedColor: "{surface.600}" }, content: { background: "{surface.0}", hoverBackground: "{surface.100}", borderColor: "{surface.300}", color: "{text.color}", hoverColor: "{text.hover.color}" }, overlay: { select: { background: "{surface.0}", borderColor: "{surface.0}", color: "{text.color}" }, popover: { background: "{surface.0}", borderColor: "{surface.0}", color: "{text.color}" }, modal: { background: "{surface.0}", borderColor: "{surface.0}", color: "{text.color}" } }, list: { option: { focusBackground: "{surface.100}", selectedBackground: "{highlight.background}", selectedFocusBackground: "{highlight.focus.background}", color: "{text.color}", focusColor: "{text.hover.color}", selectedColor: "{highlight.color}", selectedFocusColor: "{highlight.focus.color}", icon: { color: "{surface.600}", focusColor: "{surface.600}" } }, optionGroup: { background: "transparent", color: "{text.color}" } }, navigation: { item: { focusBackground: "{surface.100}", activeBackground: "{surface.200}", color: "{text.color}", focusColor: "{text.hover.color}", activeColor: "{text.hover.color}", icon: { color: "{surface.600}", focusColor: "{surface.600}", activeColor: "{surface.600}" } }, submenuLabel: { background: "transparent", color: "{text.color}" }, submenuIcon: { color: "{surface.600}", focusColor: "{surface.600}", activeColor: "{surface.600}" } } }, dark: { focusRing: { shadow: "0 0 1px 4px {surface.700}" }, surface: { 0: "#ffffff", 50: "{zinc.50}", 100: "{zinc.100}", 200: "{zinc.200}", 300: "{zinc.300}", 400: "{zinc.400}", 500: "{zinc.500}", 600: "{zinc.600}", 700: "{zinc.700}", 800: "{zinc.800}", 900: "{zinc.900}", 950: "{zinc.950}" }, primary: { color: "{primary.400}", contrastColor: "{surface.900}", hoverColor: "{primary.300}", activeColor: "{primary.200}" }, highlight: { background: "color-mix(in srgb, {primary.400}, transparent 84%)", focusBackground: "color-mix(in srgb, {primary.400}, transparent 76%)", color: "rgba(255,255,255,.87)", focusColor: "rgba(255,255,255,.87)" }, mask: { background: "rgba(0,0,0,0.6)", color: "{surface.200}" }, formField: { background: "{surface.950}", disabledBackground: "{surface.700}", filledBackground: "{surface.800}", filledHoverBackground: "{surface.700}", filledFocusBackground: "{surface.800}", borderColor: "{surface.600}", hoverBorderColor: "{surface.400}", focusBorderColor: "{primary.color}", invalidBorderColor: "{red.300}", color: "{surface.0}", disabledColor: "{surface.400}", placeholderColor: "{surface.400}", invalidPlaceholderColor: "{red.300}", floatLabelColor: "{surface.400}", floatLabelFocusColor: "{primary.color}", floatLabelActiveColor: "{surface.400}", floatLabelInvalidColor: "{form.field.invalid.placeholder.color}", iconColor: "{surface.400}", shadow: "none" }, text: { color: "{surface.0}", hoverColor: "{surface.0}", mutedColor: "{surface.400}", hoverMutedColor: "{surface.400}" }, content: { background: "{surface.900}", hoverBackground: "{surface.800}", borderColor: "{surface.700}", color: "{text.color}", hoverColor: "{text.hover.color}" }, overlay: { select: { background: "{surface.900}", borderColor: "{surface.900}", color: "{text.color}" }, popover: { background: "{surface.900}", borderColor: "{surface.900}", color: "{text.color}" }, modal: { background: "{surface.900}", borderColor: "{surface.900}", color: "{text.color}" } }, list: { option: { focusBackground: "{surface.800}", selectedBackground: "{highlight.background}", selectedFocusBackground: "{highlight.focus.background}", color: "{text.color}", focusColor: "{text.hover.color}", selectedColor: "{highlight.color}", selectedFocusColor: "{highlight.focus.color}", icon: { color: "{surface.400}", focusColor: "{surface.400}" } }, optionGroup: { background: "transparent", color: "{text.muted.color}" } }, navigation: { item: { focusBackground: "{surface.800}", activeBackground: "{surface.700}", color: "{text.color}", focusColor: "{text.hover.color}", activeColor: "{text.hover.color}", icon: { color: "{surface.400}", focusColor: "{surface.400}", activeColor: "{surface.400}" } }, submenuLabel: { background: "transparent", color: "{text.muted.color}" }, submenuIcon: { color: "{surface.400}", focusColor: "{surface.400}", activeColor: "{surface.400}" } } } } };
var e5 = { primitive: o5, semantic: r5 };

// node_modules/@primeuix/themes/material/blockui/index.mjs
var r6 = { borderRadius: "{content.border.radius}" };
var o6 = { root: r6, css: "" };

// node_modules/@primeuix/themes/material/breadcrumb/index.mjs
var o7 = { padding: "1rem", background: "{content.background}", gap: "0.5rem", transitionDuration: "{transition.duration}" };
var r7 = { color: "{text.muted.color}", hoverColor: "{text.color}", borderRadius: "{content.border.radius}", gap: "{navigation.item.gap}", icon: { color: "{navigation.item.icon.color}", hoverColor: "{navigation.item.icon.focus.color}" }, focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var i3 = { color: "{navigation.item.icon.color}" };
var t4 = { root: o7, item: r7, separator: i3, css: "" };

// node_modules/@primeuix/themes/material/button/index.mjs
var r8 = { borderRadius: "{form.field.border.radius}", roundedBorderRadius: "2rem", gap: "0.5rem", paddingX: "1rem", paddingY: "0.625rem", iconOnlyWidth: "3rem", sm: { fontSize: "{form.field.sm.font.size}", paddingX: "{form.field.sm.padding.x}", paddingY: "{form.field.sm.padding.y}", iconOnlyWidth: "2.5rem" }, lg: { fontSize: "{form.field.lg.font.size}", paddingX: "{form.field.lg.padding.x}", paddingY: "{form.field.lg.padding.y}", iconOnlyWidth: "3.5rem" }, label: { fontWeight: "500" }, raisedShadow: "0 3px 1px -2px rgba(0,0,0,.2), 0 2px 2px 0 rgba(0,0,0,.14), 0 1px 5px 0 rgba(0,0,0,.12)", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", offset: "{focus.ring.offset}" }, badgeSize: "1rem", transitionDuration: "{form.field.transition.duration}" };
var o8 = { light: { root: { primary: { background: "{primary.color}", hoverBackground: "{primary.hover.color}", activeBackground: "{primary.active.color}", borderColor: "{primary.color}", hoverBorderColor: "{primary.hover.color}", activeBorderColor: "{primary.active.color}", color: "{primary.contrast.color}", hoverColor: "{primary.contrast.color}", activeColor: "{primary.contrast.color}", focusRing: { color: "{primary.color}", shadow: "none" } }, secondary: { background: "{surface.100}", hoverBackground: "{surface.200}", activeBackground: "{surface.300}", borderColor: "{surface.100}", hoverBorderColor: "{surface.200}", activeBorderColor: "{surface.300}", color: "{surface.600}", hoverColor: "{surface.700}", activeColor: "{surface.800}", focusRing: { color: "{surface.600}", shadow: "none" } }, info: { background: "{sky.500}", hoverBackground: "{sky.400}", activeBackground: "{sky.300}", borderColor: "{sky.500}", hoverBorderColor: "{sky.400}", activeBorderColor: "{sky.300}", color: "#ffffff", hoverColor: "#ffffff", activeColor: "#ffffff", focusRing: { color: "{sky.500}", shadow: "none" } }, success: { background: "{green.500}", hoverBackground: "{green.400}", activeBackground: "{green.300}", borderColor: "{green.500}", hoverBorderColor: "{green.400}", activeBorderColor: "{green.300}", color: "#ffffff", hoverColor: "#ffffff", activeColor: "#ffffff", focusRing: { color: "{green.500}", shadow: "none" } }, warn: { background: "{orange.500}", hoverBackground: "{orange.400}", activeBackground: "{orange.300}", borderColor: "{orange.500}", hoverBorderColor: "{orange.400}", activeBorderColor: "{orange.300}", color: "#ffffff", hoverColor: "#ffffff", activeColor: "#ffffff", focusRing: { color: "{orange.500}", shadow: "none" } }, help: { background: "{purple.500}", hoverBackground: "{purple.400}", activeBackground: "{purple.300}", borderColor: "{purple.500}", hoverBorderColor: "{purple.400}", activeBorderColor: "{purple.300}", color: "#ffffff", hoverColor: "#ffffff", activeColor: "#ffffff", focusRing: { color: "{purple.500}", shadow: "none" } }, danger: { background: "{red.500}", hoverBackground: "{red.400}", activeBackground: "{red.300}", borderColor: "{red.500}", hoverBorderColor: "{red.400}", activeBorderColor: "{red.300}", color: "#ffffff", hoverColor: "#ffffff", activeColor: "#ffffff", focusRing: { color: "{red.500}", shadow: "none" } }, contrast: { background: "{surface.950}", hoverBackground: "{surface.800}", activeBackground: "{surface.700}", borderColor: "{surface.950}", hoverBorderColor: "{surface.800}", activeBorderColor: "{surface.700}", color: "{surface.0}", hoverColor: "{surface.0}", activeColor: "{surface.0}", focusRing: { color: "{surface.950}", shadow: "none" } } }, outlined: { primary: { hoverBackground: "{primary.50}", activeBackground: "{primary.100}", borderColor: "{primary.color}", color: "{primary.color}" }, secondary: { hoverBackground: "{surface.50}", activeBackground: "{surface.100}", borderColor: "{surface.600}", color: "{surface.600}" }, success: { hoverBackground: "{green.50}", activeBackground: "{green.100}", borderColor: "{green.500}", color: "{green.500}" }, info: { hoverBackground: "{sky.50}", activeBackground: "{sky.100}", borderColor: "{sky.500}", color: "{sky.500}" }, warn: { hoverBackground: "{orange.50}", activeBackground: "{orange.100}", borderColor: "{orange.500}", color: "{orange.500}" }, help: { hoverBackground: "{purple.50}", activeBackground: "{purple.100}", borderColor: "{purple.500}", color: "{purple.500}" }, danger: { hoverBackground: "{red.50}", activeBackground: "{red.100}", borderColor: "{red.500}", color: "{red.500}" }, contrast: { hoverBackground: "{surface.50}", activeBackground: "{surface.100}", borderColor: "{surface.950}", color: "{surface.950}" }, plain: { hoverBackground: "{surface.50}", activeBackground: "{surface.100}", borderColor: "{surface.900}", color: "{surface.900}" } }, text: { primary: { hoverBackground: "{primary.50}", activeBackground: "{primary.100}", color: "{primary.color}" }, secondary: { hoverBackground: "{surface.50}", activeBackground: "{surface.100}", color: "{surface.600}" }, success: { hoverBackground: "{green.50}", activeBackground: "{green.100}", color: "{green.500}" }, info: { hoverBackground: "{sky.50}", activeBackground: "{sky.100}", color: "{sky.500}" }, warn: { hoverBackground: "{orange.50}", activeBackground: "{orange.100}", color: "{orange.500}" }, help: { hoverBackground: "{purple.50}", activeBackground: "{purple.100}", color: "{purple.500}" }, danger: { hoverBackground: "{red.50}", activeBackground: "{red.100}", color: "{red.500}" }, contrast: { hoverBackground: "{surface.50}", activeBackground: "{surface.100}", color: "{surface.950}" }, plain: { hoverBackground: "{surface.50}", activeBackground: "{surface.100}", color: "{surface.900}" } }, link: { color: "{primary.color}", hoverColor: "{primary.color}", activeColor: "{primary.color}" } }, dark: { root: { primary: { background: "{primary.color}", hoverBackground: "{primary.hover.color}", activeBackground: "{primary.active.color}", borderColor: "{primary.color}", hoverBorderColor: "{primary.hover.color}", activeBorderColor: "{primary.active.color}", color: "{primary.contrast.color}", hoverColor: "{primary.contrast.color}", activeColor: "{primary.contrast.color}", focusRing: { color: "{primary.color}", shadow: "none" } }, secondary: { background: "{surface.800}", hoverBackground: "{surface.700}", activeBackground: "{surface.600}", borderColor: "{surface.800}", hoverBorderColor: "{surface.700}", activeBorderColor: "{surface.600}", color: "{surface.300}", hoverColor: "{surface.200}", activeColor: "{surface.100}", focusRing: { color: "{surface.300}", shadow: "none" } }, info: { background: "{sky.400}", hoverBackground: "{sky.300}", activeBackground: "{sky.200}", borderColor: "{sky.400}", hoverBorderColor: "{sky.300}", activeBorderColor: "{sky.200}", color: "{sky.950}", hoverColor: "{sky.950}", activeColor: "{sky.950}", focusRing: { color: "{sky.400}", shadow: "none" } }, success: { background: "{green.400}", hoverBackground: "{green.300}", activeBackground: "{green.200}", borderColor: "{green.400}", hoverBorderColor: "{green.300}", activeBorderColor: "{green.200}", color: "{green.950}", hoverColor: "{green.950}", activeColor: "{green.950}", focusRing: { color: "{green.400}", shadow: "none" } }, warn: { background: "{orange.400}", hoverBackground: "{orange.300}", activeBackground: "{orange.200}", borderColor: "{orange.400}", hoverBorderColor: "{orange.300}", activeBorderColor: "{orange.200}", color: "{orange.950}", hoverColor: "{orange.950}", activeColor: "{orange.950}", focusRing: { color: "{orange.400}", shadow: "none" } }, help: { background: "{purple.400}", hoverBackground: "{purple.300}", activeBackground: "{purple.200}", borderColor: "{purple.400}", hoverBorderColor: "{purple.300}", activeBorderColor: "{purple.200}", color: "{purple.950}", hoverColor: "{purple.950}", activeColor: "{purple.950}", focusRing: { color: "{purple.400}", shadow: "none" } }, danger: { background: "{red.400}", hoverBackground: "{red.300}", activeBackground: "{red.200}", borderColor: "{red.400}", hoverBorderColor: "{red.300}", activeBorderColor: "{red.200}", color: "{red.950}", hoverColor: "{red.950}", activeColor: "{red.950}", focusRing: { color: "{red.400}", shadow: "none" } }, contrast: { background: "{surface.0}", hoverBackground: "{surface.100}", activeBackground: "{surface.200}", borderColor: "{surface.0}", hoverBorderColor: "{surface.100}", activeBorderColor: "{surface.200}", color: "{surface.950}", hoverColor: "{surface.950}", activeColor: "{surface.950}", focusRing: { color: "{surface.0}", shadow: "none" } } }, outlined: { primary: { hoverBackground: "color-mix(in srgb, {primary.color}, transparent 96%)", activeBackground: "color-mix(in srgb, {primary.color}, transparent 84%)", borderColor: "{primary.700}", color: "{primary.color}" }, secondary: { hoverBackground: "rgba(255,255,255,0.04)", activeBackground: "rgba(255,255,255,0.16)", borderColor: "{surface.700}", color: "{surface.400}" }, success: { hoverBackground: "color-mix(in srgb, {green.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {green.400}, transparent 84%)", borderColor: "{green.700}", color: "{green.400}" }, info: { hoverBackground: "color-mix(in srgb, {sky.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {sky.400}, transparent 84%)", borderColor: "{sky.700}", color: "{sky.400}" }, warn: { hoverBackground: "color-mix(in srgb, {orange.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {orange.400}, transparent 84%)", borderColor: "{orange.700}", color: "{orange.400}" }, help: { hoverBackground: "color-mix(in srgb, {purple.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {purple.400}, transparent 84%)", borderColor: "{purple.700}", color: "{purple.400}" }, danger: { hoverBackground: "color-mix(in srgb, {red.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {red.400}, transparent 84%)", borderColor: "{red.700}", color: "{red.400}" }, contrast: { hoverBackground: "{surface.800}", activeBackground: "{surface.700}", borderColor: "{surface.500}", color: "{surface.0}" }, plain: { hoverBackground: "{surface.800}", activeBackground: "{surface.700}", borderColor: "{surface.600}", color: "{surface.0}" } }, text: { primary: { hoverBackground: "color-mix(in srgb, {primary.color}, transparent 96%)", activeBackground: "color-mix(in srgb, {primary.color}, transparent 84%)", color: "{primary.color}" }, secondary: { hoverBackground: "{surface.800}", activeBackground: "{surface.700}", color: "{surface.400}" }, success: { hoverBackground: "color-mix(in srgb, {green.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {green.400}, transparent 84%)", color: "{green.400}" }, info: { hoverBackground: "color-mix(in srgb, {sky.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {sky.400}, transparent 84%)", color: "{sky.400}" }, warn: { hoverBackground: "color-mix(in srgb, {orange.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {orange.400}, transparent 84%)", color: "{orange.400}" }, help: { hoverBackground: "color-mix(in srgb, {purple.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {purple.400}, transparent 84%)", color: "{purple.400}" }, danger: { hoverBackground: "color-mix(in srgb, {red.400}, transparent 96%)", activeBackground: "color-mix(in srgb, {red.400}, transparent 84%)", color: "{red.400}" }, contrast: { hoverBackground: "{surface.800}", activeBackground: "{surface.700}", color: "{surface.0}" }, plain: { hoverBackground: "{surface.800}", activeBackground: "{surface.700}", color: "{surface.0}" } }, link: { color: "{primary.color}", hoverColor: "{primary.color}", activeColor: "{primary.color}" } } };
var css3 = ({ dt: r86 }) => `
.p-button:focus-visible {
    background: ${r86("button.primary.active.background")};
    border-color: ${r86("button.primary.active.background")};
}

.p-button-secondary:focus-visible {
    background: ${r86("button.secondary.active.background")};
    border-color: ${r86("button.secondary.active.background")};
}

.p-button-success:focus-visible {
    background: ${r86("button.success.active.background")};
    border-color: ${r86("button.success.active.background")};
}

.p-button-info:focus-visible {
    background: ${r86("button.info.active.background")};
    border-color: ${r86("button.info.active.background")};
}

.p-button-warn:focus-visible {
    background: ${r86("button.warn.active.background")};
    border-color: ${r86("button.warn.active.background")};
}

.p-button-help:focus-visible {
    background: ${r86("button.help.active.background")};
    border-color: ${r86("button.help.active.background")};
}

.p-button-danger:focus-visible {
    background: ${r86("button.danger.active.background")};
    border-color: ${r86("button.danger.active.background")};
}

.p-button-contrast:focus-visible {
    background: ${r86("button.contrast.active.background")};
    border-color: ${r86("button.contrast.active.background")};
}

.p-button-link:focus-visible {
    background: color-mix(in srgb, ${r86("primary.color")}, transparent 84%);
    border-color: transparent;
}

.p-button-text:focus-visible {
    background: ${r86("button.text.primary.active.background")};
    border-color: transparent;
}

.p-button-secondary.p-button-text:focus-visible {
    background: ${r86("button.text.secondary.active.background")};
    border-color: transparent;
}

.p-button-success.p-button-text:focus-visible {
    background: ${r86("button.text.success.active.background")};
    border-color: transparent;
}

.p-button-info.p-button-text:focus-visible {
    background: ${r86("button.text.info.active.background")};
    border-color: transparent;
}

.p-button-warn.p-button-text:focus-visible {
    background: ${r86("button.text.warn.active.background")};
    border-color: transparent;
}

.p-button-help.p-button-text:focus-visible {
    background: ${r86("button.text.help.active.background")};
    border-color: transparent;
}

.p-button-danger.p-button-text:focus-visible {
    background: ${r86("button.text.danger.active.background")};
    border-color: transparent;
}

.p-button-contrast.p-button-text:focus-visible {
    background: ${r86("button.text.contrast.active.background")};
    border-color: transparent;
}

.p-button-plain.p-button-text:focus-visible {
    background: ${r86("button.text.plain.active.background")};
    border-color: transparent;
}

.p-button-outlined:focus-visible {
    background: ${r86("button.outlined.primary.active.background")};
}

.p-button-secondary.p-button-outlined:focus-visible {
    background: ${r86("button.outlined.secondary.active.background")};
    border-color: ${r86("button.outlined.secondary.border.color")};
}

.p-button-success.p-button-outlined:focus-visible {
    background: ${r86("button.outlined.success.active.background")};
}

.p-button-info.p-button-outlined:focus-visible {
    background: ${r86("button.outlined.info.active.background")};
}

.p-button-warn.p-button-outlined:focus-visible {
    background: ${r86("button.outlined.warn.active.background")};
}

.p-button-help.p-button-outlined:focus-visible {
    background: ${r86("button.outlined.help.active.background")};
}

.p-button-danger.p-button-outlined:focus-visible {
    background: ${r86("button.outlined.danger.active.background")};
}

.p-button-contrast.p-button-outlined:focus-visible {
    background: ${r86("button.outlined.contrast.active.background")};
}

.p-button-plain.p-button-outlined:focus-visible {
    background: ${r86("button.outlined.plain.active.background")};
}
`;
var e6 = { root: r8, colorScheme: o8, css: css3 };

// node_modules/@primeuix/themes/material/card/index.mjs
var o9 = { background: "{content.background}", borderRadius: "{content.border.radius}", color: "{content.color}", shadow: "0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12)" };
var r9 = { padding: "1.5rem", gap: "0.75rem" };
var t5 = { gap: "0.5rem" };
var e7 = { fontSize: "1.25rem", fontWeight: "500" };
var a3 = { color: "{text.muted.color}" };
var d3 = { root: o9, body: r9, caption: t5, title: e7, subtitle: a3, css: "" };

// node_modules/@primeuix/themes/material/carousel/index.mjs
var o10 = { transitionDuration: "{transition.duration}" };
var r10 = { gap: "0.25rem" };
var n5 = { padding: "1rem", gap: "1rem" };
var a4 = { width: "1.25rem", height: "1.25rem", borderRadius: "50%", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var i4 = { light: { indicator: { background: "{surface.200}", hoverBackground: "{surface.300}", activeBackground: "{primary.color}" } }, dark: { indicator: { background: "{surface.700}", hoverBackground: "{surface.600}", activeBackground: "{primary.color}" } } };
var css4 = ({ dt: o88 }) => `
.p-carousel-indicator-button:hover {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o88("text.color")}, transparent 96%);
}

.p-carousel-indicator-button:focus-visible {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o88("text.color")}, transparent 96%);
}

.p-carousel-indicator-active .p-carousel-indicator-button:hover {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o88("carousel.indicator.active.background")}, transparent 92%);
}

.p-carousel-indicator-active .p-carousel-indicator-button:focus-visible {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o88("carousel.indicator.active.background")}, transparent 84%);
}
`;
var t6 = { root: o10, content: r10, indicatorList: n5, indicator: a4, colorScheme: i4, css: css4 };

// node_modules/@primeuix/themes/material/cascadeselect/index.mjs
var o11 = { background: "{form.field.background}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", filledHoverBackground: "{form.field.filled.hover.background}", filledFocusBackground: "{form.field.filled.focus.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", color: "{form.field.color}", disabledColor: "{form.field.disabled.color}", placeholderColor: "{form.field.placeholder.color}", invalidPlaceholderColor: "{form.field.invalid.placeholder.color}", shadow: "{form.field.shadow}", paddingX: "{form.field.padding.x}", paddingY: "{form.field.padding.y}", borderRadius: "{form.field.border.radius}", focusRing: { width: "{form.field.focus.ring.width}", style: "{form.field.focus.ring.style}", color: "{form.field.focus.ring.color}", offset: "{form.field.focus.ring.offset}", shadow: "{form.field.focus.ring.shadow}" }, transitionDuration: "{form.field.transition.duration}", sm: { fontSize: "{form.field.sm.font.size}", paddingX: "{form.field.sm.padding.x}", paddingY: "{form.field.sm.padding.y}" }, lg: { fontSize: "{form.field.lg.font.size}", paddingX: "{form.field.lg.padding.x}", paddingY: "{form.field.lg.padding.y}" } };
var e8 = { width: "2.5rem", color: "{form.field.icon.color}" };
var r11 = { background: "{overlay.select.background}", borderColor: "{overlay.select.border.color}", borderRadius: "{overlay.select.border.radius}", color: "{overlay.select.color}", shadow: "{overlay.select.shadow}" };
var d4 = { padding: "{list.padding}", gap: "{list.gap}", mobileIndent: "1rem" };
var c3 = { focusBackground: "{list.option.focus.background}", selectedBackground: "{list.option.selected.background}", selectedFocusBackground: "{list.option.selected.focus.background}", color: "{list.option.color}", focusColor: "{list.option.focus.color}", selectedColor: "{list.option.selected.color}", selectedFocusColor: "{list.option.selected.focus.color}", padding: "{list.option.padding}", borderRadius: "{list.option.border.radius}", icon: { color: "{list.option.icon.color}", focusColor: "{list.option.icon.focus.color}", size: "0.875rem" } };
var l2 = { color: "{form.field.icon.color}" };
var css5 = ({ dt: o88 }) => `
.p-cascadeselect.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o88("cascadeselect.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("cascadeselect.focus.border.color")}, ${o88("cascadeselect.focus.border.color")}), linear-gradient(to bottom, ${o88("cascadeselect.border.color")}, ${o88("cascadeselect.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-cascadeselect.p-variant-filled:not(.p-disabled):hover {
    background: ${o88("cascadeselect.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("cascadeselect.focus.border.color")}, ${o88("cascadeselect.focus.border.color")}), linear-gradient(to bottom, ${o88("cascadeselect.hover.border.color")}, ${o88("cascadeselect.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-cascadeselect.p-variant-filled:not(.p-disabled).p-focus {
    outline: 0 none;
    background: ${o88("cascadeselect.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("cascadeselect.focus.border.color")}, ${o88("cascadeselect.focus.border.color")}), linear-gradient(to bottom, ${o88("cascadeselect.border.color")}, ${o88("cascadeselect.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-cascadeselect.p-variant-filled:not(.p-disabled).p-focus:hover {
    background-image: linear-gradient(to bottom, ${o88("cascadeselect.focus.border.color")}, ${o88("cascadeselect.focus.border.color")}), linear-gradient(to bottom, ${o88("cascadeselect.hover.border.color")}, ${o88("cascadeselect.hover.border.color")});
}

.p-cascadeselect.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o88("cascadeselect.invalid.border.color")}, ${o88("cascadeselect.invalid.border.color")}), linear-gradient(to bottom, ${o88("cascadeselect.invalid.border.color")}, ${o88("cascadeselect.invalid.border.color")});
}

.p-cascadeselect.p-variant-filled.p-invalid:not(.p-disabled).p-focus  {
    background-image: linear-gradient(to bottom, ${o88("cascadeselect.invalid.border.color")}, ${o88("cascadeselect.invalid.border.color")}), linear-gradient(to bottom, ${o88("cascadeselect.invalid.border.color")}, ${o88("cascadeselect.invalid.border.color")});
}

.p-cascadeselect-option {
    transition: none;
}
`;
var a5 = { root: o11, dropdown: e8, overlay: r11, list: d4, option: c3, clearIcon: l2, css: css5 };

// node_modules/@primeuix/themes/material/checkbox/index.mjs
var o12 = { borderRadius: "{border.radius.xs}", width: "18px", height: "18px", background: "{form.field.background}", checkedBackground: "{primary.color}", checkedHoverBackground: "{primary.color}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", checkedBorderColor: "{primary.color}", checkedHoverBorderColor: "{primary.color}", checkedFocusBorderColor: "{primary.color}", checkedDisabledBorderColor: "{form.field.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", shadow: "{form.field.shadow}", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" }, transitionDuration: "{form.field.transition.duration}", sm: { width: "14px", height: "14px" }, lg: { width: "22px", height: "22px" } };
var r12 = { size: "0.875rem", color: "{form.field.color}", checkedColor: "{primary.contrast.color}", checkedHoverColor: "{primary.contrast.color}", disabledColor: "{form.field.disabled.color}", sm: { size: "0.75rem" }, lg: { size: "1rem" } };
var css6 = ({ dt: o88 }) => `
.p-checkbox {
    border-radius: 50%;
    transition: box-shadow ${o88("checkbox.transition.duration")};
}

.p-checkbox-box {
    border-width: 2px;
}

.p-checkbox:not(.p-disabled):has(.p-checkbox-input:hover) {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o88("text.color")}, transparent 96%);
}

.p-checkbox:not(.p-disabled):has(.p-checkbox-input:focus-visible) {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o88("text.color")}, transparent 88%);
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o88("checkbox.checked.background")}, transparent 92%);
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:focus-visible) {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o88("checkbox.checked.background")}, transparent 84%);
}

.p-checkbox-checked .p-checkbox-box:before  {
    content: "";
    position: absolute;
    top: var(--p-md-check-icon-t);
    left: 2px;
    border-right: 2px solid transparent;
    border-bottom: 2px solid transparent;
    transform: rotate(45deg);
    transform-origin: 0% 100%;
    animation: p-md-check 125ms 50ms linear forwards;
}

.p-checkbox-checked .p-checkbox-icon {
    display: none;
}

.p-checkbox {
    --p-md-check-icon-t: 10px;
    --p-md-check-icon-w: 6px;
    --p-md-check-icon-h: 12px;
}

.p-checkbox-sm {
    --p-md-check-icon-t: 8px;
    --p-md-check-icon-w: 4px;
    --p-md-check-icon-h: 10px;
}

.p-checkbox-lg {
    --p-md-check-icon-t: 12px;
    --p-md-check-icon-w: 8px;
    --p-md-check-icon-h: 16px;
}

@keyframes p-md-check {
    0%{
      width: 0;
      height: 0;
      border-color: ${o88("checkbox.icon.checked.color")};
      transform: translate3d(0,0,0) rotate(45deg);
    }
    33%{
      width: var(--p-md-check-icon-w);
      height: 0;
      transform: translate3d(0,0,0) rotate(45deg);
    }
    100%{
      width: var(--p-md-check-icon-w);
      height: var(--p-md-check-icon-h);
      border-color: ${o88("checkbox.icon.checked.color")};
      transform: translate3d(0,calc(-1 * var(--p-md-check-icon-h)),0) rotate(45deg);
    }
}
`;
var c4 = { root: o12, icon: r12, css: css6 };

// node_modules/@primeuix/themes/material/chip/index.mjs
var o13 = { borderRadius: "2rem", paddingX: "0.75rem", paddingY: "0.75rem", gap: "0.5rem", transitionDuration: "{transition.duration}" };
var r13 = { width: "2.25rem", height: "2.25rem" };
var e9 = { size: "1rem" };
var c5 = { size: "1rem", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}" } };
var s = { light: { root: { background: "{surface.200}", color: "{surface.900}" }, icon: { color: "{surface.600}" }, removeIcon: { color: "{surface.600}", focusRing: { shadow: "0 0 1px 4px {surface.300}" } } }, dark: { root: { background: "{surface.700}", color: "{surface.0}" }, icon: { color: "{surface.0}" }, removeIcon: { color: "{surface.0}", focusRing: { shadow: "0 0 1px 4px {surface.600}" } } } };
var a6 = { root: o13, image: r13, icon: e9, removeIcon: c5, colorScheme: s, css: "" };

// node_modules/@primeuix/themes/material/colorpicker/index.mjs
var r14 = { transitionDuration: "{transition.duration}" };
var o14 = { width: "2rem", height: "2rem", borderRadius: "{form.field.border.radius}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var e10 = { shadow: "{overlay.popover.shadow}", borderRadius: "{overlay.popover.borderRadius}" };
var a7 = { light: { panel: { background: "{surface.800}", borderColor: "{surface.900}" }, handle: { color: "{surface.0}" } }, dark: { panel: { background: "{surface.900}", borderColor: "{surface.700}" }, handle: { color: "{surface.0}" } } };
var s2 = { root: r14, preview: o14, panel: e10, colorScheme: a7, css: "" };

// node_modules/@primeuix/themes/material/confirmdialog/index.mjs
var o15 = { size: "2rem", color: "{overlay.modal.color}" };
var e11 = { gap: "1rem" };
var r15 = { icon: o15, content: e11, css: "" };

// node_modules/@primeuix/themes/material/confirmpopup/index.mjs
var o16 = { background: "{overlay.popover.background}", borderColor: "{overlay.popover.border.color}", color: "{overlay.popover.color}", borderRadius: "{overlay.popover.border.radius}", shadow: "{overlay.popover.shadow}", gutter: "10px", arrowOffset: "1.25rem" };
var r16 = { padding: "{overlay.popover.padding}", gap: "1rem" };
var e12 = { size: "1.5rem", color: "{overlay.popover.color}" };
var p = { gap: "0.5rem", padding: "0 {overlay.popover.padding} {overlay.popover.padding} {overlay.popover.padding}" };
var a8 = { root: o16, content: r16, icon: e12, footer: p, css: "" };

// node_modules/@primeuix/themes/material/contextmenu/index.mjs
var o17 = { background: "{content.background}", borderColor: "transparent", color: "{content.color}", borderRadius: "{content.border.radius}", shadow: "{overlay.navigation.shadow}", transitionDuration: "{transition.duration}" };
var i5 = { padding: "{navigation.list.padding}", gap: "{navigation.list.gap}" };
var n6 = { focusBackground: "{navigation.item.focus.background}", activeBackground: "{navigation.item.active.background}", color: "{navigation.item.color}", focusColor: "{navigation.item.focus.color}", activeColor: "{navigation.item.active.color}", padding: "{navigation.item.padding}", borderRadius: "{navigation.item.border.radius}", gap: "{navigation.item.gap}", icon: { color: "{navigation.item.icon.color}", focusColor: "{navigation.item.icon.focus.color}", activeColor: "{navigation.item.icon.active.color}" } };
var a9 = { mobileIndent: "1rem" };
var t7 = { size: "{navigation.submenu.icon.size}", color: "{navigation.submenu.icon.color}", focusColor: "{navigation.submenu.icon.focus.color}", activeColor: "{navigation.submenu.icon.active.color}" };
var r17 = { borderColor: "{content.border.color}" };
var c6 = { root: o17, list: i5, item: n6, submenu: a9, submenuIcon: t7, separator: r17, css: "" };

// node_modules/@primeuix/themes/material/datatable/index.mjs
var o18 = { transitionDuration: "{transition.duration}" };
var r18 = { background: "{content.background}", borderColor: "{datatable.border.color}", color: "{content.color}", borderWidth: "0 0 1px 0", padding: "0.75rem 1rem", sm: { padding: "0.375rem 0.5rem" }, lg: { padding: "1rem 1.25rem" } };
var e13 = { background: "{content.background}", hoverBackground: "{content.hover.background}", selectedBackground: "{highlight.background}", borderColor: "{datatable.border.color}", color: "{content.color}", hoverColor: "{content.hover.color}", selectedColor: "{highlight.color}", gap: "0.5rem", padding: "0.75rem 1rem", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "-1px", shadow: "{focus.ring.shadow}" }, sm: { padding: "0.375rem 0.5rem" }, lg: { padding: "1rem 1.25rem" } };
var d5 = { fontWeight: "600" };
var t8 = { background: "{content.background}", hoverBackground: "{content.hover.background}", selectedBackground: "{highlight.background}", color: "{content.color}", hoverColor: "{content.hover.color}", selectedColor: "{highlight.color}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "-1px", shadow: "{focus.ring.shadow}" } };
var l3 = { borderColor: "{datatable.border.color}", padding: "0.75rem 1rem", sm: { padding: "0.375rem 0.5rem" }, lg: { padding: "1rem 1.25rem" } };
var c7 = { background: "{content.background}", borderColor: "{datatable.border.color}", color: "{content.color}", padding: "0.75rem 1rem", sm: { padding: "0.375rem 0.5rem" }, lg: { padding: "1rem 1.25rem" } };
var n7 = { fontWeight: "600" };
var a10 = { background: "{content.background}", borderColor: "{datatable.border.color}", color: "{content.color}", borderWidth: "0 0 1px 0", padding: "0.75rem 1rem", sm: { padding: "0.375rem 0.5rem" }, lg: { padding: "1rem 1.25rem" } };
var i6 = { color: "{primary.color}" };
var s3 = { width: "0.5rem" };
var g = { width: "1px", color: "{primary.color}" };
var u = { color: "{text.muted.color}", hoverColor: "{text.hover.muted.color}", size: "0.875rem" };
var b = { size: "2rem" };
var p2 = { hoverBackground: "{content.hover.background}", selectedHoverBackground: "{content.background}", color: "{text.muted.color}", hoverColor: "{text.color}", selectedHoverColor: "{primary.color}", size: "1.75rem", borderRadius: "50%", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var m = { inlineGap: "0.5rem", overlaySelect: { background: "{overlay.select.background}", borderColor: "{overlay.select.border.color}", borderRadius: "{overlay.select.border.radius}", color: "{overlay.select.color}", shadow: "{overlay.select.shadow}" }, overlayPopover: { background: "{overlay.popover.background}", borderColor: "{overlay.popover.border.color}", borderRadius: "{overlay.popover.border.radius}", color: "{overlay.popover.color}", shadow: "{overlay.popover.shadow}", padding: "{overlay.popover.padding}", gap: "0.5rem" }, rule: { borderColor: "{content.border.color}" }, constraintList: { padding: "{list.padding}", gap: "{list.gap}" }, constraint: { focusBackground: "{list.option.focus.background}", selectedBackground: "{list.option.selected.background}", selectedFocusBackground: "{list.option.selected.focus.background}", color: "{list.option.color}", focusColor: "{list.option.focus.color}", selectedColor: "{list.option.selected.color}", selectedFocusColor: "{list.option.selected.focus.color}", separator: { borderColor: "{content.border.color}" }, padding: "{list.option.padding}", borderRadius: "{list.option.border.radius}" } };
var h = { borderColor: "{datatable.border.color}", borderWidth: "0 0 1px 0" };
var f = { borderColor: "{datatable.border.color}", borderWidth: "0 0 1px 0" };
var v = { light: { root: { borderColor: "{content.border.color}" }, row: { stripedBackground: "{surface.50}" }, bodyCell: { selectedBorderColor: "{primary.100}" } }, dark: { root: { borderColor: "{surface.800}" }, row: { stripedBackground: "{surface.950}" }, bodyCell: { selectedBorderColor: "{primary.900}" } } };
var css7 = "\n.p-datatable-header-cell,\n.p-datatable-tbody > tr {\n    transition: none;\n}\n";
var k = { root: o18, header: r18, headerCell: e13, columnTitle: d5, row: t8, bodyCell: l3, footerCell: c7, columnFooter: n7, footer: a10, dropPoint: i6, columnResizer: s3, resizeIndicator: g, sortIcon: u, loadingIcon: b, rowToggleButton: p2, filter: m, paginatorTop: h, paginatorBottom: f, colorScheme: v, css: css7 };

// node_modules/@primeuix/themes/material/dataview/index.mjs
var o19 = { borderColor: "transparent", borderWidth: "0", borderRadius: "0", padding: "0" };
var r19 = { background: "{content.background}", color: "{content.color}", borderColor: "{content.border.color}", borderWidth: "0 0 1px 0", padding: "0.75rem 1rem", borderRadius: "0" };
var d6 = { background: "{content.background}", color: "{content.color}", borderColor: "transparent", borderWidth: "0", padding: "0", borderRadius: "0" };
var e14 = { background: "{content.background}", color: "{content.color}", borderColor: "{content.border.color}", borderWidth: "1px 0 0 0", padding: "0.75rem 1rem", borderRadius: "0" };
var t9 = { borderColor: "{content.border.color}", borderWidth: "0 0 1px 0" };
var n8 = { borderColor: "{content.border.color}", borderWidth: "1px 0 0 0" };
var c8 = { root: o19, header: r19, content: d6, footer: e14, paginatorTop: t9, paginatorBottom: n8, css: "" };

// node_modules/@primeuix/themes/material/datepicker/index.mjs
var o20 = { transitionDuration: "{transition.duration}" };
var r20 = { background: "{content.background}", borderColor: "{content.border.color}", color: "{content.color}", borderRadius: "{content.border.radius}", shadow: "{overlay.popover.shadow}", padding: "0.5rem" };
var e15 = { background: "{content.background}", borderColor: "{content.border.color}", color: "{content.color}", padding: "0 0 0.5rem 0" };
var n9 = { gap: "0.5rem", fontWeight: "700" };
var c9 = { width: "3rem", sm: { width: "2.5rem" }, lg: { width: "3.5rem" }, borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.border.color}", activeBorderColor: "{form.field.border.color}", borderRadius: "{form.field.border.radius}", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var d7 = { color: "{form.field.icon.color}" };
var t10 = { hoverBackground: "{content.hover.background}", color: "{content.color}", hoverColor: "{content.hover.color}", padding: "0.5rem 0.75rem", borderRadius: "{content.border.radius}" };
var a11 = { hoverBackground: "{content.hover.background}", color: "{content.color}", hoverColor: "{content.hover.color}", padding: "0.5rem 0.75rem", borderRadius: "{content.border.radius}" };
var i7 = { borderColor: "{content.border.color}", gap: "{overlay.popover.padding}" };
var l4 = { margin: "0.5rem 0 0 0" };
var u2 = { padding: "0.5rem", fontWeight: "700", color: "{content.color}" };
var s4 = { hoverBackground: "{content.hover.background}", selectedBackground: "{primary.color}", rangeSelectedBackground: "{highlight.background}", color: "{content.color}", hoverColor: "{content.hover.color}", selectedColor: "{primary.contrast.color}", rangeSelectedColor: "{highlight.color}", width: "2.5rem", height: "2.5rem", borderRadius: "50%", padding: "0.125rem", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var g2 = { margin: "0.5rem 0 0 0" };
var h2 = { padding: "0.625rem", borderRadius: "{content.border.radius}" };
var b2 = { margin: "0.5rem 0 0 0" };
var p3 = { padding: "0.625rem", borderRadius: "{content.border.radius}" };
var f2 = { padding: "0.5rem 0 0 0", borderColor: "{content.border.color}" };
var k2 = { padding: "0.5rem 0 0 0", borderColor: "{content.border.color}", gap: "0.5rem", buttonGap: "0.25rem" };
var m2 = { light: { dropdown: { background: "{surface.100}", hoverBackground: "{surface.200}", activeBackground: "{surface.300}", color: "{surface.600}", hoverColor: "{surface.700}", activeColor: "{surface.800}" }, today: { background: "{surface.200}", color: "{surface.900}" } }, dark: { dropdown: { background: "{surface.800}", hoverBackground: "{surface.700}", activeBackground: "{surface.600}", color: "{surface.300}", hoverColor: "{surface.200}", activeColor: "{surface.100}" }, today: { background: "{surface.700}", color: "{surface.0}" } } };
var css8 = ({ dt: o88 }) => `
.p-datepicker-header {
    justify-content: start;
}

.p-datepicker-title {
    order: 1;
}

.p-datepicker-prev-button {
    order: 2;
    margin-inline-start: auto;
}

.p-datepicker-next-button {
    order: 2;
    margin-inline-start: 0.5rem;
}

.p-datepicker-select-month:focus-visible {
    background: ${o88("datepicker.select.month.hover.background")};
    color: ${o88("datepicker.select.month.hover.color")};
    outline: 0 none;
}

.p-datepicker-select-year:focus-visible {
    background: ${o88("datepicker.select.year.hover.background")};
    color: ${o88("datepicker.select.year.hover.color")};
    outline: 0 none;
}

.p-datepicker-dropdown:focus-visible {
    outline: 0 none;
    background: ${o88("datepicker.dropdown.hover.background")};
    border-color: ${o88("datepicker.dropdown.hover.border.color")};
    color: ${o88("datepicker.dropdown.hover.color")};
}
`;
var v2 = { root: o20, panel: r20, header: e15, title: n9, dropdown: c9, inputIcon: d7, selectMonth: t10, selectYear: a11, group: i7, dayView: l4, weekDay: u2, date: s4, monthView: g2, month: h2, yearView: b2, year: p3, buttonbar: f2, timePicker: k2, colorScheme: m2, css: css8 };

// node_modules/@primeuix/themes/material/dialog/index.mjs
var o21 = { background: "{overlay.modal.background}", borderColor: "{overlay.modal.border.color}", color: "{overlay.modal.color}", borderRadius: "{overlay.modal.border.radius}", shadow: "{overlay.modal.shadow}" };
var a12 = { padding: "{overlay.modal.padding}", gap: "0.5rem" };
var d8 = { fontSize: "1.25rem", fontWeight: "600" };
var r21 = { padding: "0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}" };
var l5 = { padding: "0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}", gap: "0.5rem" };
var e16 = { root: o21, header: a12, title: d8, content: r21, footer: l5, css: "" };

// node_modules/@primeuix/themes/material/divider/index.mjs
var r22 = { borderColor: "{content.border.color}" };
var o22 = { background: "{content.background}", color: "{text.color}" };
var n10 = { margin: "1rem 0", padding: "0 1rem", content: { padding: "0 0.5rem" } };
var e17 = { margin: "0 1rem", padding: "0.5rem 0", content: { padding: "0.5rem 0" } };
var t11 = { root: r22, content: o22, horizontal: n10, vertical: e17, css: "" };

// node_modules/@primeuix/themes/material/dock/index.mjs
var r23 = { background: "rgba(255, 255, 255, 0.1)", borderColor: "rgba(255, 255, 255, 0.2)", padding: "0.5rem", borderRadius: "{border.radius.xl}" };
var o23 = { borderRadius: "{content.border.radius}", padding: "0.5rem", size: "3rem", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var s5 = { root: r23, item: o23, css: "" };

// node_modules/@primeuix/themes/material/drawer/index.mjs
var o24 = { background: "{overlay.modal.background}", borderColor: "{overlay.modal.border.color}", color: "{overlay.modal.color}", shadow: "{overlay.modal.shadow}" };
var a13 = { padding: "{overlay.modal.padding}" };
var d9 = { fontSize: "1.5rem", fontWeight: "600" };
var r24 = { padding: "0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}" };
var l6 = { padding: "{overlay.modal.padding}" };
var e18 = { root: o24, header: a13, title: d9, content: r24, footer: l6, css: "" };

// node_modules/@primeuix/themes/material/editor/index.mjs
var o25 = { background: "{content.background}", borderColor: "{content.border.color}", borderRadius: "{content.border.radius}" };
var r25 = { color: "{text.muted.color}", hoverColor: "{text.color}", activeColor: "{primary.color}" };
var e19 = { background: "{overlay.select.background}", borderColor: "{overlay.select.border.color}", borderRadius: "{overlay.select.border.radius}", color: "{overlay.select.color}", shadow: "{overlay.select.shadow}", padding: "{list.padding}" };
var t12 = { focusBackground: "{list.option.focus.background}", color: "{list.option.color}", focusColor: "{list.option.focus.color}", padding: "{list.option.padding}", borderRadius: "{list.option.border.radius}" };
var d10 = { background: "{content.background}", borderColor: "{content.border.color}", color: "{content.color}", borderRadius: "{content.border.radius}" };
var css9 = "\n.p-editor .p-editor-toolbar {\n    padding: 0.75rem\n}\n";
var l7 = { toolbar: o25, toolbarItem: r25, overlay: e19, overlayOption: t12, content: d10, css: css9 };

// node_modules/@primeuix/themes/material/fieldset/index.mjs
var o26 = { background: "{content.background}", borderColor: "{content.border.color}", borderRadius: "{content.border.radius}", color: "{content.color}", padding: "0 1.25rem 1.25rem 1.25rem", transitionDuration: "{transition.duration}" };
var r26 = { background: "{content.background}", hoverBackground: "{content.hover.background}", color: "{content.color}", hoverColor: "{content.hover.color}", borderRadius: "{content.border.radius}", borderWidth: "1px", borderColor: "transparent", padding: "0.75rem 1rem", gap: "0.5rem", fontWeight: "600", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var n11 = { color: "{text.muted.color}", hoverColor: "{text.hover.muted.color}" };
var t13 = { padding: "0" };
var css10 = ({ dt: o88 }) => `
.p-fieldset-toggle-button:focus-visible {
    background: ${o88("navigation.item.active.background")};
}
`;
var e20 = { root: o26, legend: r26, toggleIcon: n11, content: t13, css: css10 };

// node_modules/@primeuix/themes/material/fileupload/index.mjs
var r27 = { background: "{content.background}", borderColor: "{content.border.color}", color: "{content.color}", borderRadius: "{content.border.radius}", transitionDuration: "{transition.duration}" };
var o27 = { background: "transparent", color: "{text.color}", padding: "1.25rem", borderColor: "unset", borderWidth: "0", borderRadius: "0", gap: "0.5rem" };
var e21 = { highlightBorderColor: "{primary.color}", padding: "0 1.25rem 1.25rem 1.25rem", gap: "1rem" };
var t14 = { padding: "1rem", gap: "1rem", borderColor: "{content.border.color}", info: { gap: "0.5rem" } };
var a14 = { gap: "0.5rem" };
var n12 = { height: "0.25rem" };
var d11 = { gap: "0.5rem" };
var i8 = { root: r27, header: o27, content: e21, file: t14, fileList: a14, progressbar: n12, basic: d11, css: "" };

// node_modules/@primeuix/themes/material/floatlabel/index.mjs
var o28 = { color: "{form.field.float.label.color}", focusColor: "{form.field.float.label.focus.color}", activeColor: "{form.field.float.label.active.color}", invalidColor: "{form.field.float.label.invalid.color}", transitionDuration: "0.2s", positionX: "{form.field.padding.x}", positionY: "{form.field.padding.y}", fontWeight: "500", active: { fontSize: "0.75rem", fontWeight: "400" } };
var i9 = { active: { top: "-1.25rem" } };
var r28 = { input: { paddingTop: "1.5rem", paddingBottom: "0.5rem" }, active: { top: "0.5rem" } };
var e22 = { borderRadius: "{border.radius.xs}", active: { background: "{form.field.background}", padding: "0 0.125rem" } };
var a15 = { root: o28, over: i9, in: r28, on: e22, css: "" };

// node_modules/@primeuix/themes/material/galleria/index.mjs
var o29 = { borderWidth: "1px", borderColor: "{content.border.color}", borderRadius: "{content.border.radius}", transitionDuration: "{transition.duration}" };
var r29 = { background: "rgba(255, 255, 255, 0.1)", hoverBackground: "rgba(255, 255, 255, 0.2)", color: "{surface.100}", hoverColor: "{surface.0}", size: "3rem", gutter: "0.5rem", prev: { borderRadius: "50%" }, next: { borderRadius: "50%" }, focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var e23 = { size: "1.5rem" };
var c10 = { background: "{content.background}", padding: "1rem 0.25rem" };
var t15 = { size: "2rem", borderRadius: "50%", gutter: "0.5rem", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var a16 = { size: "1rem" };
var n13 = { background: "rgba(0, 0, 0, 0.5)", color: "{surface.100}", padding: "1rem" };
var s6 = { gap: "0.5rem", padding: "1rem" };
var u3 = { width: "1rem", height: "1rem", activeBackground: "{primary.color}", borderRadius: "50%", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var i10 = { background: "rgba(0, 0, 0, 0.5)" };
var d12 = { background: "rgba(255, 255, 255, 0.4)", hoverBackground: "rgba(255, 255, 255, 0.6)", activeBackground: "rgba(255, 255, 255, 0.9)" };
var g3 = { size: "3rem", gutter: "0.5rem", background: "rgba(255, 255, 255, 0.1)", hoverBackground: "rgba(255, 255, 255, 0.2)", color: "{surface.50}", hoverColor: "{surface.0}", borderRadius: "50%", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var f3 = { size: "1.5rem" };
var h3 = { light: { thumbnailNavButton: { hoverBackground: "{surface.100}", color: "{surface.600}", hoverColor: "{surface.700}" }, indicatorButton: { background: "{surface.200}", hoverBackground: "{surface.300}" } }, dark: { thumbnailNavButton: { hoverBackground: "{surface.700}", color: "{surface.400}", hoverColor: "{surface.0}" }, indicatorButton: { background: "{surface.700}", hoverBackground: "{surface.600}" } } };
var l8 = { root: o29, navButton: r29, navIcon: e23, thumbnailsContent: c10, thumbnailNavButton: t15, thumbnailNavButtonIcon: a16, caption: n13, indicatorList: s6, indicatorButton: u3, insetIndicatorList: i10, insetIndicatorButton: d12, closeButton: g3, closeButtonIcon: f3, colorScheme: h3, css: "" };

// node_modules/@primeuix/themes/material/iconfield/index.mjs
var o30 = { color: "{form.field.icon.color}" };
var c11 = { icon: o30, css: "" };

// node_modules/@primeuix/themes/material/iftalabel/index.mjs
var o31 = { color: "{form.field.float.label.color}", focusColor: "{form.field.float.label.focus.color}", invalidColor: "{form.field.float.label.invalid.color}", transitionDuration: "0.2s", positionX: "{form.field.padding.x}", top: "0.5rem", fontSize: "0.75rem", fontWeight: "400" };
var l9 = { paddingTop: "1.5rem", paddingBottom: "0.5rem" };
var i11 = { root: o31, input: l9, css: "" };

// node_modules/@primeuix/themes/material/image/index.mjs
var o32 = { transitionDuration: "{transition.duration}" };
var r30 = { icon: { size: "1.5rem" }, mask: { background: "{mask.background}", color: "{mask.color}" } };
var a17 = { position: { left: "auto", right: "1rem", top: "1rem", bottom: "auto" }, blur: "8px", background: "rgba(255,255,255,0.1)", borderColor: "rgba(255,255,255,0.2)", borderWidth: "1px", borderRadius: "30px", padding: ".5rem", gap: "0.5rem" };
var i12 = { hoverBackground: "rgba(255,255,255,0.1)", color: "{surface.50}", hoverColor: "{surface.0}", size: "3rem", iconSize: "1.5rem", borderRadius: "50%", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var e24 = { root: o32, preview: r30, toolbar: a17, action: i12, css: "" };

// node_modules/@primeuix/themes/material/imagecompare/index.mjs
var o33 = { size: "20px", hoverSize: "40px", background: "rgba(255,255,255,0.4)", hoverBackground: "rgba(255,255,255,0.6)", borderColor: "unset", hoverBorderColor: "unset", borderWidth: "0", borderRadius: "50%", transitionDuration: "{transition.duration}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "rgba(255,255,255,0.3)", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var r31 = { handle: o33, css: "" };

// node_modules/@primeuix/themes/material/inlinemessage/index.mjs
var r32 = { padding: "{form.field.padding.y} {form.field.padding.x}", borderRadius: "{content.border.radius}", gap: "0.5rem" };
var o34 = { fontWeight: "500" };
var e25 = { size: "1rem" };
var n14 = { light: { info: { background: "color-mix(in srgb, {blue.50}, transparent 5%)", borderColor: "{blue.200}", color: "{blue.600}", shadow: "0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)" }, success: { background: "color-mix(in srgb, {green.50}, transparent 5%)", borderColor: "{green.200}", color: "{green.600}", shadow: "0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)" }, warn: { background: "color-mix(in srgb,{yellow.50}, transparent 5%)", borderColor: "{yellow.200}", color: "{yellow.600}", shadow: "0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)" }, error: { background: "color-mix(in srgb, {red.50}, transparent 5%)", borderColor: "{red.200}", color: "{red.600}", shadow: "0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)" }, secondary: { background: "{surface.100}", borderColor: "{surface.200}", color: "{surface.600}", shadow: "0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)" }, contrast: { background: "{surface.900}", borderColor: "{surface.950}", color: "{surface.50}", shadow: "0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)" } }, dark: { info: { background: "color-mix(in srgb, {blue.500}, transparent 84%)", borderColor: "color-mix(in srgb, {blue.700}, transparent 64%)", color: "{blue.500}", shadow: "0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)" }, success: { background: "color-mix(in srgb, {green.500}, transparent 84%)", borderColor: "color-mix(in srgb, {green.700}, transparent 64%)", color: "{green.500}", shadow: "0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)" }, warn: { background: "color-mix(in srgb, {yellow.500}, transparent 84%)", borderColor: "color-mix(in srgb, {yellow.700}, transparent 64%)", color: "{yellow.500}", shadow: "0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)" }, error: { background: "color-mix(in srgb, {red.500}, transparent 84%)", borderColor: "color-mix(in srgb, {red.700}, transparent 64%)", color: "{red.500}", shadow: "0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)" }, secondary: { background: "{surface.800}", borderColor: "{surface.700}", color: "{surface.300}", shadow: "0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)" }, contrast: { background: "{surface.0}", borderColor: "{surface.100}", color: "{surface.950}", shadow: "0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)" } } };
var a18 = { root: r32, text: o34, icon: e25, colorScheme: n14, css: "" };

// node_modules/@primeuix/themes/material/inplace/index.mjs
var o35 = { padding: "{form.field.padding.y} {form.field.padding.x}", borderRadius: "{content.border.radius}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" }, transitionDuration: "{transition.duration}" };
var r33 = { hoverBackground: "{content.hover.background}", hoverColor: "{content.hover.color}" };
var n15 = { root: o35, display: r33, css: "" };

// node_modules/@primeuix/themes/material/inputchips/index.mjs
var o36 = { background: "{form.field.background}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", filledFocusBackground: "{form.field.filled.focus.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", color: "{form.field.color}", disabledColor: "{form.field.disabled.color}", placeholderColor: "{form.field.placeholder.color}", shadow: "{form.field.shadow}", paddingX: "{form.field.padding.x}", paddingY: "{form.field.padding.y}", borderRadius: "{form.field.border.radius}", focusRing: { width: "{form.field.focus.ring.width}", style: "{form.field.focus.ring.style}", color: "{form.field.focus.ring.color}", offset: "{form.field.focus.ring.offset}", shadow: "{form.field.focus.ring.shadow}" }, transitionDuration: "{form.field.transition.duration}" };
var r34 = { borderRadius: "{border.radius.sm}" };
var d13 = { light: { chip: { focusBackground: "{surface.200}", color: "{surface.800}" } }, dark: { chip: { focusBackground: "{surface.700}", color: "{surface.0}" } } };
var f4 = { root: o36, chip: r34, colorScheme: d13, css: "" };

// node_modules/@primeuix/themes/material/inputgroup/index.mjs
var r35 = { background: "{form.field.background}", borderColor: "{form.field.border.color}", color: "{form.field.icon.color}", borderRadius: "{form.field.border.radius}", padding: "0.75rem", minWidth: "3rem" };
var css11 = ({ dt: r86 }) => `
.p-inputgroup:has(.p-variant-filled) .p-inputgroupaddon {
    border-block-start-color: ${r86("inputtext.filled.background")};
    border-inline-color: ${r86("inputtext.filled.background")};
    background: ${r86("inputtext.filled.background")} no-repeat;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
`;
var o37 = { addon: r35, css: css11 };

// node_modules/@primeuix/themes/material/inputnumber/index.mjs
var r36 = { transitionDuration: "{transition.duration}" };
var o38 = { width: "3rem", borderRadius: "{form.field.border.radius}", verticalPadding: "{form.field.padding.y}" };
var e26 = { light: { button: { background: "transparent", hoverBackground: "{surface.100}", activeBackground: "{surface.200}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.border.color}", activeBorderColor: "{form.field.border.color}", color: "{surface.400}", hoverColor: "{surface.500}", activeColor: "{surface.600}" } }, dark: { button: { background: "transparent", hoverBackground: "{surface.800}", activeBackground: "{surface.700}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.border.color}", activeBorderColor: "{form.field.border.color}", color: "{surface.400}", hoverColor: "{surface.300}", activeColor: "{surface.200}" } } };
var css12 = ({ dt: r86 }) => `
.p-inputnumber-stacked .p-inputnumber-button-group {
    top: 2px;
    right: 2px;
    height: calc(100% - 4px);
}

.p-inputnumber-horizontal:has(.p-variant-filled) .p-inputnumber-button {
    border-block-start-color: ${r86("inputtext.filled.background")};
    border-inline-color: ${r86("inputtext.filled.background")};
    background: ${r86("inputtext.filled.background")} no-repeat;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.p-inputnumber-vertical:has(.p-variant-filled) .p-inputnumber-button {
    border-block-color: ${r86("inputtext.filled.background")};
    border-inline-color: ${r86("inputtext.filled.background")};
    background: ${r86("inputtext.filled.background")} no-repeat;
}

.p-inputnumber-vertical:has(.p-variant-filled) .p-inputnumber-increment-button {
    border-block-end: 1px solid ${r86("inputtext.border.color")}
}
`;
var n16 = { root: r36, button: o38, colorScheme: e26, css: css12 };

// node_modules/@primeuix/themes/material/inputotp/index.mjs
var r37 = { gap: "0.5rem" };
var t16 = { width: "3rem", sm: { width: "2.5rem" }, lg: { width: "3.5rem" } };
var e27 = { root: r37, input: t16, css: "" };

// node_modules/@primeuix/themes/material/inputtext/index.mjs
var o39 = { background: "{form.field.background}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", filledHoverBackground: "{form.field.filled.hover.background}", filledFocusBackground: "{form.field.filled.focus.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", color: "{form.field.color}", disabledColor: "{form.field.disabled.color}", placeholderColor: "{form.field.placeholder.color}", invalidPlaceholderColor: "{form.field.invalid.placeholder.color}", shadow: "{form.field.shadow}", paddingX: "{form.field.padding.x}", paddingY: "{form.field.padding.y}", borderRadius: "{form.field.border.radius}", focusRing: { width: "{form.field.focus.ring.width}", style: "{form.field.focus.ring.style}", color: "{form.field.focus.ring.color}", offset: "{form.field.focus.ring.offset}", shadow: "{form.field.focus.ring.shadow}" }, transitionDuration: "{form.field.transition.duration}", sm: { fontSize: "{form.field.sm.font.size}", paddingX: "{form.field.sm.padding.x}", paddingY: "{form.field.sm.padding.y}" }, lg: { fontSize: "{form.field.lg.font.size}", paddingX: "{form.field.lg.padding.x}", paddingY: "{form.field.lg.padding.y}" } };
var css13 = ({ dt: o88 }) => `
.p-inputtext.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o88("inputtext.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("inputtext.focus.border.color")}, ${o88("inputtext.focus.border.color")}), linear-gradient(to bottom, ${o88("inputtext.border.color")}, ${o88("inputtext.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-inputtext.p-variant-filled:enabled:hover {
    background: ${o88("inputtext.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("inputtext.focus.border.color")}, ${o88("inputtext.focus.border.color")}), linear-gradient(to bottom, ${o88("inputtext.hover.border.color")}, ${o88("inputtext.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-inputtext.p-variant-filled:enabled:focus {
    outline: 0 none;
    background: ${o88("inputtext.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("inputtext.focus.border.color")}, ${o88("inputtext.focus.border.color")}), linear-gradient(to bottom, ${o88("inputtext.border.color")}, ${o88("inputtext.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-inputtext.p-variant-filled:enabled:hover:focus {
    background-image: linear-gradient(to bottom, ${o88("inputtext.focus.border.color")}, ${o88("inputtext.focus.border.color")}), linear-gradient(to bottom, ${o88("inputtext.hover.border.color")}, ${o88("inputtext.hover.border.color")});
}

.p-inputtext.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o88("inputtext.invalid.border.color")}, ${o88("inputtext.invalid.border.color")}), linear-gradient(to bottom, ${o88("inputtext.invalid.border.color")}, ${o88("inputtext.invalid.border.color")});
}

.p-inputtext.p-variant-filled.p-invalid:enabled:focus {
    background-image: linear-gradient(to bottom, ${o88("inputtext.invalid.border.color")}, ${o88("inputtext.invalid.border.color")}), linear-gradient(to bottom, ${o88("inputtext.invalid.border.color")}, ${o88("inputtext.invalid.border.color")});
}
`;
var r38 = { root: o39, css: css13 };

// node_modules/@primeuix/themes/material/knob/index.mjs
var o40 = { transitionDuration: "{transition.duration}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var r39 = { background: "{primary.color}" };
var t17 = { background: "{content.border.color}" };
var n17 = { color: "{text.muted.color}" };
var s7 = { root: o40, value: r39, range: t17, text: n17, css: "" };

// node_modules/@primeuix/themes/material/listbox/index.mjs
var o41 = { background: "{form.field.background}", disabledBackground: "{form.field.disabled.background}", borderColor: "{form.field.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", color: "{form.field.color}", disabledColor: "{form.field.disabled.color}", shadow: "{form.field.shadow}", borderRadius: "{form.field.border.radius}", transitionDuration: "{form.field.transition.duration}" };
var r40 = { padding: "{list.padding}", gap: "{list.gap}", header: { padding: "{list.header.padding}" } };
var i13 = { focusBackground: "{list.option.focus.background}", selectedBackground: "{list.option.selected.background}", selectedFocusBackground: "{list.option.selected.focus.background}", color: "{list.option.color}", focusColor: "{list.option.focus.color}", selectedColor: "{list.option.selected.color}", selectedFocusColor: "{list.option.selected.focus.color}", padding: "{list.option.padding}", borderRadius: "{list.option.border.radius}" };
var d14 = { background: "{list.option.group.background}", color: "{list.option.group.color}", fontWeight: "{list.option.group.font.weight}", padding: "{list.option.group.padding}" };
var t18 = { color: "{list.option.color}", gutterStart: "-0.375rem", gutterEnd: "0.375rem" };
var e28 = { padding: "{list.option.padding}" };
var l10 = { light: { option: { stripedBackground: "{surface.50}" } }, dark: { option: { stripedBackground: "{surface.900}" } } };
var css14 = "\n.p-listbox-option {\n    transition: none;\n}\n";
var n18 = { root: o41, list: r40, option: i13, optionGroup: d14, checkmark: t18, emptyMessage: e28, colorScheme: l10, css: css14 };

// node_modules/@primeuix/themes/material/megamenu/index.mjs
var o42 = { background: "{content.background}", borderColor: "{content.border.color}", borderRadius: "{content.border.radius}", color: "{content.color}", gap: "0.5rem", verticalOrientation: { padding: "{navigation.list.padding}", gap: "{navigation.list.gap}" }, horizontalOrientation: { padding: "0.5rem 0.75rem", gap: "0.5rem" }, transitionDuration: "{transition.duration}" };
var n19 = { borderRadius: "{content.border.radius}", padding: "{navigation.item.padding}" };
var a19 = { focusBackground: "{navigation.item.focus.background}", activeBackground: "{navigation.item.active.background}", color: "{navigation.item.color}", focusColor: "{navigation.item.focus.color}", activeColor: "{navigation.item.active.color}", padding: "{navigation.item.padding}", borderRadius: "{navigation.item.border.radius}", gap: "{navigation.item.gap}", icon: { color: "{navigation.item.icon.color}", focusColor: "{navigation.item.icon.focus.color}", activeColor: "{navigation.item.icon.active.color}" } };
var i14 = { padding: "0", background: "{content.background}", borderColor: "transparent", borderRadius: "{content.border.radius}", color: "{content.color}", shadow: "{overlay.navigation.shadow}", gap: "0.5rem" };
var t19 = { padding: "{navigation.list.padding}", gap: "{navigation.list.gap}" };
var r41 = { padding: "{navigation.submenu.label.padding}", fontWeight: "{navigation.submenu.label.font.weight}", background: "{navigation.submenu.label.background.}", color: "{navigation.submenu.label.color}" };
var e29 = { size: "{navigation.submenu.icon.size}", color: "{navigation.submenu.icon.color}", focusColor: "{navigation.submenu.icon.focus.color}", activeColor: "{navigation.submenu.icon.active.color}" };
var c12 = { borderColor: "{content.border.color}" };
var d15 = { borderRadius: "50%", size: "2.5rem", color: "{text.muted.color}", hoverColor: "{text.hover.muted.color}", hoverBackground: "{content.hover.background}", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var css15 = ({ dt: o88 }) => `
.p-megamenu-button:focus-visible {
    background: ${o88("navigation.item.active.background")};
}
`;
var g4 = { root: o42, baseItem: n19, item: a19, overlay: i14, submenu: t19, submenuLabel: r41, submenuIcon: e29, separator: c12, mobileButton: d15, css: css15 };

// node_modules/@primeuix/themes/material/menu/index.mjs
var o43 = { background: "{content.background}", borderColor: "{content.border.color}", color: "{content.color}", borderRadius: "{content.border.radius}", shadow: "{overlay.navigation.shadow}", transitionDuration: "{transition.duration}" };
var n20 = { padding: "{navigation.list.padding}", gap: "{navigation.list.gap}" };
var a20 = { focusBackground: "{navigation.item.focus.background}", color: "{navigation.item.color}", focusColor: "{navigation.item.focus.color}", padding: "{navigation.item.padding}", borderRadius: "{navigation.item.border.radius}", gap: "{navigation.item.gap}", icon: { color: "{navigation.item.icon.color}", focusColor: "{navigation.item.icon.focus.color}" } };
var i15 = { padding: "{navigation.submenu.label.padding}", fontWeight: "{navigation.submenu.label.font.weight}", background: "{navigation.submenu.label.background}", color: "{navigation.submenu.label.color}" };
var r42 = { borderColor: "{content.border.color}" };
var css16 = "\n.p-menu-overlay {\n    border-color: transparent;\n}\n";
var t20 = { root: o43, list: n20, item: a20, submenuLabel: i15, separator: r42, css: css16 };

// node_modules/@primeuix/themes/material/menubar/index.mjs
var o44 = { background: "{content.background}", borderColor: "{content.border.color}", borderRadius: "{content.border.radius}", color: "{content.color}", gap: "0.5rem", padding: "0.5rem 0.75rem", transitionDuration: "{transition.duration}" };
var n21 = { borderRadius: "{content.border.radius}", padding: "{navigation.item.padding}" };
var i16 = { focusBackground: "{navigation.item.focus.background}", activeBackground: "{navigation.item.active.background}", color: "{navigation.item.color}", focusColor: "{navigation.item.focus.color}", activeColor: "{navigation.item.active.color}", padding: "{navigation.item.padding}", borderRadius: "{navigation.item.border.radius}", gap: "{navigation.item.gap}", icon: { color: "{navigation.item.icon.color}", focusColor: "{navigation.item.icon.focus.color}", activeColor: "{navigation.item.icon.active.color}" } };
var a21 = { padding: "{navigation.list.padding}", gap: "{navigation.list.gap}", background: "{content.background}", borderColor: "transparent", borderRadius: "{content.border.radius}", shadow: "{overlay.navigation.shadow}", mobileIndent: "1rem", icon: { size: "{navigation.submenu.icon.size}", color: "{navigation.submenu.icon.color}", focusColor: "{navigation.submenu.icon.focus.color}", activeColor: "{navigation.submenu.icon.active.color}" } };
var r43 = { borderColor: "{content.border.color}" };
var t21 = { borderRadius: "50%", size: "2.5rem", color: "{text.muted.color}", hoverColor: "{text.hover.muted.color}", hoverBackground: "{content.hover.background}", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var css17 = ({ dt: o88 }) => `
.p-menubar-button:focus-visible {
    background: ${o88("navigation.item.active.background")};
}
`;
var e30 = { root: o44, baseItem: n21, item: i16, submenu: a21, separator: r43, mobileButton: t21, css: css17 };

// node_modules/@primeuix/themes/material/message/index.mjs
var o45 = { borderRadius: "{content.border.radius}", borderWidth: "0", transitionDuration: "{transition.duration}" };
var r44 = { padding: "1rem 1.25rem", gap: "0.5rem", sm: { padding: "0.625rem 0.625rem" }, lg: { padding: "0.825rem 0.825rem" } };
var e31 = { fontSize: "1rem", fontWeight: "500", sm: { fontSize: "0.875rem" }, lg: { fontSize: "1.125rem" } };
var n22 = { size: "1.25rem", sm: { size: "1rem" }, lg: { size: "1.5rem" } };
var l11 = { width: "2rem", height: "2rem", borderRadius: "50%", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", offset: "{focus.ring.offset}" } };
var c13 = { size: "1rem", sm: { size: "0.875rem" }, lg: { size: "1.125rem" } };
var s8 = { root: { borderWidth: "1px" } };
var a22 = { content: { padding: "0" } };
var d16 = { light: { info: { background: "color-mix(in srgb, {blue.50}, transparent 5%)", borderColor: "{blue.200}", color: "{blue.600}", shadow: "none", closeButton: { hoverBackground: "{blue.100}", focusRing: { color: "{blue.600}", shadow: "none" } }, outlined: { color: "{blue.600}", borderColor: "{blue.600}" }, simple: { color: "{blue.600}" } }, success: { background: "color-mix(in srgb, {green.50}, transparent 5%)", borderColor: "{green.200}", color: "{green.600}", shadow: "none", closeButton: { hoverBackground: "{green.100}", focusRing: { color: "{green.600}", shadow: "none" } }, outlined: { color: "{green.600}", borderColor: "{green.600}" }, simple: { color: "{green.600}" } }, warn: { background: "color-mix(in srgb,{yellow.50}, transparent 5%)", borderColor: "{yellow.200}", color: "{yellow.900}", shadow: "none", closeButton: { hoverBackground: "{yellow.100}", focusRing: { color: "{yellow.600}", shadow: "none" } }, outlined: { color: "{yellow.900}", borderColor: "{yellow.900}" }, simple: { color: "{yellow.900}" } }, error: { background: "color-mix(in srgb, {red.50}, transparent 5%)", borderColor: "{red.200}", color: "{red.600}", shadow: "none", closeButton: { hoverBackground: "{red.100}", focusRing: { color: "{red.600}", shadow: "none" } }, outlined: { color: "{red.600}", borderColor: "{red.600}" }, simple: { color: "{red.600}" } }, secondary: { background: "{surface.100}", borderColor: "{surface.200}", color: "{surface.600}", shadow: "none", closeButton: { hoverBackground: "{surface.200}", focusRing: { color: "{surface.600}", shadow: "none" } }, outlined: { color: "{surface.600}", borderColor: "{surface.600}" }, simple: { color: "{surface.600}" } }, contrast: { background: "{surface.900}", borderColor: "{surface.950}", color: "{surface.50}", shadow: "none", closeButton: { hoverBackground: "{surface.800}", focusRing: { color: "{surface.50}", shadow: "none" } }, outlined: { color: "{surface.950}", borderColor: "{surface.950}" }, simple: { color: "{surface.950}" } } }, dark: { info: { background: "color-mix(in srgb, {blue.500}, transparent 84%)", borderColor: "color-mix(in srgb, {blue.700}, transparent 64%)", color: "{blue.500}", shadow: "none", closeButton: { hoverBackground: "rgba(255, 255, 255, 0.05)", focusRing: { color: "{blue.500}", shadow: "none" } }, outlined: { color: "{blue.500}", borderColor: "{blue.500}" }, simple: { color: "{blue.500}" } }, success: { background: "color-mix(in srgb, {green.500}, transparent 84%)", borderColor: "color-mix(in srgb, {green.700}, transparent 64%)", color: "{green.500}", shadow: "none", closeButton: { hoverBackground: "rgba(255, 255, 255, 0.05)", focusRing: { color: "{green.500}", shadow: "none" } }, outlined: { color: "{green.500}", borderColor: "{green.500}" }, simple: { color: "{green.500}" } }, warn: { background: "color-mix(in srgb, {yellow.500}, transparent 84%)", borderColor: "color-mix(in srgb, {yellow.700}, transparent 64%)", color: "{yellow.500}", shadow: "none", closeButton: { hoverBackground: "rgba(255, 255, 255, 0.05)", focusRing: { color: "{yellow.500}", shadow: "none" } }, outlined: { color: "{yellow.500}", borderColor: "{yellow.500}" }, simple: { color: "{yellow.500}" } }, error: { background: "color-mix(in srgb, {red.500}, transparent 84%)", borderColor: "color-mix(in srgb, {red.700}, transparent 64%)", color: "{red.500}", shadow: "none", closeButton: { hoverBackground: "rgba(255, 255, 255, 0.05)", focusRing: { color: "{red.500}", shadow: "none" } }, outlined: { color: "{red.500}", borderColor: "{red.500}" }, simple: { color: "{red.500}" } }, secondary: { background: "{surface.800}", borderColor: "{surface.700}", color: "{surface.300}", shadow: "none", closeButton: { hoverBackground: "{surface.700}", focusRing: { color: "{surface.300}", shadow: "none" } }, outlined: { color: "{surface.400}", borderColor: "{surface.400}" }, simple: { color: "{surface.400}" } }, contrast: { background: "{surface.0}", borderColor: "{surface.100}", color: "{surface.950}", shadow: "none", closeButton: { hoverBackground: "{surface.100}", focusRing: { color: "{surface.950}", shadow: "none" } }, outlined: { color: "{surface.0}", borderColor: "{surface.0}" }, simple: { color: "{surface.0}" } } } };
var u4 = { root: o45, content: r44, text: e31, icon: n22, closeButton: l11, closeIcon: c13, outlined: s8, simple: a22, colorScheme: d16, css: "" };

// node_modules/@primeuix/themes/material/metergroup/index.mjs
var e32 = { borderRadius: "{content.border.radius}", gap: "1rem" };
var r45 = { background: "{content.border.color}", size: "0.5rem" };
var a23 = { gap: "0.5rem" };
var o46 = { size: "0.5rem" };
var l12 = { size: "1rem" };
var t22 = { verticalGap: "0.5rem", horizontalGap: "1rem" };
var s9 = { root: e32, meters: r45, label: a23, labelMarker: o46, labelIcon: l12, labelList: t22, css: "" };

// node_modules/@primeuix/themes/material/multiselect/index.mjs
var o47 = { background: "{form.field.background}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", filledHoverBackground: "{form.field.filled.hover.background}", filledFocusBackground: "{form.field.filled.focus.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", color: "{form.field.color}", disabledColor: "{form.field.disabled.color}", placeholderColor: "{form.field.placeholder.color}", invalidPlaceholderColor: "{form.field.invalid.placeholder.color}", shadow: "{form.field.shadow}", paddingX: "{form.field.padding.x}", paddingY: "{form.field.padding.y}", borderRadius: "{form.field.border.radius}", focusRing: { width: "{form.field.focus.ring.width}", style: "{form.field.focus.ring.style}", color: "{form.field.focus.ring.color}", offset: "{form.field.focus.ring.offset}", shadow: "{form.field.focus.ring.shadow}" }, transitionDuration: "{form.field.transition.duration}", sm: { fontSize: "{form.field.sm.font.size}", paddingX: "{form.field.sm.padding.x}", paddingY: "{form.field.sm.padding.y}" }, lg: { fontSize: "{form.field.lg.font.size}", paddingX: "{form.field.lg.padding.x}", paddingY: "{form.field.lg.padding.y}" } };
var r46 = { width: "2.5rem", color: "{form.field.icon.color}" };
var e33 = { background: "{overlay.select.background}", borderColor: "{overlay.select.border.color}", borderRadius: "{overlay.select.border.radius}", color: "{overlay.select.color}", shadow: "{overlay.select.shadow}" };
var l13 = { padding: "{list.padding}", gap: "{list.gap}", header: { padding: "{list.header.padding}" } };
var i17 = { focusBackground: "{list.option.focus.background}", selectedBackground: "{list.option.selected.background}", selectedFocusBackground: "{list.option.selected.focus.background}", color: "{list.option.color}", focusColor: "{list.option.focus.color}", selectedColor: "{list.option.selected.color}", selectedFocusColor: "{list.option.selected.focus.color}", padding: "{list.option.padding}", borderRadius: "{list.option.border.radius}", gap: "0.75rem" };
var d17 = { background: "{list.option.group.background}", color: "{list.option.group.color}", fontWeight: "{list.option.group.font.weight}", padding: "{list.option.group.padding}" };
var t23 = { color: "{form.field.icon.color}" };
var n23 = { borderRadius: "{border.radius.sm}" };
var c14 = { padding: "{list.option.padding}" };
var css18 = ({ dt: o88 }) => `
.p-multiselect.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o88("multiselect.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("multiselect.focus.border.color")}, ${o88("multiselect.focus.border.color")}), linear-gradient(to bottom, ${o88("multiselect.border.color")}, ${o88("multiselect.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-multiselect.p-variant-filled:not(.p-disabled):hover {
    background: ${o88("multiselect.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("multiselect.focus.border.color")}, ${o88("multiselect.focus.border.color")}), linear-gradient(to bottom, ${o88("multiselect.hover.border.color")}, ${o88("multiselect.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-multiselect.p-variant-filled:not(.p-disabled).p-focus {
    outline: 0 none;
    background: ${o88("multiselect.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("multiselect.focus.border.color")}, ${o88("multiselect.focus.border.color")}), linear-gradient(to bottom, ${o88("multiselect.border.color")}, ${o88("multiselect.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-multiselect.p-variant-filled:not(.p-disabled).p-focus:hover {
    background-image: linear-gradient(to bottom, ${o88("multiselect.focus.border.color")}, ${o88("multiselect.focus.border.color")}), linear-gradient(to bottom, ${o88("multiselect.hover.border.color")}, ${o88("multiselect.hover.border.color")});
}

.p-multiselect.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o88("multiselect.invalid.border.color")}, ${o88("multiselect.invalid.border.color")}), linear-gradient(to bottom, ${o88("multiselect.invalid.border.color")}, ${o88("multiselect.invalid.border.color")});
}

.p-multiselect.p-variant-filled.p-invalid:not(.p-disabled).p-focus  {
    background-image: linear-gradient(to bottom, ${o88("multiselect.invalid.border.color")}, ${o88("multiselect.invalid.border.color")}), linear-gradient(to bottom, ${o88("multiselect.invalid.border.color")}, ${o88("multiselect.invalid.border.color")});
}

.p-multiselect-option {
    transition: none;
}
`;
var a24 = { root: o47, dropdown: r46, overlay: e33, list: l13, option: i17, optionGroup: d17, chip: n23, clearIcon: t23, emptyMessage: c14, css: css18 };

// node_modules/@primeuix/themes/material/orderlist/index.mjs
var r47 = { gap: "1.125rem" };
var a25 = { gap: "0.5rem" };
var o48 = { root: r47, controls: a25, css: "" };

// node_modules/@primeuix/themes/material/organizationchart/index.mjs
var o49 = { gutter: "0.75rem", transitionDuration: "{transition.duration}" };
var r48 = { background: "{content.background}", hoverBackground: "{content.hover.background}", selectedBackground: "{highlight.background}", borderColor: "{content.border.color}", color: "{content.color}", selectedColor: "{highlight.color}", hoverColor: "{content.hover.color}", padding: "1rem 1.25rem", toggleablePadding: "1rem 1.25rem 1.5rem 1.25rem", borderRadius: "{content.border.radius}" };
var e34 = { background: "{content.background}", hoverBackground: "{content.hover.background}", borderColor: "{content.border.color}", color: "{text.muted.color}", hoverColor: "{text.color}", size: "1.75rem", borderRadius: "50%", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var t24 = { color: "{content.border.color}", borderRadius: "{content.border.radius}", height: "24px" };
var n24 = { root: o49, node: r48, nodeToggleButton: e34, connector: t24, css: "" };

// node_modules/@primeuix/themes/material/overlaybadge/index.mjs
var o50 = { outline: { width: "2px", color: "{content.background}" } };
var t25 = { root: o50, css: "" };

// node_modules/@primeuix/themes/material/paginator/index.mjs
var o51 = { padding: "0.5rem 1rem", gap: "0.25rem", borderRadius: "{content.border.radius}", background: "{content.background}", color: "{content.color}", transitionDuration: "{transition.duration}" };
var r49 = { background: "transparent", hoverBackground: "{content.hover.background}", selectedBackground: "{highlight.background}", color: "{text.muted.color}", hoverColor: "{text.hover.muted.color}", selectedColor: "{highlight.color}", width: "2.5rem", height: "2.5rem", borderRadius: "50%", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var t26 = { color: "{text.muted.color}" };
var e35 = { maxWidth: "2.5rem" };
var n25 = { root: o51, navButton: r49, currentPageReport: t26, jumpToPageInput: e35, css: "" };

// node_modules/@primeuix/themes/material/panel/index.mjs
var r50 = { background: "{content.background}", borderColor: "{content.border.color}", color: "{content.color}", borderRadius: "{content.border.radius}" };
var o52 = { background: "transparent", color: "{text.color}", padding: "1.25rem", borderColor: "{content.border.color}", borderWidth: "0", borderRadius: "0" };
var e36 = { padding: "0.5rem 1.25rem" };
var d18 = { fontWeight: "600" };
var t27 = { padding: "0 1.25rem 1.25rem 1.25rem" };
var n26 = { padding: "0 1.25rem 1.25rem 1.25rem" };
var a26 = { root: r50, header: o52, toggleableHeader: e36, title: d18, content: t27, footer: n26, css: "" };

// node_modules/@primeuix/themes/material/panelmenu/index.mjs
var o53 = { gap: "0", transitionDuration: "{transition.duration}" };
var n27 = { background: "{content.background}", borderColor: "{content.border.color}", borderWidth: "0", color: "{content.color}", padding: "0", borderRadius: "0", first: { borderWidth: "0", topBorderRadius: "{content.border.radius}" }, last: { borderWidth: "0", bottomBorderRadius: "{content.border.radius}" } };
var r51 = { focusBackground: "{navigation.item.focus.background}", color: "{navigation.item.color}", focusColor: "{navigation.item.focus.color}", gap: "0.5rem", padding: "{navigation.item.padding}", borderRadius: "{content.border.radius}", icon: { color: "{navigation.item.icon.color}", focusColor: "{navigation.item.icon.focus.color}" } };
var a27 = { indent: "1rem" };
var i18 = { color: "{navigation.submenu.icon.color}", focusColor: "{navigation.submenu.icon.focus.color}" };
var css19 = ({ dt: o88 }) => `
.p-panelmenu-panel {
    box-shadow: 0 0 0 1px ${o88("panelmenu.panel.border.color")};
    transition: margin ${o88("panelmenu.transition.duration")};
}

.p-panelmenu-panel:has(.p-panelmenu-header-active) {
    margin: 1rem 0;
}

.p-panelmenu-panel:first-child {
    border-top-left-radius: ${o88("content.border.radius")};
    border-top-right-radius: ${o88("content.border.radius")};
    margin-top: 0;
}

.p-panelmenu-panel:last-child {
    border-bottom-left-radius: ${o88("content.border.radius")};
    border-bottom-right-radius: ${o88("content.border.radius")};
    margin-bottom: 0;
}

.p-accordionpanel:not(.p-disabled) .p-accordionheader:focus-visible {
    background: ${o88("navigation.item.active.background")};
}
`;
var e37 = { root: o53, panel: n27, item: r51, submenu: a27, submenuIcon: i18, css: css19 };

// node_modules/@primeuix/themes/material/password/index.mjs
var r52 = { background: "{content.border.color}", borderRadius: "{content.border.radius}", height: ".75rem" };
var o54 = { color: "{form.field.icon.color}" };
var e38 = { background: "{overlay.popover.background}", borderColor: "{overlay.popover.border.color}", borderRadius: "{overlay.popover.border.radius}", color: "{overlay.popover.color}", padding: "{overlay.popover.padding}", shadow: "{overlay.popover.shadow}" };
var a28 = { gap: "0.5rem" };
var d19 = { light: { strength: { weakBackground: "{red.500}", mediumBackground: "{amber.500}", strongBackground: "{green.500}" } }, dark: { strength: { weakBackground: "{red.400}", mediumBackground: "{amber.400}", strongBackground: "{green.400}" } } };
var n28 = { meter: r52, icon: o54, overlay: e38, content: a28, colorScheme: d19, css: "" };

// node_modules/@primeuix/themes/material/picklist/index.mjs
var r53 = { gap: "1.125rem" };
var a29 = { gap: "0.5rem" };
var o55 = { root: r53, controls: a29, css: "" };

// node_modules/@primeuix/themes/material/popover/index.mjs
var o56 = { background: "{overlay.popover.background}", borderColor: "{overlay.popover.border.color}", color: "{overlay.popover.color}", borderRadius: "{overlay.popover.border.radius}", shadow: "{overlay.popover.shadow}", gutter: "10px", arrowOffset: "1.25rem" };
var r54 = { padding: "{overlay.popover.padding}" };
var e39 = { root: o56, content: r54, css: "" };

// node_modules/@primeuix/themes/material/progressbar/index.mjs
var r55 = { background: "{content.border.color}", borderRadius: "{content.border.radius}", height: "1rem" };
var o57 = { background: "{primary.color}" };
var e40 = { color: "{primary.contrast.color}", fontSize: "0.75rem", fontWeight: "600" };
var t28 = { root: r55, value: o57, label: e40, css: "" };

// node_modules/@primeuix/themes/material/progressspinner/index.mjs
var o58 = { light: { root: { colorOne: "{red.500}", colorTwo: "{blue.500}", colorThree: "{green.500}", colorFour: "{yellow.500}" } }, dark: { root: { colorOne: "{red.400}", colorTwo: "{blue.400}", colorThree: "{green.400}", colorFour: "{yellow.400}" } } };
var r56 = { colorScheme: o58, css: "" };

// node_modules/@primeuix/themes/material/radiobutton/index.mjs
var o59 = { width: "20px", height: "20px", background: "{form.field.background}", checkedBackground: "{primary.contrast.color}", checkedHoverBackground: "{primary.contrast.color}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", checkedBorderColor: "{primary.color}", checkedHoverBorderColor: "{primary.color}", checkedFocusBorderColor: "{primary.color}", checkedDisabledBorderColor: "{form.field.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", shadow: "{form.field.shadow}", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" }, transitionDuration: "{form.field.transition.duration}", sm: { width: "16px", height: "16px" }, lg: { width: "24px", height: "24px" } };
var r57 = { size: "10px", checkedColor: "{primary.color}", checkedHoverColor: "{primary.color}", disabledColor: "{form.field.disabled.color}", sm: { size: "8px" }, lg: { size: "12px" } };
var d20 = { root: o59, icon: r57 };

// node_modules/@primeuix/themes/material/rating/index.mjs
var o60 = { gap: "0.5rem", transitionDuration: "{transition.duration}", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var n29 = { size: "1.125rem", color: "{text.muted.color}", hoverColor: "{primary.color}", activeColor: "{primary.color}" };
var css20 = ({ dt: o88 }) => `
.p-rating:not(.p-disabled):not(.p-readonly) .p-rating-option:hover {
    background: color-mix(in srgb, ${o88("rating.icon.color")}, transparent 96%);
    box-shadow: 0 0 1px 8px color-mix(in srgb, ${o88("rating.icon.color")}, transparent 96%);
}

.p-rating:not(.p-disabled):not(.p-readonly) .p-rating-option-active:hover {
    background: color-mix(in srgb, ${o88("rating.icon.active.color")}, transparent 92%);
    box-shadow: 0 0 1px 8px color-mix(in srgb, ${o88("rating.icon.active.color")}, transparent 92%);
}

.p-rating-option.p-focus-visible {
    background: color-mix(in srgb, ${o88("rating.icon.active.color")}, transparent 84%);
    box-shadow: 0 0 1px 8px color-mix(in srgb, ${o88("rating.icon.active.color")}, transparent 84%);
}
`;
var r58 = { root: o60, icon: n29, css: css20 };

// node_modules/@primeuix/themes/material/ripple/index.mjs
var r59 = { light: { root: { background: "rgba(0,0,0,0.1)" } }, dark: { root: { background: "rgba(255,255,255,0.3)" } } };
var o61 = { colorScheme: r59, css: "" };

// node_modules/@primeuix/themes/material/scrollpanel/index.mjs
var r60 = { transitionDuration: "{transition.duration}" };
var o62 = { size: "9px", borderRadius: "{border.radius.sm}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var s10 = { light: { bar: { background: "{surface.200}" } }, dark: { bar: { background: "{surface.700}" } } };
var a30 = { root: r60, bar: o62, colorScheme: s10, css: "" };

// node_modules/@primeuix/themes/material/select/index.mjs
var o63 = { background: "{form.field.background}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", filledHoverBackground: "{form.field.filled.hover.background}", filledFocusBackground: "{form.field.filled.focus.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", color: "{form.field.color}", disabledColor: "{form.field.disabled.color}", placeholderColor: "{form.field.placeholder.color}", invalidPlaceholderColor: "{form.field.invalid.placeholder.color}", shadow: "{form.field.shadow}", paddingX: "{form.field.padding.x}", paddingY: "{form.field.padding.y}", borderRadius: "{form.field.border.radius}", focusRing: { width: "{form.field.focus.ring.width}", style: "{form.field.focus.ring.style}", color: "{form.field.focus.ring.color}", offset: "{form.field.focus.ring.offset}", shadow: "{form.field.focus.ring.shadow}" }, transitionDuration: "{form.field.transition.duration}", sm: { fontSize: "{form.field.sm.font.size}", paddingX: "{form.field.sm.padding.x}", paddingY: "{form.field.sm.padding.y}" }, lg: { fontSize: "{form.field.lg.font.size}", paddingX: "{form.field.lg.padding.x}", paddingY: "{form.field.lg.padding.y}" } };
var r61 = { width: "2.5rem", color: "{form.field.icon.color}" };
var e41 = { background: "{overlay.select.background}", borderColor: "{overlay.select.border.color}", borderRadius: "{overlay.select.border.radius}", color: "{overlay.select.color}", shadow: "{overlay.select.shadow}" };
var d21 = { padding: "{list.padding}", gap: "{list.gap}", header: { padding: "{list.header.padding}" } };
var l14 = { focusBackground: "{list.option.focus.background}", selectedBackground: "{list.option.selected.background}", selectedFocusBackground: "{list.option.selected.focus.background}", color: "{list.option.color}", focusColor: "{list.option.focus.color}", selectedColor: "{list.option.selected.color}", selectedFocusColor: "{list.option.selected.focus.color}", padding: "{list.option.padding}", borderRadius: "{list.option.border.radius}" };
var i19 = { background: "{list.option.group.background}", color: "{list.option.group.color}", fontWeight: "{list.option.group.font.weight}", padding: "{list.option.group.padding}" };
var n30 = { color: "{form.field.icon.color}" };
var t29 = { color: "{list.option.color}", gutterStart: "-0.375rem", gutterEnd: "0.375rem" };
var c15 = { padding: "{list.option.padding}" };
var css21 = ({ dt: o88 }) => `
.p-select.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o88("select.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("select.focus.border.color")}, ${o88("select.focus.border.color")}), linear-gradient(to bottom, ${o88("select.border.color")}, ${o88("select.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-select.p-variant-filled:not(.p-disabled):hover {
    background: ${o88("select.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("select.focus.border.color")}, ${o88("select.focus.border.color")}), linear-gradient(to bottom, ${o88("select.hover.border.color")}, ${o88("select.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-select.p-variant-filled:not(.p-disabled).p-focus {
    outline: 0 none;
    background: ${o88("select.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("select.focus.border.color")}, ${o88("select.focus.border.color")}), linear-gradient(to bottom, ${o88("select.border.color")}, ${o88("select.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-select.p-variant-filled:not(.p-disabled).p-focus:hover {
    background-image: linear-gradient(to bottom, ${o88("select.focus.border.color")}, ${o88("select.focus.border.color")}), linear-gradient(to bottom, ${o88("select.hover.border.color")}, ${o88("select.hover.border.color")});
}

.p-select.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o88("select.invalid.border.color")}, ${o88("select.invalid.border.color")}), linear-gradient(to bottom, ${o88("select.invalid.border.color")}, ${o88("select.invalid.border.color")});
}

.p-select.p-variant-filled.p-invalid:not(.p-disabled).p-focus  {
    background-image: linear-gradient(to bottom, ${o88("select.invalid.border.color")}, ${o88("select.invalid.border.color")}), linear-gradient(to bottom, ${o88("select.invalid.border.color")}, ${o88("select.invalid.border.color")});
}

.p-select-option {
    transition: none;
}
`;
var a31 = { root: o63, dropdown: r61, overlay: e41, list: d21, option: l14, optionGroup: i19, clearIcon: n30, checkmark: t29, emptyMessage: c15, css: css21 };

// node_modules/@primeuix/themes/material/selectbutton/index.mjs
var r62 = { borderRadius: "{form.field.border.radius}" };
var o64 = { light: { root: { invalidBorderColor: "{form.field.invalid.border.color}" } }, dark: { root: { invalidBorderColor: "{form.field.invalid.border.color}" } } };
var d22 = { root: r62, colorScheme: o64, css: "" };

// node_modules/@primeuix/themes/material/skeleton/index.mjs
var r63 = { borderRadius: "{content.border.radius}" };
var a32 = { light: { root: { background: "{surface.200}", animationBackground: "rgba(255,255,255,0.4)" } }, dark: { root: { background: "rgba(255, 255, 255, 0.06)", animationBackground: "rgba(255, 255, 255, 0.04)" } } };
var o65 = { root: r63, colorScheme: a32, css: "" };

// node_modules/@primeuix/themes/material/slider/index.mjs
var r64 = { transitionDuration: "{transition.duration}" };
var o66 = { background: "{content.border.color}", borderRadius: "{border.radius.xs}", size: "2px" };
var n31 = { background: "{primary.color}" };
var a33 = { width: "18px", height: "18px", borderRadius: "50%", background: "{primary.color}", hoverBackground: "{primary.color}", content: { borderRadius: "50%", background: "{primary.color}", hoverBackground: "{primary.color}", width: "18px", height: "18px", shadow: "0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12)" }, focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var css22 = ({ dt: r86 }) => `
.p-slider-handle {
    transition: box-shadow ${r86("slider.transition.duration")};
}

.p-slider:not(.p-disabled) .p-slider-handle:hover {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${r86("slider.handle.background")}, transparent 92%);
}

.p-slider-handle:focus-visible,
.p-slider:not(.p-disabled) .p-slider-handle:focus:hover {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${r86("slider.handle.background")}, transparent 84%);
}
`;
var d23 = { root: r64, track: o66, range: n31, handle: a33, css: css22 };

// node_modules/@primeuix/themes/material/speeddial/index.mjs
var t30 = { gap: "0.5rem", transitionDuration: "{transition.duration}" };
var a34 = { root: t30, css: "" };

// node_modules/@primeuix/themes/material/splitbutton/index.mjs
var r65 = { borderRadius: "{form.field.border.radius}", roundedBorderRadius: "2rem", raisedShadow: "0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)" };
var d24 = { root: r65, css: "" };

// node_modules/@primeuix/themes/material/splitter/index.mjs
var o67 = { background: "{content.background}", borderColor: "{content.border.color}", color: "{content.color}", transitionDuration: "{transition.duration}" };
var r66 = { background: "{content.border.color}" };
var n32 = { size: "24px", background: "transparent", borderRadius: "{content.border.radius}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var t31 = { root: o67, gutter: r66, handle: n32, css: "" };

// node_modules/@primeuix/themes/material/stepper/index.mjs
var r67 = { transitionDuration: "{transition.duration}" };
var o68 = { background: "{content.border.color}", activeBackground: "{primary.color}", margin: "0 0 0 1.625rem", size: "2px" };
var e42 = { padding: "0.5rem", gap: "1rem" };
var t32 = { padding: "0.75rem 1rem", borderRadius: "{content.border.radius}", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" }, gap: "0.5rem" };
var a35 = { color: "{text.muted.color}", activeColor: "{text.color}", fontWeight: "500" };
var n33 = { activeBackground: "{primary.color}", activeBorderColor: "{primary.color}", activeColor: "{primary.contrast.color}", size: "2rem", fontSize: "1.143rem", fontWeight: "500", borderRadius: "50%", shadow: "none" };
var c16 = { padding: "0.875rem 0.5rem 1.125rem 0.5rem" };
var i20 = { background: "{content.background}", color: "{content.color}", padding: "0", indent: "1rem" };
var d25 = { light: { stepNumber: { background: "{surface.400}", borderColor: "{surface.400}", color: "{surface.0}" } }, dark: { stepNumber: { background: "{surface.200}", borderColor: "{surface.200}", color: "{surface.900}" } } };
var css23 = ({ dt: r86 }) => `
.p-step-header:focus-visible {
    background: ${r86("navigation.item.active.background")};
}
`;
var s11 = { root: r67, separator: o68, step: e42, stepHeader: t32, stepTitle: a35, stepNumber: n33, steppanels: c16, steppanel: i20, colorScheme: d25, css: css23 };

// node_modules/@primeuix/themes/material/steps/index.mjs
var o69 = { transitionDuration: "{transition.duration}" };
var r68 = { background: "{content.border.color}" };
var t33 = { borderRadius: "{content.border.radius}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" }, gap: "0.5rem" };
var e43 = { color: "{text.muted.color}", activeColor: "{primary.color}", fontWeight: "500" };
var c17 = { background: "{content.background}", activeBackground: "{content.background}", borderColor: "{content.border.color}", activeBorderColor: "{content.border.color}", color: "{text.muted.color}", activeColor: "{primary.color}", size: "2rem", fontSize: "1.143rem", fontWeight: "500", borderRadius: "50%", shadow: "0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)" };
var n34 = { root: o69, separator: r68, itemLink: t33, itemLabel: e43, itemNumber: c17, css: "" };

// node_modules/@primeuix/themes/material/tabmenu/index.mjs
var o70 = { transitionDuration: "{transition.duration}" };
var r69 = { borderWidth: "0 0 1px 0", background: "{content.background}", borderColor: "{content.border.color}" };
var t34 = { background: "transparent", hoverBackground: "transparent", activeBackground: "transparent", borderWidth: "0 0 1px 0", borderColor: "{content.border.color}", hoverBorderColor: "{content.border.color}", activeBorderColor: "{primary.color}", color: "{text.muted.color}", hoverColor: "{text.color}", activeColor: "{primary.color}", padding: "1rem 1.125rem", fontWeight: "600", margin: "0 0 -1px 0", gap: "0.5rem", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var e44 = { color: "{text.muted.color}", hoverColor: "{text.color}", activeColor: "{primary.color}" };
var c18 = { height: "1px", bottom: "-1px", background: "{primary.color}" };
var n35 = { root: o70, tablist: r69, item: t34, itemIcon: e44, activeBar: c18, css: "" };

// node_modules/@primeuix/themes/material/tabs/index.mjs
var o71 = { transitionDuration: "{transition.duration}" };
var r70 = { borderWidth: "0 0 1px 0", background: "{content.background}", borderColor: "{content.border.color}" };
var n36 = { background: "transparent", hoverBackground: "{content.hover.background}", activeBackground: "transparent", borderWidth: "0 0 1px 0", borderColor: "{content.border.color}", hoverBorderColor: "{content.border.color}", activeBorderColor: "{primary.color}", color: "{text.color}", hoverColor: "{text.color}", activeColor: "{primary.color}", padding: "1rem 1.25rem", fontWeight: "600", margin: "0 0 -1px 0", gap: "0.5rem", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var t35 = { background: "{content.background}", color: "{content.color}", padding: "1.25rem 1.25rem 1.25rem 1.25rem", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var e45 = { background: "{content.background}", color: "{text.muted.color}", hoverColor: "{text.color}", width: "3rem", shadow: "none", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" } };
var a36 = { height: "2px", bottom: "-1px", background: "{primary.color}" };
var css24 = ({ dt: o88 }) => `
.p-tabs-scrollable .p-tab {
    flex-grow: 0
}

.p-tab-active {
    --p-ripple-background: color-mix(in srgb, ${o88("primary.color")}, transparent 90%);
}

.p-tab:not(.p-disabled):focus-visible {
    background: ${o88("navigation.item.active.background")};
}

.p-tablist-nav-button:focus-visible {
    background: ${o88("navigation.item.active.background")};
}
`;
var c19 = { root: o71, tablist: r70, tab: n36, tabpanel: t35, navButton: e45, activeBar: a36, css: css24 };

// node_modules/@primeuix/themes/material/tabview/index.mjs
var o72 = { transitionDuration: "{transition.duration}" };
var r71 = { background: "{content.background}", borderColor: "{content.border.color}" };
var t36 = { borderColor: "{content.border.color}", activeBorderColor: "{primary.color}", color: "{text.muted.color}", hoverColor: "{text.color}", activeColor: "{primary.color}" };
var n37 = { background: "{content.background}", color: "{content.color}" };
var c20 = { background: "{content.background}", color: "{text.muted.color}", hoverColor: "{text.color}" };
var a37 = { light: { navButton: { shadow: "0px 0px 10px 50px rgba(255, 255, 255, 0.6)" } }, dark: { navButton: { shadow: "0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)" } } };
var e46 = { root: o72, tabList: r71, tab: t36, tabPanel: n37, navButton: c20, colorScheme: a37, css: "" };

// node_modules/@primeuix/themes/material/tag/index.mjs
var r72 = { fontSize: "0.875rem", fontWeight: "700", padding: "0.25rem 0.5rem", gap: "0.25rem", borderRadius: "{content.border.radius}", roundedBorderRadius: "{border.radius.xl}" };
var o73 = { size: "0.75rem" };
var c21 = { light: { primary: { background: "{primary.color}", color: "{primary.contrast.color}" }, secondary: { background: "{surface.100}", color: "{surface.600}" }, success: { background: "{green.500}", color: "{surface.0}" }, info: { background: "{sky.500}", color: "{surface.0}" }, warn: { background: "{orange.500}", color: "{surface.0}" }, danger: { background: "{red.500}", color: "{surface.0}" }, contrast: { background: "{surface.950}", color: "{surface.0}" } }, dark: { primary: { background: "{primary.color}", color: "{primary.contrast.color}" }, secondary: { background: "{surface.800}", color: "{surface.300}" }, success: { background: "{green.400}", color: "{green.950}" }, info: { background: "{sky.400}", color: "{sky.950}" }, warn: { background: "{orange.400}", color: "{orange.950}" }, danger: { background: "{red.400}", color: "{red.950}" }, contrast: { background: "{surface.0}", color: "{surface.950}" } } };
var a38 = { root: r72, icon: o73, colorScheme: c21, css: "" };

// node_modules/@primeuix/themes/material/terminal/index.mjs
var r73 = { background: "{form.field.background}", borderColor: "{form.field.border.color}", color: "{form.field.color}", height: "18rem", padding: "{form.field.padding.y} {form.field.padding.x}", borderRadius: "{form.field.border.radius}" };
var o74 = { gap: "0.25rem" };
var d26 = { margin: "2px 0" };
var e47 = { root: r73, prompt: o74, commandResponse: d26, css: "" };

// node_modules/@primeuix/themes/material/textarea/index.mjs
var o75 = { background: "{form.field.background}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", filledHoverBackground: "{form.field.filled.hover.background}", filledFocusBackground: "{form.field.filled.focus.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", color: "{form.field.color}", disabledColor: "{form.field.disabled.color}", placeholderColor: "{form.field.placeholder.color}", invalidPlaceholderColor: "{form.field.invalid.placeholder.color}", shadow: "{form.field.shadow}", paddingX: "{form.field.padding.x}", paddingY: "{form.field.padding.y}", borderRadius: "{form.field.border.radius}", focusRing: { width: "{form.field.focus.ring.width}", style: "{form.field.focus.ring.style}", color: "{form.field.focus.ring.color}", offset: "{form.field.focus.ring.offset}", shadow: "{form.field.focus.ring.shadow}" }, transitionDuration: "{form.field.transition.duration}", sm: { fontSize: "{form.field.sm.font.size}", paddingX: "{form.field.sm.padding.x}", paddingY: "{form.field.sm.padding.y}" }, lg: { fontSize: "{form.field.lg.font.size}", paddingX: "{form.field.lg.padding.x}", paddingY: "{form.field.lg.padding.y}" } };
var css25 = ({ dt: o88 }) => `
.p-textarea.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o88("textarea.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("textarea.focus.border.color")}, ${o88("textarea.focus.border.color")}), linear-gradient(to bottom, ${o88("textarea.border.color")}, ${o88("textarea.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-textarea.p-variant-filled:enabled:hover {
    background: ${o88("textarea.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("textarea.focus.border.color")}, ${o88("textarea.focus.border.color")}), linear-gradient(to bottom, ${o88("textarea.hover.border.color")}, ${o88("textarea.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-textarea.p-variant-filled:enabled:focus {
    outline: 0 none;
    background: ${o88("textarea.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("textarea.focus.border.color")}, ${o88("textarea.focus.border.color")}), linear-gradient(to bottom, ${o88("textarea.border.color")}, ${o88("textarea.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-textarea.p-variant-filled:enabled:hover:focus {
    background-image: linear-gradient(to bottom, ${o88("textarea.focus.border.color")}, ${o88("textarea.focus.border.color")}), linear-gradient(to bottom, ${o88("textarea.hover.border.color")}, ${o88("textarea.hover.border.color")});
}

.p-textarea.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o88("textarea.invalid.border.color")}, ${o88("textarea.invalid.border.color")}), linear-gradient(to bottom, ${o88("textarea.invalid.border.color")}, ${o88("textarea.invalid.border.color")});
}

.p-textarea.p-variant-filled.p-invalid:enabled:focus {
    background-image: linear-gradient(to bottom, ${o88("textarea.invalid.border.color")}, ${o88("textarea.invalid.border.color")}), linear-gradient(to bottom, ${o88("textarea.invalid.border.color")}, ${o88("textarea.invalid.border.color")});
}
`;
var r74 = { root: o75, css: css25 };

// node_modules/@primeuix/themes/material/tieredmenu/index.mjs
var o76 = { background: "{content.background}", borderColor: "{content.border.color}", color: "{content.color}", borderRadius: "{content.border.radius}", shadow: "{overlay.navigation.shadow}", transitionDuration: "{transition.duration}" };
var n38 = { padding: "{navigation.list.padding}", gap: "{navigation.list.gap}" };
var i21 = { focusBackground: "{navigation.item.focus.background}", activeBackground: "{navigation.item.active.background}", color: "{navigation.item.color}", focusColor: "{navigation.item.focus.color}", activeColor: "{navigation.item.active.color}", padding: "{navigation.item.padding}", borderRadius: "{navigation.item.border.radius}", gap: "{navigation.item.gap}", icon: { color: "{navigation.item.icon.color}", focusColor: "{navigation.item.icon.focus.color}", activeColor: "{navigation.item.icon.active.color}" } };
var a39 = { mobileIndent: "1rem" };
var r75 = { size: "{navigation.submenu.icon.size}", color: "{navigation.submenu.icon.color}", focusColor: "{navigation.submenu.icon.focus.color}", activeColor: "{navigation.submenu.icon.active.color}" };
var t37 = { borderColor: "{content.border.color}" };
var css26 = "\n.p-tieredmenu-overlay {\n    border-color: transparent;\n}\n";
var c22 = { root: o76, list: n38, item: i21, submenu: a39, submenuIcon: r75, separator: t37, css: css26 };

// node_modules/@primeuix/themes/material/timeline/index.mjs
var e48 = { minHeight: "5rem" };
var r76 = { eventContent: { padding: "1rem 0" } };
var o77 = { eventContent: { padding: "0 1rem" } };
var n39 = { size: "1.5rem", borderRadius: "50%", borderWidth: "2px", background: "{primary.color}", content: { borderRadius: "50%", size: "0", background: "{primary.color}", insetShadow: "none" } };
var t38 = { color: "{content.border.color}", size: "2px" };
var a40 = { light: { eventMarker: { borderColor: "{surface.0}" } }, dark: { eventMarker: { borderColor: "{surface.900}" } } };
var d27 = { event: e48, horizontal: r76, vertical: o77, eventMarker: n39, eventConnector: t38, colorScheme: a40, css: "" };

// node_modules/@primeuix/themes/material/toast/index.mjs
var o78 = { width: "25rem", borderRadius: "{content.border.radius}", borderWidth: "0", transitionDuration: "{transition.duration}" };
var r77 = { size: "1.25rem" };
var e49 = { padding: "{overlay.popover.padding}", gap: "0.5rem" };
var a41 = { gap: "0.5rem" };
var p4 = { fontWeight: "500", fontSize: "1rem" };
var x = { fontWeight: "500", fontSize: "0.875rem" };
var n40 = { width: "2rem", height: "2rem", borderRadius: "50%", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", offset: "{focus.ring.offset}" } };
var c23 = { size: "1rem" };
var s12 = { light: { blur: "0", info: { background: "{blue.50}", borderColor: "{blue.200}", color: "{blue.600}", detailColor: "{surface.700}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "{blue.100}", focusRing: { color: "{blue.600}", shadow: "none" } } }, success: { background: "{green.50}", borderColor: "{green.200}", color: "{green.600}", detailColor: "{surface.700}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "{green.100}", focusRing: { color: "{green.600}", shadow: "none" } } }, warn: { background: "{yellow.50}", borderColor: "{yellow.200}", color: "{yellow.900}", detailColor: "{surface.700}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "{yellow.100}", focusRing: { color: "{yellow.600}", shadow: "none" } } }, error: { background: "{red.50}", borderColor: "{red.200}", color: "{red.600}", detailColor: "{surface.700}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "{red.100}", focusRing: { color: "{red.600}", shadow: "none" } } }, secondary: { background: "{surface.100}", borderColor: "{surface.200}", color: "{surface.600}", detailColor: "{surface.700}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "{surface.200}", focusRing: { color: "{surface.600}", shadow: "none" } } }, contrast: { background: "{surface.900}", borderColor: "{surface.950}", color: "{surface.50}", detailColor: "{surface.0}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "{surface.800}", focusRing: { color: "{surface.50}", shadow: "none" } } } }, dark: { blur: "10px", info: { background: "color-mix(in srgb, {blue.500}, transparent 36%)", borderColor: "color-mix(in srgb, {blue.700}, transparent 64%)", color: "{surface.0}", detailColor: "{blue.100}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "rgba(255, 255, 255, 0.05)", focusRing: { color: "{blue.500}", shadow: "none" } } }, success: { background: "color-mix(in srgb, {green.500}, transparent 36%)", borderColor: "color-mix(in srgb, {green.700}, transparent 64%)", color: "{surface.0}", detailColor: "{green.100}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "rgba(255, 255, 255, 0.05)", focusRing: { color: "{green.500}", shadow: "none" } } }, warn: { background: "color-mix(in srgb, {yellow.500}, transparent 36%)", borderColor: "color-mix(in srgb, {yellow.700}, transparent 64%)", color: "{surface.0}", detailColor: "{yellow.50}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "rgba(255, 255, 255, 0.05)", focusRing: { color: "{yellow.500}", shadow: "none" } } }, error: { background: "color-mix(in srgb, {red.500}, transparent 36%)", borderColor: "color-mix(in srgb, {red.700}, transparent 64%)", color: "{surface.0}", detailColor: "{red.100}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "rgba(255, 255, 255, 0.05)", focusRing: { color: "{red.500}", shadow: "none" } } }, secondary: { background: "{surface.800}", borderColor: "{surface.700}", color: "{surface.300}", detailColor: "{surface.0}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "{surface.700}", focusRing: { color: "{surface.300}", shadow: "none" } } }, contrast: { background: "{surface.0}", borderColor: "{surface.100}", color: "{surface.950}", detailColor: "{surface.950}", shadow: "0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)", closeButton: { hoverBackground: "{surface.100}", focusRing: { color: "{surface.950}", shadow: "none" } } } } };
var l15 = { root: o78, icon: r77, content: e49, text: a41, summary: p4, detail: x, closeButton: n40, closeIcon: c23, colorScheme: s12, css: "" };

// node_modules/@primeuix/themes/material/togglebutton/index.mjs
var o79 = { padding: "0.75rem 1rem", borderRadius: "{form.field.border.radius}", gap: "0.5rem", fontWeight: "500", background: "{form.field.background}", borderColor: "{form.field.border.color}", color: "{form.field.color}", hoverColor: "{form.field.color}", checkedColor: "{form.field.color}", checkedBorderColor: "{form.field.border.color}", disabledBackground: "{form.field.disabled.background}", disabledBorderColor: "{form.field.disabled.background}", disabledColor: "{form.field.disabled.color}", invalidBorderColor: "{form.field.invalid.border.color}", focusRing: { width: "0", style: "none", offset: "0", color: "unset", shadow: "none" }, transitionDuration: "{form.field.transition.duration}", sm: { fontSize: "{form.field.sm.font.size}", padding: "0.625rem 0.75rem" }, lg: { fontSize: "{form.field.lg.font.size}", padding: "0.875rem 1.25rem" } };
var r78 = { color: "{text.muted.color}", hoverColor: "{text.muted.color}", checkedColor: "{text.muted.color}", disabledColor: "{form.field.disabled.color}" };
var e50 = { checkedBackground: "transparent", checkedShadow: "none", padding: "0", borderRadius: "0", sm: { padding: "0" }, lg: { padding: "0" } };
var d28 = { light: { root: { hoverBackground: "{surface.100}", checkedBackground: "{surface.200}" } }, dark: { root: { hoverBackground: "{surface.800}", checkedBackground: "{surface.700}" } } };
var css27 = ({ dt: o88 }) => `
.p-togglebutton:focus-visible {
    background: ${o88("togglebutton.hover.background")};
}
`;
var l16 = { root: o79, icon: r78, content: e50, colorScheme: d28, css: css27 };

// node_modules/@primeuix/themes/material/toggleswitch/index.mjs
var r79 = { width: "2.75rem", height: "1rem", borderRadius: "30px", gap: "0px", shadow: "none", focusRing: { width: "0", style: "none", color: "unset", offset: "0", shadow: "none" }, borderWidth: "1px", borderColor: "transparent", hoverBorderColor: "transparent", checkedBorderColor: "transparent", checkedHoverBorderColor: "transparent", invalidBorderColor: "{form.field.invalid.border.color}", transitionDuration: "{form.field.transition.duration}", slideDuration: "0.2s" };
var o80 = { borderRadius: "50%", size: "1.5rem" };
var e51 = { light: { root: { background: "{surface.300}", disabledBackground: "{surface.400}", hoverBackground: "{surface.300}", checkedBackground: "{primary.200}", checkedHoverBackground: "{primary.200}" }, handle: { background: "{surface.0}", disabledBackground: "{surface.200}", hoverBackground: "{surface.0}", checkedBackground: "{primary.color}", checkedHoverBackground: "{primary.color}", color: "{text.muted.color}", hoverColor: "{text.color}", checkedColor: "{primary.contrast.color}", checkedHoverColor: "{primary.contrast.color}" } }, dark: { root: { background: "{surface.700}", disabledBackground: "{surface.600}", hoverBackground: "{surface.700}", checkedBackground: "{primary.color}", checkedHoverBackground: "{primary.color}" }, handle: { background: "{surface.400}", disabledBackground: "{surface.500}", hoverBackground: "{surface.300}", checkedBackground: "{primary.200}", checkedHoverBackground: "{primary.200}", color: "{surface.800}", hoverColor: "{surface.900}", checkedColor: "{primary.contrast.color}", checkedHoverColor: "{primary.contrast.color}" } } };
var css28 = ({ dt: r86 }) => `
.p-toggleswitch-handle {
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover) .p-toggleswitch-handle {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${r86("text.color")}, transparent 96%), 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:focus-visible) .p-toggleswitch-handle {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${r86("text.color")}, transparent 88%), 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover).p-toggleswitch-checked .p-toggleswitch-handle {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${r86("toggleswitch.handle.checked.background")}, transparent 92%), 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:focus-visible).p-toggleswitch-checked .p-toggleswitch-handle {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${r86("toggleswitch.handle.checked.background")}, transparent 84%), 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}
`;
var a42 = { root: r79, handle: o80, colorScheme: e51, css: css28 };

// node_modules/@primeuix/themes/material/toolbar/index.mjs
var r80 = { color: "{content.color}", borderRadius: "{content.border.radius}", gap: "0.5rem", padding: "1rem" };
var o81 = { light: { root: { background: "{surface.100}", borderColor: "{surface.100}" } }, dark: { root: { background: "{surface.800}", borderColor: "{surface.800}" } } };
var e52 = { root: r80, colorScheme: o81, css: "" };

// node_modules/@primeuix/themes/material/tooltip/index.mjs
var r81 = { background: "{surface.600}", color: "{surface.0}", maxWidth: "12.5rem", gutter: "0.25rem", shadow: "{overlay.popover.shadow}", padding: "0.5rem 0.75rem", borderRadius: "{overlay.popover.border.radius}" };
var o82 = { root: r81, css: "" };

// node_modules/@primeuix/themes/material/tree/index.mjs
var o83 = { background: "{content.background}", color: "{content.color}", padding: "1rem", gap: "2px", indent: "2rem", transitionDuration: "{transition.duration}" };
var r82 = { padding: "0.5rem 0.75rem", borderRadius: "{border.radius.xs}", hoverBackground: "{content.hover.background}", selectedBackground: "{highlight.background}", color: "{text.color}", hoverColor: "{text.hover.color}", selectedColor: "{highlight.color}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "-1px", shadow: "{focus.ring.shadow}" }, gap: "0.5rem" };
var e53 = { color: "{text.muted.color}", hoverColor: "{text.hover.muted.color}", selectedColor: "{highlight.color}" };
var t39 = { borderRadius: "50%", size: "2rem", hoverBackground: "{content.hover.background}", selectedHoverBackground: "{content.background}", color: "{text.muted.color}", hoverColor: "{text.hover.muted.color}", selectedHoverColor: "{primary.color}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var n41 = { size: "2rem" };
var c24 = { margin: "0 0 0.75rem 0" };
var css29 = "\n.p-tree-node-content {\n    transition: none;\n}\n";
var d29 = { root: o83, node: r82, nodeIcon: e53, nodeToggleButton: t39, loadingIcon: n41, filter: c24, css: css29 };

// node_modules/@primeuix/themes/material/treeselect/index.mjs
var o84 = { background: "{form.field.background}", disabledBackground: "{form.field.disabled.background}", filledBackground: "{form.field.filled.background}", filledHoverBackground: "{form.field.filled.hover.background}", filledFocusBackground: "{form.field.filled.focus.background}", borderColor: "{form.field.border.color}", hoverBorderColor: "{form.field.hover.border.color}", focusBorderColor: "{form.field.focus.border.color}", invalidBorderColor: "{form.field.invalid.border.color}", color: "{form.field.color}", disabledColor: "{form.field.disabled.color}", placeholderColor: "{form.field.placeholder.color}", invalidPlaceholderColor: "{form.field.invalid.placeholder.color}", shadow: "{form.field.shadow}", paddingX: "{form.field.padding.x}", paddingY: "{form.field.padding.y}", borderRadius: "{form.field.border.radius}", focusRing: { width: "{form.field.focus.ring.width}", style: "{form.field.focus.ring.style}", color: "{form.field.focus.ring.color}", offset: "{form.field.focus.ring.offset}", shadow: "{form.field.focus.ring.shadow}" }, transitionDuration: "{form.field.transition.duration}", sm: { fontSize: "{form.field.sm.font.size}", paddingX: "{form.field.sm.padding.x}", paddingY: "{form.field.sm.padding.y}" }, lg: { fontSize: "{form.field.lg.font.size}", paddingX: "{form.field.lg.padding.x}", paddingY: "{form.field.lg.padding.y}" } };
var e54 = { width: "2.5rem", color: "{form.field.icon.color}" };
var r83 = { background: "{overlay.select.background}", borderColor: "{overlay.select.border.color}", borderRadius: "{overlay.select.border.radius}", color: "{overlay.select.color}", shadow: "{overlay.select.shadow}" };
var d30 = { padding: "{list.padding}" };
var l17 = { padding: "{list.option.padding}" };
var i22 = { borderRadius: "{border.radius.sm}" };
var n42 = { color: "{form.field.icon.color}" };
var css30 = ({ dt: o88 }) => `
.p-treeselect.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o88("treeselect.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("treeselect.focus.border.color")}, ${o88("treeselect.focus.border.color")}), linear-gradient(to bottom, ${o88("treeselect.border.color")}, ${o88("treeselect.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-treeselect.p-variant-filled:not(.p-disabled):hover {
    background: ${o88("treeselect.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("treeselect.focus.border.color")}, ${o88("treeselect.focus.border.color")}), linear-gradient(to bottom, ${o88("treeselect.hover.border.color")}, ${o88("treeselect.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-treeselect.p-variant-filled:not(.p-disabled).p-focus {
    outline: 0 none;
    background: ${o88("treeselect.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o88("treeselect.focus.border.color")}, ${o88("treeselect.focus.border.color")}), linear-gradient(to bottom, ${o88("treeselect.border.color")}, ${o88("treeselect.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-treeselect.p-variant-filled:not(.p-disabled).p-focus:hover {
    background-image: linear-gradient(to bottom, ${o88("treeselect.focus.border.color")}, ${o88("treeselect.focus.border.color")}), linear-gradient(to bottom, ${o88("treeselect.hover.border.color")}, ${o88("treeselect.hover.border.color")});
}

.p-treeselect.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o88("treeselect.invalid.border.color")}, ${o88("treeselect.invalid.border.color")}), linear-gradient(to bottom, ${o88("treeselect.invalid.border.color")}, ${o88("treeselect.invalid.border.color")});
}

.p-treeselect.p-variant-filled.p-invalid:not(.p-disabled).p-focus  {
    background-image: linear-gradient(to bottom, ${o88("treeselect.invalid.border.color")}, ${o88("treeselect.invalid.border.color")}), linear-gradient(to bottom, ${o88("treeselect.invalid.border.color")}, ${o88("treeselect.invalid.border.color")});
}
`;
var t40 = { root: o84, dropdown: e54, overlay: r83, tree: d30, emptyMessage: l17, chip: i22, clearIcon: n42, css: css30 };

// node_modules/@primeuix/themes/material/treetable/index.mjs
var o85 = { transitionDuration: "{transition.duration}" };
var r84 = { background: "{content.background}", borderColor: "{treetable.border.color}", color: "{content.color}", borderWidth: "0 0 1px 0", padding: "0.75rem 1rem" };
var e55 = { background: "{content.background}", hoverBackground: "{content.hover.background}", selectedBackground: "{highlight.background}", borderColor: "{treetable.border.color}", color: "{content.color}", hoverColor: "{content.hover.color}", selectedColor: "{highlight.color}", gap: "0.5rem", padding: "0.75rem 1rem", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "-1px", shadow: "{focus.ring.shadow}" } };
var t41 = { fontWeight: "600" };
var c25 = { background: "{content.background}", hoverBackground: "{content.hover.background}", selectedBackground: "{highlight.background}", color: "{content.color}", hoverColor: "{content.hover.color}", selectedColor: "{highlight.color}", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "-1px", shadow: "{focus.ring.shadow}" } };
var n43 = { borderColor: "{treetable.border.color}", padding: "0.75rem 1rem", gap: "0.5rem" };
var l18 = { background: "{content.background}", borderColor: "{treetable.border.color}", color: "{content.color}", padding: "0.75rem 1rem" };
var d31 = { fontWeight: "600" };
var i23 = { background: "{content.background}", borderColor: "{treetable.border.color}", color: "{content.color}", borderWidth: "0 0 1px 0", padding: "0.75rem 1rem" };
var a43 = { width: "0.5rem" };
var g5 = { width: "1px", color: "{primary.color}" };
var s13 = { color: "{text.muted.color}", hoverColor: "{text.hover.muted.color}", size: "0.875rem" };
var h4 = { size: "2rem" };
var u5 = { hoverBackground: "{content.hover.background}", selectedHoverBackground: "{content.background}", color: "{text.muted.color}", hoverColor: "{text.color}", selectedHoverColor: "{primary.color}", size: "1.75rem", borderRadius: "50%", focusRing: { width: "{focus.ring.width}", style: "{focus.ring.style}", color: "{focus.ring.color}", offset: "{focus.ring.offset}", shadow: "{focus.ring.shadow}" } };
var b3 = { borderColor: "{content.border.color}", borderWidth: "0 0 1px 0" };
var f5 = { borderColor: "{content.border.color}", borderWidth: "0 0 1px 0" };
var m3 = { light: { root: { borderColor: "{content.border.color}" }, bodyCell: { selectedBorderColor: "{primary.100}" } }, dark: { root: { borderColor: "{surface.800}" }, bodyCell: { selectedBorderColor: "{primary.900}" } } };
var p5 = { root: o85, header: r84, headerCell: e55, columnTitle: t41, row: c25, bodyCell: n43, footerCell: l18, columnFooter: d31, footer: i23, columnResizer: a43, resizeIndicator: g5, sortIcon: s13, loadingIcon: h4, nodeToggleButton: u5, paginatorTop: b3, paginatorBottom: f5, colorScheme: m3 };

// node_modules/@primeuix/themes/material/virtualscroller/index.mjs
var o86 = { mask: { background: "{content.background}", color: "{text.muted.color}" }, icon: { size: "2rem" } };
var e56 = { loader: o86, css: "" };

// node_modules/@primeuix/themes/material/index.mjs
var e57 = Object.defineProperty;
var m4 = Object.defineProperties;
var r85 = Object.getOwnPropertyDescriptors;
var i24 = Object.getOwnPropertySymbols;
var t42 = Object.prototype.hasOwnProperty;
var a44 = Object.prototype.propertyIsEnumerable;
var o87 = (m5, r86, i25) => r86 in m5 ? e57(m5, r86, { enumerable: true, configurable: true, writable: true, value: i25 }) : m5[r86] = i25;
var Ne;
var Qe = (Ne = ((e58, m5) => {
  for (var r86 in m5 || (m5 = {})) t42.call(m5, r86) && o87(e58, r86, m5[r86]);
  if (i24) for (var r86 of i24(m5)) a44.call(m5, r86) && o87(e58, r86, m5[r86]);
  return e58;
})({}, e5), m4(Ne, r85({ components: { accordion: e, autocomplete: i, avatar: n3, badge: d2, blockui: o6, breadcrumb: t4, button: e6, datepicker: v2, card: d3, carousel: t6, cascadeselect: a5, checkbox: c4, chip: a6, colorpicker: s2, confirmdialog: r15, confirmpopup: a8, contextmenu: c6, dataview: c8, datatable: k, dialog: e16, divider: t11, dock: s5, drawer: e18, editor: l7, fieldset: e20, fileupload: i8, iftalabel: i11, floatlabel: a15, galleria: l8, iconfield: c11, image: e24, imagecompare: r31, inlinemessage: a18, inplace: n15, inputchips: f4, inputgroup: o37, inputnumber: n16, inputotp: e27, inputtext: r38, knob: s7, listbox: n18, megamenu: g4, menu: t20, menubar: e30, message: u4, metergroup: s9, multiselect: a24, orderlist: o48, organizationchart: n24, overlaybadge: t25, popover: e39, paginator: n25, password: n28, panel: a26, panelmenu: e37, picklist: o55, progressbar: t28, progressspinner: r56, radiobutton: d20, rating: r58, ripple: o61, scrollpanel: a30, select: a31, selectbutton: d22, skeleton: o65, slider: d23, speeddial: a34, splitter: t31, splitbutton: d24, stepper: s11, steps: n34, tabmenu: n35, tabs: c19, tabview: e46, textarea: r74, tieredmenu: c22, tag: a38, terminal: e47, timeline: d27, togglebutton: l16, toggleswitch: a42, tree: d29, treeselect: t40, treetable: p5, toast: l15, toolbar: e52, tooltip: o82, virtualscroller: e56 } })));
export {
  Qe as default
};
//# sourceMappingURL=@primeuix_themes_material.js.map
