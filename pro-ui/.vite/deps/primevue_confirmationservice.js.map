{"version": 3, "sources": ["../../node_modules/src/confirmationeventbus/ConfirmationEventBus.js", "../../node_modules/src/useconfirm/UseConfirm.js", "../../node_modules/src/confirmationservice/ConfirmationService.js"], "sourcesContent": ["import { EventBus } from '@primeuix/utils/eventbus';\n\nexport default EventBus();\n", "import { inject } from 'vue';\n\nexport const PrimeVueConfirmSymbol = Symbol();\n\nexport function useConfirm() {\n    const PrimeVueConfirm = inject(PrimeVueConfirmSymbol);\n\n    if (!PrimeVueConfirm) {\n        throw new Error('No PrimeVue Confirmation provided!');\n    }\n\n    return PrimeVueConfirm;\n}\n", "import ConfirmationEventBus from 'primevue/confirmationeventbus';\nimport { PrimeVueConfirmSymbol } from 'primevue/useconfirm';\n\nexport default {\n    install: (app) => {\n        const ConfirmationService = {\n            require: (options) => {\n                ConfirmationEventBus.emit('confirm', options);\n            },\n            close: () => {\n                ConfirmationEventBus.emit('close');\n            }\n        };\n\n        app.config.globalProperties.$confirm = ConfirmationService;\n        app.provide(PrimeVueConfirmSymbol, ConfirmationService);\n    }\n};\n"], "mappings": ";;;;;;AAEA,IAAA,uBAAeA,SAAQ;;;ACAVC,IAAAA,wBAAwBC,OAAM;;;ACC3C,IAAA,sBAAe;EACXC,SAAS,SAATA,QAAUC,KAAQ;AACd,QAAMC,uBAAsB;MACxBC,SAAS,SAATA,SAAUC,SAAY;AAClBC,6BAAqBC,KAAK,WAAWF,OAAO;;MAEhDG,OAAO,SAAPA,QAAa;AACTF,6BAAqBC,KAAK,OAAO;MACrC;;AAGJL,QAAIO,OAAOC,iBAAiBC,WAAWR;AACvCD,QAAIU,QAAQC,uBAAuBV,oBAAmB;EAC1D;AACJ;", "names": ["EventBus", "PrimeVueConfirmSymbol", "Symbol", "install", "app", "ConfirmationService", "require", "options", "ConfirmationEventBus", "emit", "close", "config", "globalProperties", "$confirm", "provide", "PrimeVueConfirmSymbol"]}