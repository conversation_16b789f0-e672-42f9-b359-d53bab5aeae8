{"version": 3, "sources": ["../../node_modules/@primeuix/src/eventbus/index.ts"], "sourcesContent": ["export type Handler = (evt: unknown) => void;\n\nexport interface EventBusOptions {\n    on(type: string, handler: Handler): void;\n    off(type: string, handler: Handler): void;\n    emit(type: string, evt?: unknown): void;\n    clear(): void;\n}\n\nexport function EventBus(): EventBusOptions {\n    const allHandlers = new Map<string, Handler[]>();\n\n    return {\n        on(type: string, handler: Handler) {\n            let handlers = allHandlers.get(type);\n\n            if (!handlers) handlers = [handler];\n            else handlers.push(handler);\n\n            allHandlers.set(type, handlers);\n\n            return this;\n        },\n        off(type: string, handler: Handler) {\n            const handlers = allHandlers.get(type);\n\n            if (handlers) {\n                handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n            }\n\n            return this;\n        },\n        emit(type: string, evt?: unknown) {\n            const handlers = allHandlers.get(type);\n\n            if (handlers) {\n                handlers.forEach((handler) => {\n                    handler(evt);\n                });\n            }\n        },\n        clear() {\n            allHandlers.clear();\n        }\n    };\n}\n"], "mappings": ";AASO,SAAS,WAA4B;AACxC,QAAM,cAAc,oBAAI,IAAuB;AAE/C,SAAO;IACH,GAAG,MAAc,SAAkB;AAC/B,UAAI,WAAW,YAAY,IAAI,IAAI;AAEnC,UAAI,CAAC,SAAU,YAAW,CAAC,OAAO;UAC7B,UAAS,KAAK,OAAO;AAE1B,kBAAY,IAAI,MAAM,QAAQ;AAE9B,aAAO;IACX;IACA,IAAI,MAAc,SAAkB;AAChC,YAAM,WAAW,YAAY,IAAI,IAAI;AAErC,UAAI,UAAU;AACV,iBAAS,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,CAAC;MACtD;AAEA,aAAO;IACX;IACA,KAAK,MAAc,KAAe;AAC9B,YAAM,WAAW,YAAY,IAAI,IAAI;AAErC,UAAI,UAAU;AACV,iBAAS,QAAQ,CAAC,YAAY;AAC1B,kBAAQ,GAAG;QACf,CAAC;MACL;IACJ;IACA,QAAQ;AACJ,kBAAY,MAAM;IACtB;EACJ;AACJ;", "names": []}