import { useAuthStore } from '@/stores/auth';

export const requireAuth = async (to, from, next) => {
    const authStore = useAuthStore();
    
    // Если еще загружается, ждем
    if (authStore.isLoading) {
        // Ждем инициализации
        await new Promise(resolve => {
            const unwatch = authStore.$subscribe((mutation, state) => {
                if (!state.loading) {
                    unwatch();
                    resolve();
                }
            });
        });
    }
    
    // Если пользователь не авторизован, перенаправляем на логин
    if (!authStore.isAuthenticated) {
        console.log('User not authenticated, redirecting to login');
        await authStore.login();
        return;
    }
    
    // Если есть ошибка авторизации
    if (authStore.getError) {
        console.error('Authentication error:', authStore.getError);
        next('/auth-error');
        return;
    }
    
    // Пользователь авторизован, продолжаем
    next();
};

export const requireRole = (roles) => {
    return async (to, from, next) => {
        const authStore = useAuthStore();
        
        // Сначала проверяем авторизацию
        await requireAuth(to, from, (result) => {
            if (result === false) {
                return; // requireAuth уже обработал перенаправление
            }
        });
        
        // Если пользователь не авторизован, requireAuth уже перенаправил
        if (!authStore.isAuthenticated) {
            return;
        }
        
        // Проверяем роли
        if (!authStore.hasAnyRole(Array.isArray(roles) ? roles : [roles])) {
            console.log('User does not have required role(s):', roles);
            next('/forbidden');
            return;
        }
        
        next();
    };
}; 