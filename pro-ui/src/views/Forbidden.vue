<template>
    <div class="flex items-center justify-center min-h-screen bg-gray-100">
        <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                    <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <h3 class="mt-2 text-sm font-medium text-gray-900">
                    Доступ запрещен
                </h3>
                <div class="mt-2 text-sm text-gray-500">
                    <p>
                        У вас нет прав для доступа к этой странице. Обратитесь к администратору для получения необходимых прав.
                    </p>
                </div>
                <div class="mt-6">
                    <button
                        @click="goHome"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        Вернуться на главную
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const goHome = () => {
    window.location.href = '/';
};
</script> 