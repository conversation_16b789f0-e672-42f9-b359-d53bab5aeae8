<script setup>
import { ref, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { FilterMatchMode } from '@primevue/core/api';
import { TerminalProfileService } from '@/service/TerminalProfileService';

const toast = useToast();
const confirm = useConfirm();

const loading = ref(false);
const configurations = ref([]);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    description: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
});

onMounted(() => {
    loadConfigurations();
});

const loadConfigurations = async () => {
    try {
        loading.value = true;
        const data = await TerminalProfileService.getTerminalProfiles();
        configurations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки конфигураций:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить список конфигураций',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 1: return 'success';
        case 0: return 'warning';
        case 2: return 'danger';
        default: return 'info';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 1: return 'Активная';
        case 0: return 'Черновик';
        case 2: return 'Удалена';
        default: return 'Неизвестно';
    }
};

const openForm = () => {
    // TODO: Реализовать открытие формы создания конфигурации
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Форма создания конфигурации будет реализована позже',
        life: 3000
    });
};

const editConfiguration = (config) => {
    // TODO: Реализовать редактирование конфигурации
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Форма редактирования конфигурации будет реализована позже',
        life: 3000
    });
};

const viewConfiguration = (config) => {
    // TODO: Реализовать просмотр конфигурации
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Просмотр конфигурации будет реализован позже',
        life: 3000
    });
};

const deployConfiguration = (config) => {
    // TODO: Реализовать развертывание конфигурации
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Развертывание конфигурации будет реализовано позже',
        life: 3000
    });
};
</script>

<template>
    <div class="configuration-list">
        <Toast />
        <ConfirmDialog />
        
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Конфигурации терминалов</h1>
            <Button 
                label="Создать конфигурацию" 
                icon="pi pi-plus" 
                @click="openForm()"
                class="p-button-success"
            />
        </div>

        <div class="card">
            <DataTable 
                :value="configurations" 
                :loading="loading"
                :paginator="true" 
                :rows="20"
                :rowsPerPageOptions="[10, 20, 50]"
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                currentPageReportTemplate="Показано {first} - {last} из {totalRecords} конфигураций"
                :globalFilterFields="['name', 'description']"
                v-model:filters="filters"
                filterDisplay="menu"
                :showGridlines="true"
                stripedRows
                responsiveLayout="scroll"
            >
                <template #header>
                    <div class="flex justify-content-between">
                        <span class="p-input-icon-left">
                            <i class="pi pi-search" />
                            <InputText 
                                v-model="filters['global'].value" 
                                placeholder="Поиск по названию или описанию..." 
                                class="p-inputtext-sm"
                            />
                        </span>
                    </div>
                </template>
                <Column field="name" header="Название" sortable>
                    <template #body="{ data }">
                        <div class="font-medium">{{ data.name }}</div>
                    </template>
                </Column>

                <Column field="description" header="Описание" sortable>
                    <template #body="{ data }">
                        <div class="text-color-secondary">{{ data.description || 'Описание отсутствует' }}</div>
                    </template>
                </Column>

                <Column field="status" header="Статус" sortable>
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)"
                        />
                    </template>
                </Column>

                <Column field="version" header="Версия" sortable>
                    <template #body="{ data }">
                        <div class="text-center">{{ data.version }}</div>
                    </template>
                </Column>

                <Column field="activeFrom" header="Активен с" sortable>
                    <template #body="{ data }">
                        <div v-if="data.activeFrom">
                            {{ formatDate(data.activeFrom) }}
                        </div>
                        <div v-else class="text-color-secondary">Не указано</div>
                    </template>
                </Column>

                <Column field="activeTill" header="Активен до" sortable>
                    <template #body="{ data }">
                        <div v-if="data.activeTill">
                            {{ formatDate(data.activeTill) }}
                        </div>
                        <div v-else class="text-color-secondary">Бессрочно</div>
                    </template>
                </Column>

                <Column header="Действия" :exportable="false" style="min-width:8rem">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button 
                                icon="pi pi-eye" 
                                class="p-button-rounded p-button-info p-button-text" 
                                @click="viewConfiguration(data)"
                                v-tooltip.top="'Просмотр'"
                            />
                            <Button 
                                icon="pi pi-pencil" 
                                class="p-button-rounded p-button-success p-button-text" 
                                @click="editConfiguration(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button 
                                icon="pi pi-send" 
                                class="p-button-rounded p-button-warning p-button-text" 
                                @click="deployConfiguration(data)"
                                v-tooltip.top="'Развернуть'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.configuration-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
