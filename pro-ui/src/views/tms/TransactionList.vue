<script setup>
import { ref, onMounted } from 'vue';
import { ProcessingService } from '@/service/ProcessingService';

const transactions = ref([]);
const loading = ref(true);

onMounted(async () => {
    try {
        // TMS получает все транзакции с терминалов (первичный источник)
        const data = await ProcessingService.getTMSTransactions();
        transactions.value = data;
    } catch (error) {
        console.error('Ошибка загрузки транзакций TMS:', error);
    } finally {
        loading.value = false;
    }
});

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount);
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'completed': return 'success';
        case 'processing': return 'warning';
        case 'error': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'completed': return 'Завершена';
        case 'processing': return 'Обработка';
        case 'error': return 'Ошибка';
        default: return status;
    }
};

const getPaymentTypeLabel = (type) => {
    switch (type) {
        case 'cash': return 'Наличные';
        case 'emv': return 'Банковская карта';
        default: return type;
    }
};

const getPaymentTypeColor = (type) => {
    switch (type) {
        case 'cash': return 'success';
        case 'emv': return 'info';
        case 'troika': return 'warning';
        default: return 'secondary';
    }
};

const getBatteryIcon = (level) => {
    if (level > 75) return 'pi pi-battery-full';
    if (level > 50) return 'pi pi-battery-half';
    if (level > 25) return 'pi pi-battery-low';
    return 'pi pi-battery-empty';
};

const getBatteryColor = (level) => {
    if (level > 50) return '#22c55e';
    if (level > 25) return '#f59e0b';
    return '#ef4444';
};
</script>

<template>
    <div class="transaction-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Транзакции терминалов</h1>
            <div class="flex gap-2">
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                />
                <Button
                    label="Обновить"
                    icon="pi pi-refresh"
                />
            </div>
        </div>

        <div class="card">
            <DataTable
                :value="transactions"
                :paginator="true"
                :rows="15"
                responsiveLayout="scroll"
                :loading="loading"
                showGridlines
            >
                <Column field="transactionId" header="ID транзакции" :sortable="true">
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.transactionId }}</span>
                    </template>
                </Column>

                <Column field="terminalId" header="Терминал" :sortable="true">
                    <template #body="{ data }">
                        <span class="font-mono">{{ data.terminalId }}</span>
                    </template>
                </Column>

                <Column field="amount" header="Сумма" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatAmount(data.amount) }}
                        </div>
                    </template>
                </Column>

                <Column field="paymentType" header="Способ оплаты" :sortable="true">
                    <template #body="{ data }">
                        <Tag
                            :value="getPaymentTypeLabel(data.paymentType)"
                            :severity="getPaymentTypeColor(data.paymentType)"
                        />
                    </template>
                </Column>

                <Column field="terminalStatus" header="Статус терминала" :sortable="true">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i
                                :class="data.terminalStatus === 'online' ? 'pi pi-circle-fill text-green-500' : 'pi pi-circle-fill text-red-500'"
                                class="mr-2"
                            ></i>
                            <span>{{ data.terminalStatus === 'online' ? 'Онлайн' : 'Офлайн' }}</span>
                        </div>
                    </template>
                </Column>

                <Column field="signalStrength" header="Сигнал" :sortable="true">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-wifi mr-2"></i>
                            <span>{{ data.signalStrength }}/5</span>
                        </div>
                    </template>
                </Column>

                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag
                            :value="getStatusLabel(data.status)"
                            :severity="getStatusSeverity(data.status)"
                        />
                    </template>
                </Column>

                <Column field="operationDate" header="Время операции" :sortable="true">
                    <template #body="{ data }">
                        {{ formatDate(data.operationDate) }}
                    </template>
                </Column>

                <Column header="Регистрация" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div><strong>Терминал:</strong></div>
                            <div class="text-xs text-color-secondary">{{ formatDate(data.terminalRegTime) }}</div>
                            <div class="mt-1"><strong>Сервер:</strong></div>
                            <div class="text-xs text-color-secondary">{{ formatDate(data.serverRegTime) }}</div>
                        </div>
                    </template>
                </Column>

                <Column field="firmwareVersion" header="Прошивка" :sortable="true" style="min-width: 100px">
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.firmwareVersion }}</span>
                    </template>
                </Column>

                <Column field="batteryLevel" header="Батарея" :sortable="true" style="min-width: 100px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i
                                :class="getBatteryIcon(data.batteryLevel)"
                                :style="{ color: getBatteryColor(data.batteryLevel) }"
                                class="mr-2"
                            ></i>
                            <span>{{ data.batteryLevel }}%</span>
                        </div>
                    </template>
                </Column>

                <Column header="Действия">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button
                                icon="pi pi-eye"
                                size="small"
                                text
                                v-tooltip.top="'Детали'"
                            />
                            <Button
                                v-if="data.status === 'error'"
                                icon="pi pi-refresh"
                                size="small"
                                text
                                v-tooltip.top="'Повторить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.transaction-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
