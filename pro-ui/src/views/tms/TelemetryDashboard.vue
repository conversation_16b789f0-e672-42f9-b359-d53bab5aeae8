<script setup>
import { ref, onMounted } from 'vue';

const loading = ref(true);
const telemetryData = ref({
    totalTerminals: 156,
    onlineTerminals: 142,
    offlineTerminals: 14,
    warningTerminals: 8,
    averageBattery: 78,
    totalTransactions: 12456,
    todayTransactions: 1234
});

const terminalsByStatus = ref([
    { status: 'Онлайн', count: 142, color: '#10b981' },
    { status: 'Офлайн', count: 14, color: '#ef4444' },
    { status: 'Предупреждение', count: 8, color: '#f59e0b' }
]);

const batteryLevels = ref([
    { range: '90-100%', count: 45, color: '#10b981' },
    { range: '70-89%', count: 67, count: '#3b82f6' },
    { range: '50-69%', count: 32, color: '#f59e0b' },
    { range: '0-49%', count: 12, color: '#ef4444' }
]);

const recentAlerts = ref([
    {
        id: 1,
        terminalId: 'TRM-MSK-005',
        type: 'low_battery',
        message: 'Низкий заряд батареи (15%)',
        timestamp: '2024-01-20T18:30:00Z',
        severity: 'warning'
    },
    {
        id: 2,
        terminalId: 'TRM-MSK-012',
        type: 'offline',
        message: 'Терминал не отвечает более 30 минут',
        timestamp: '2024-01-20T18:15:00Z',
        severity: 'danger'
    },
    {
        id: 3,
        terminalId: 'TRM-MSK-023',
        type: 'high_temperature',
        message: 'Высокая температура (65°C)',
        timestamp: '2024-01-20T17:45:00Z',
        severity: 'warning'
    }
]);

onMounted(() => {
    setTimeout(() => {
        loading.value = false;
    }, 1000);
});

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const getSeverityIcon = (severity) => {
    switch (severity) {
        case 'danger': return 'pi pi-exclamation-triangle text-red-500';
        case 'warning': return 'pi pi-exclamation-circle text-orange-500';
        case 'info': return 'pi pi-info-circle text-blue-500';
        default: return 'pi pi-info-circle text-gray-500';
    }
};
</script>

<template>
    <div class="telemetry-dashboard">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Телеметрия терминалов</h1>
            <Button 
                label="Обновить данные" 
                icon="pi pi-refresh" 
                :loading="loading"
            />
        </div>

        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных телеметрии...</p>
            </div>
        </div>

        <div v-else>
            <!-- Общая статистика -->
            <div class="grid mb-4">
                <div class="col-12 md:col-3">
                    <div class="card bg-blue-50 border-blue-200">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-blue-500 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-tablet text-white text-xl"></i>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-blue-900">{{ telemetryData.totalTerminals }}</div>
                                <div class="text-blue-700">Всего терминалов</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 md:col-3">
                    <div class="card bg-green-50 border-green-200">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-green-500 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-check-circle text-white text-xl"></i>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-green-900">{{ telemetryData.onlineTerminals }}</div>
                                <div class="text-green-700">Онлайн</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 md:col-3">
                    <div class="card bg-orange-50 border-orange-200">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-orange-500 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-battery-half text-white text-xl"></i>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-orange-900">{{ telemetryData.averageBattery }}%</div>
                                <div class="text-orange-700">Средний заряд</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 md:col-3">
                    <div class="card bg-purple-50 border-purple-200">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-purple-500 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-chart-line text-white text-xl"></i>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-purple-900">{{ telemetryData.todayTransactions }}</div>
                                <div class="text-purple-700">Транзакций сегодня</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid">
                <!-- Статус терминалов -->
                <div class="col-12 lg:col-6">
                    <div class="card">
                        <h3 class="text-lg font-semibold mb-4">Статус терминалов</h3>
                        <div class="space-y-3">
                            <div v-for="item in terminalsByStatus" :key="item.status" class="flex align-items-center justify-content-between p-3 border-round border-1 border-200">
                                <div class="flex align-items-center">
                                    <div class="w-1rem h-1rem border-round mr-3" :style="{ backgroundColor: item.color }"></div>
                                    <span class="font-medium">{{ item.status }}</span>
                                </div>
                                <span class="font-bold text-lg">{{ item.count }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Последние уведомления -->
                <div class="col-12 lg:col-6">
                    <div class="card">
                        <h3 class="text-lg font-semibold mb-4">Последние уведомления</h3>
                        <div class="space-y-3">
                            <div v-for="alert in recentAlerts" :key="alert.id" class="flex align-items-start p-3 border-round border-1 border-200">
                                <i :class="getSeverityIcon(alert.severity)" class="mr-3 mt-1"></i>
                                <div class="flex-1">
                                    <div class="font-medium mb-1">{{ alert.terminalId }}</div>
                                    <div class="text-sm text-color-secondary mb-2">{{ alert.message }}</div>
                                    <div class="text-xs text-color-secondary">{{ formatDate(alert.timestamp) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.telemetry-dashboard {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.bg-blue-50 { background-color: #eff6ff; }
.bg-green-50 { background-color: #f0fdf4; }
.bg-orange-50 { background-color: #fff7ed; }
.bg-purple-50 { background-color: #faf5ff; }

.border-blue-200 { border-color: #bfdbfe; }
.border-green-200 { border-color: #bbf7d0; }
.border-orange-200 { border-color: #fed7aa; }
.border-purple-200 { border-color: #e9d5ff; }

.text-blue-900 { color: #1e3a8a; }
.text-green-900 { color: #14532d; }
.text-orange-900 { color: #9a3412; }
.text-purple-900 { color: #581c87; }

.text-blue-700 { color: #1d4ed8; }
.text-green-700 { color: #15803d; }
.text-orange-700 { color: #c2410c; }
.text-purple-700 { color: #7c3aed; }
</style>
