<template>
    <div class="card">
        <Toast />
        <ConfirmDialog />
        
        <div class="flex justify-content-between align-items-center mb-4">
            <h2>Конфигурации терминалов</h2>
            <Button 
                label="Добавить конфигурацию" 
                icon="pi pi-plus" 
                @click="openForm()"
                class="p-button-success"
            />
        </div>

        <DataTable 
            :value="terminalProfiles" 
            :loading="loading"
            :paginator="true" 
            :rows="20"
            :rowsPerPageOptions="[10, 20, 50]"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Показано {first} - {last} из {totalRecords} конфигураций"
            :globalFilterFields="['name', 'description']"
            v-model:filters="filters"
            filterDisplay="menu"
            :showGridlines="true"
            stripedRows
            responsiveLayout="scroll"
        >
            <template #header>
                <div class="flex justify-content-between">
                    <span class="p-input-icon-left">
                        <i class="pi pi-search" />
                        <InputText 
                            v-model="filters['global'].value" 
                            placeholder="Поиск по названию или описанию..." 
                            class="p-inputtext-sm"
                        />
                    </span>
                </div>
            </template>

            <Column field="name" header="Название" sortable>
                <template #body="{ data }">
                    <div class="font-medium">{{ data.name }}</div>
                </template>
            </Column>

            <Column field="description" header="Описание" sortable>
                <template #body="{ data }">
                    <div class="text-color-secondary">{{ data.description || 'Описание отсутствует' }}</div>
                </template>
            </Column>

            <Column field="status" header="Статус" sortable>
                <template #body="{ data }">
                    <Tag 
                        :value="getStatusLabel(data.status)" 
                        :severity="getStatusSeverity(data.status)"
                    />
                </template>
            </Column>

            <Column field="version" header="Версия" sortable>
                <template #body="{ data }">
                    <div class="text-center">{{ data.version }}</div>
                </template>
            </Column>

            <Column field="activeFrom" header="Активен с" sortable>
                <template #body="{ data }">
                    <div v-if="data.activeFrom">
                        {{ formatDate(data.activeFrom) }}
                    </div>
                    <div v-else class="text-color-secondary">Не указано</div>
                </template>
            </Column>

            <Column field="activeTill" header="Активен до" sortable>
                <template #body="{ data }">
                    <div v-if="data.activeTill">
                        {{ formatDate(data.activeTill) }}
                    </div>
                    <div v-else class="text-color-secondary">Бессрочно</div>
                </template>
            </Column>

            <Column header="Действия" :exportable="false" style="min-width:8rem">
                <template #body="{ data }">
                    <div class="flex gap-2">
                        <Button 
                            icon="pi pi-pencil" 
                            class="p-button-rounded p-button-success p-button-text" 
                            @click="editProfile(data)"
                            v-tooltip.top="'Редактировать'"
                        />
                        <Button 
                            icon="pi pi-trash" 
                            class="p-button-rounded p-button-danger p-button-text" 
                            @click="confirmDelete(data)"
                            v-tooltip.top="'Удалить'"
                        />
                    </div>
                </template>
            </Column>
        </DataTable>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { FilterMatchMode } from '@primevue/core/api';
import { TerminalProfileService } from '@/service/TerminalProfileService';

const toast = useToast();
const confirm = useConfirm();

const loading = ref(false);
const terminalProfiles = ref([]);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    description: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
});

onMounted(() => {
    loadTerminalProfiles();
});

const loadTerminalProfiles = async () => {
    try {
        loading.value = true;
        const data = await TerminalProfileService.getTerminalProfiles();
        terminalProfiles.value = data;
    } catch (error) {
        console.error('Ошибка загрузки конфигураций:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить список конфигураций',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 1: return 'Активная';
        case 0: return 'Черновик';
        case 2: return 'Удалена';
        default: return 'Неизвестно';
    }
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 1: return 'success';
        case 0: return 'warning';
        case 2: return 'danger';
        default: return 'info';
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const openForm = () => {
    // TODO: Реализовать открытие формы создания конфигурации
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Форма создания конфигурации будет реализована позже',
        life: 3000
    });
};

const editProfile = (profile) => {
    // TODO: Реализовать редактирование конфигурации
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Форма редактирования конфигурации будет реализована позже',
        life: 3000
    });
};

const confirmDelete = (profile) => {
    confirm.require({
        message: `Вы уверены, что хотите удалить конфигурацию "${profile.name}"?`,
        header: 'Подтверждение удаления',
        icon: 'pi pi-exclamation-triangle',
        accept: () => deleteProfile(profile),
        reject: () => {
            toast.add({
                severity: 'info',
                summary: 'Отменено',
                detail: 'Удаление отменено',
                life: 3000
            });
        }
    });
};

const deleteProfile = async (profile) => {
    try {
        // TODO: Реализовать удаление конфигурации через API
        toast.add({
            severity: 'info',
            summary: 'Информация',
            detail: 'Удаление конфигурации будет реализовано позже',
            life: 3000
        });
    } catch (error) {
        console.error('Ошибка удаления конфигурации:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось удалить конфигурацию',
            life: 3000
        });
    }
};
</script>

<style scoped>
.card {
    padding: 1.5rem;
}
</style> 