<template>
    <div class="tariff-detail">
        <div class="flex justify-content-between align-items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold m-0 mb-2">Детали тарифа</h1>
                <p class="text-xl text-color-secondary m-0">
                    Проект: "{{ project?.name || 'Загрузка...' }}"
                </p>
            </div>
            <div class="flex gap-3">
                <Button
                    label="Назад к списку"
                    icon="pi pi-arrow-left"
                    class="p-button-outlined"
                    @click="goBack"
                />
                <Button
                    label="Редактировать"
                    icon="pi pi-pencil"
                    @click="editTariff"
                />
            </div>
        </div>

        <!-- Карточка тарифа -->
        <div v-if="tariff" class="mb-6">
            <TariffCard 
                :tariff="tariff"
                @edit="editTariff"
                @activate="activateTariff"
                @deactivate="deactivateTariff"
                @delete="confirmDelete"
                @copy="copyTariff"
                @history="viewHistory"
                @export="exportTariff"
            />
        </div>

        <!-- Загрузка -->
        <div v-else-if="loading" class="card">
            <div class="text-center p-6">
                <ProgressSpinner />
                <p class="mt-3">Загрузка данных тарифа...</p>
            </div>
        </div>

        <!-- Ошибка -->
        <div v-else-if="error" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-3"></i>
                <h3 class="text-lg font-semibold mb-3 text-red-600">Ошибка загрузки тарифа</h3>
                <p class="text-color-secondary mb-4">{{ error }}</p>
                <Button
                    label="Повторить"
                    icon="pi pi-refresh"
                    @click="loadTariff"
                />
            </div>
        </div>

        <!-- Диалог редактирования -->
        <Dialog 
            v-model:visible="editDialogVisible" 
            header="Редактировать тариф"
            modal 
            class="p-fluid"
            :style="{ width: '50rem' }"
        >
            <div class="grid grid-cols-2 gap-4">
                <div class="field">
                    <label for="name">Название *</label>
                    <InputText 
                        id="name" 
                        v-model="editForm.name" 
                        required 
                        autofocus 
                        :class="{ 'p-invalid': submitted && !editForm.name }"
                    />
                    <small class="p-error" v-if="submitted && !editForm.name">
                        Название обязательно для заполнения.
                    </small>
                </div>

                <div class="field">
                    <label for="status">Статус</label>
                    <Dropdown 
                        id="status" 
                        v-model="editForm.status" 
                        :options="statusOptions" 
                        optionLabel="label" 
                        optionValue="value" 
                        placeholder="Выберите статус"
                    />
                </div>

                <div class="field col-span-2">
                    <label for="tags">Теги</label>
                    <InputText 
                        id="tags" 
                        v-model="editForm.tags" 
                        placeholder="Введите теги через запятую"
                    />
                    <small class="text-gray-500">Например: basic, default, premium</small>
                </div>
            </div>

            <template #footer>
                <Button 
                    label="Отмена" 
                    icon="pi pi-times" 
                    @click="closeEditDialog" 
                    class="p-button-text"
                />
                <Button 
                    label="Сохранить" 
                    icon="pi pi-check" 
                    @click="saveEdit" 
                    :loading="saving"
                />
            </template>
        </Dialog>

        <!-- Диалог подтверждения удаления -->
        <ConfirmDialog />
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { TariffService } from '@/service/TariffService';
import { ProjectService } from '@/service/ProjectService';
import TariffCard from '@/components/TariffCard.vue';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';
import ConfirmDialog from 'primevue/confirmdialog';
import ProgressSpinner from 'primevue/progressspinner';

const route = useRoute();
const router = useRouter();
const toast = useToast();
const confirm = useConfirm();

// Реактивные данные
const projectId = ref(route.params.projectId);
const tariffId = ref(route.params.tariffId);
const project = ref(null);
const tariff = ref(null);
const loading = ref(true);
const error = ref(null);
const editDialogVisible = ref(false);
const submitted = ref(false);
const saving = ref(false);

// Форма редактирования
const editForm = reactive({
    name: '',
    status: 'ACTIVE',
    tags: ''
});

// Опции статусов
const statusOptions = [
    { label: 'Активный', value: 'ACTIVE' },
    { label: 'Отключен', value: 'DISABLED' },
    { label: 'Заблокирован', value: 'BLOCKED' }
];

// Методы
const loadProject = async () => {
    try {
        const projectData = await ProjectService.getProjectById(projectId.value);
        project.value = projectData;
    } catch (error) {
        console.error('Ошибка загрузки проекта:', error);
    }
};

const loadTariff = async () => {
    try {
        loading.value = true;
        error.value = null;
        const tariffData = await TariffService.getTariffById(tariffId.value);
        tariff.value = tariffData;
    } catch (err) {
        console.error('Ошибка загрузки тарифа:', err);
        error.value = err.message || 'Ошибка загрузки тарифа';
    } finally {
        loading.value = false;
    }
};

const goBack = () => {
    router.push(`/pro/${projectId.value}/nsi/tariff`);
};

const editTariff = () => {
    editForm.name = tariff.value.name || '';
    editForm.status = tariff.value.status || 'ACTIVE';
    editForm.tags = tariff.value.tags || '';
    editDialogVisible.value = true;
};

const closeEditDialog = () => {
    editDialogVisible.value = false;
    resetEditForm();
};

const resetEditForm = () => {
    editForm.name = '';
    editForm.status = 'ACTIVE';
    editForm.tags = '';
    submitted.value = false;
};

const saveEdit = async () => {
    submitted.value = true;
    
    if (!editForm.name) {
        return;
    }

    try {
        saving.value = true;
        
        await TariffService.updateTariff(tariffId.value, {
            name: editForm.name,
            status: editForm.status,
            tags: editForm.tags
        });
        
        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Тариф обновлен',
            life: 3000
        });
        
        closeEditDialog();
        loadTariff(); // Перезагружаем данные
    } catch (error) {
        console.error('Ошибка обновления тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось обновить тариф',
            life: 3000
        });
    } finally {
        saving.value = false;
    }
};

const activateTariff = async (id) => {
    try {
        await TariffService.activateTariff(id);
        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Тариф активирован',
            life: 3000
        });
        loadTariff();
    } catch (error) {
        console.error('Ошибка активации тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось активировать тариф',
            life: 3000
        });
    }
};

const deactivateTariff = async (id) => {
    try {
        await TariffService.deactivateTariff(id);
        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Тариф деактивирован',
            life: 3000
        });
        loadTariff();
    } catch (error) {
        console.error('Ошибка деактивации тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось деактивировать тариф',
            life: 3000
        });
    }
};

const confirmDelete = (tariff) => {
    confirm.require({
        message: `Вы уверены, что хотите удалить тариф "${tariff.name}"?`,
        header: 'Подтверждение удаления',
        icon: 'pi pi-exclamation-triangle',
        accept: () => deleteTariff(tariff.id)
    });
};

const deleteTariff = async (id) => {
    try {
        await TariffService.deleteTariff(id);
        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Тариф удален',
            life: 3000
        });
        goBack();
    } catch (error) {
        console.error('Ошибка удаления тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось удалить тариф',
            life: 3000
        });
    }
};

const copyTariff = (tariff) => {
    // TODO: Реализовать копирование тарифа
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Функция копирования тарифа будет реализована позже',
        life: 3000
    });
};

const viewHistory = (id) => {
    // TODO: Реализовать просмотр истории версий
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Функция просмотра истории версий будет реализована позже',
        life: 3000
    });
};

const exportTariff = (tariff) => {
    // TODO: Реализовать экспорт тарифа
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Функция экспорта тарифа будет реализована позже',
        life: 3000
    });
};

// Загрузка данных при монтировании
onMounted(() => {
    loadProject();
    loadTariff();
});
</script>

<style scoped>
.tariff-detail {
    padding: 1rem;
}

.grid {
    display: grid;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-4 {
    gap: 1rem;
}

.col-span-2 {
    grid-column: span 2 / span 2;
}

.field label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.text-gray-500 {
    color: #6b7280;
}

.p-error {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
</style> 