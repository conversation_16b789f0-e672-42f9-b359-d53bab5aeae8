<script setup>
import { useRoute } from 'vue-router';
import { ref } from 'vue';

const route = useRoute();
const projectCode = route.params.code;

const documents = ref([
    {
        id: 1,
        name: 'Техническое задание.pdf',
        type: 'PDF',
        size: '2.5 MB',
        date: '15.01.2024',
        author: 'Петров П.П.'
    },
    {
        id: 2,
        name: 'Договор.docx',
        type: 'DOCX',
        size: '1.2 MB',
        date: '10.01.2024',
        author: 'Сидоров С.С.'
    },
    {
        id: 3,
        name: 'Смета.xlsx',
        type: 'XLSX',
        size: '856 KB',
        date: '12.01.2024',
        author: 'Иванов И.И.'
    }
]);

const getFileIcon = (type) => {
    switch (type) {
        case 'PDF': return 'pi pi-file-pdf';
        case 'DOCX': return 'pi pi-file-word';
        case 'XLSX': return 'pi pi-file-excel';
        default: return 'pi pi-file';
    }
};
</script>

<template>
    <div class="project-documents">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold">Документы проекта</h2>
            <Button label="Добавить документ" icon="pi pi-plus" />
        </div>
        
        <DataTable :value="documents" responsiveLayout="scroll">
            <Column field="name" header="Название">
                <template #body="{ data }">
                    <div class="flex align-items-center">
                        <i :class="getFileIcon(data.type)" class="mr-2 text-lg"></i>
                        <span>{{ data.name }}</span>
                    </div>
                </template>
            </Column>
            <Column field="type" header="Тип" />
            <Column field="size" header="Размер" />
            <Column field="date" header="Дата" />
            <Column field="author" header="Автор" />
            <Column header="Действия">
                <template #body>
                    <div class="flex gap-2">
                        <Button icon="pi pi-download" size="small" text />
                        <Button icon="pi pi-eye" size="small" text />
                        <Button icon="pi pi-trash" size="small" text severity="danger" />
                    </div>
                </template>
            </Column>
        </DataTable>
    </div>
</template>
