<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { StationService } from '@/service/StationService';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;

const stations = ref([]);
const loading = ref(true);
const error = ref(null);
const pagination = ref({
    page: 0,
    size: 10,
    totalElements: 0,
    totalPages: 0
});

// Фильтры для API
const apiFilters = ref({
    name: null,
    city: null,
    region: null,
    country: null
});

// Фильтры для DataTable (локальные)
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    city: { value: null, matchMode: FilterMatchMode.CONTAINS },
    region: { value: null, matchMode: FilterMatchMode.CONTAINS },
    country: { value: null, matchMode: FilterMatchMode.CONTAINS }
});



const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        city: { value: null, matchMode: FilterMatchMode.CONTAINS },
        region: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};

onMounted(() => {
    loadStations();
});

const loadStations = async (page = 0) => {
    try {
        loading.value = true;
        error.value = null;
        const response = await StationService.getStationsByProject(
            projectId, 
            page, 
            pagination.value.size, 
            apiFilters.value
        );
        console.log('Загруженные станции:', response);
        
        if (response.content) {
            stations.value = response.content;
            pagination.value = {
                page: response.pagination.page || page,
                size: response.pagination.size || pagination.value.size,
                totalElements: response.pagination.totalElements || 0,
                totalPages: response.pagination.totalPages || 0
            };
        } else {
            stations.value = [];
        }
    } catch (err) {
        console.error('Ошибка загрузки остановок:', err);
        error.value = err.message || 'Ошибка загрузки остановок';
        stations.value = [];
    } finally {
        loading.value = false;
    }
};

const onPageChange = (event) => {
    loadStations(event.page);
};

const applyFilters = () => {
    // Обновляем API фильтры из локальных фильтров
    apiFilters.value = {
        name: filters.value.name?.value || null,
        city: filters.value.city?.value || null,
        region: filters.value.region?.value || null,
        country: filters.value.country?.value || null
    };
    loadStations(0); // Сбрасываем на первую страницу
};

const addStation = () => {
    router.push(`/pro/${projectId}/nsi/station/create`);
};

const editStation = (station) => {
    router.push(`/pro/${projectId}/nsi/station/${station.id}/edit`);
};

const deleteStation = async (station) => {
    if (confirm(`Вы уверены, что хотите удалить остановку "${station.name}"?`)) {
        try {
            await StationService.deleteStation(station.id);
            await loadStations(pagination.value.page);
        } catch (error) {
            console.error('Ошибка удаления остановки:', error);
        }
    }
};

const importFromFile = () => {
    // TODO: Реализовать импорт из файла
    console.log('Импорт из файла');
};

const exportData = () => {
    // TODO: Реализовать экспорт
    console.log('Экспорт данных');
};



const clearFilter = () => {
    initFilters();
};

const refreshStations = () => {
    loadStations();
};

const getCoordinatesIcon = (hasCoordinates) => {
    return hasCoordinates ? 'pi pi-check-circle' : 'pi pi-times-circle';
};

const getCoordinatesSeverity = (hasCoordinates) => {
    return hasCoordinates ? 'success' : 'danger';
};
</script>

<template>
    <div class="station-list">
                <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Остановки</h2>
            <div class="flex gap-2">
                <Button
                    label="Добавить остановку"
                    icon="pi pi-plus"
                    @click="addStation"
                />
                <Button
                    label="Применить фильтры"
                    icon="pi pi-filter"
                    @click="applyFilters"
                />
                <Button
                    label="Загрузить из файла"
                    icon="pi pi-upload"
                    outlined
                    @click="importFromFile"
                />
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <!-- Ошибка загрузки -->
        <div v-if="error" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-3"></i>
                <h3 class="text-lg font-semibold mb-3 text-red-600">Ошибка загрузки остановок</h3>
                <p class="text-color-secondary mb-4">{{ error }}</p>
                <Button
                    label="Повторить"
                    icon="pi pi-refresh"
                    @click="loadStations"
                />
            </div>
        </div>

        <!-- Список остановок -->
        <div v-else class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <Button
                    label="Создать"
                    icon="pi pi-plus"
                    @click="addStation"
                />
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="stations"
                :paginator="true"
                :rows="pagination.size"
                :totalRecords="pagination.totalElements"
                :lazy="true"
                :first="pagination.page * pagination.size"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['name', 'latinName', 'city', 'district', 'region', 'country']"
                showGridlines
                responsiveLayout="scroll"
                @page="onPageChange"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Остановки не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="name" header="Наименование" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по наименованию"
                            @keyup.enter="applyFilters"
                        />
                    </template>
                </Column>

                <Column field="latinName" header="Латинское наименование" :sortable="true" style="min-width: 200px">
                </Column>

                <Column field="city" header="Город" :sortable="true" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по городу"
                            @keyup.enter="applyFilters"
                        />
                    </template>
                </Column>

                <Column field="district" header="Район" :sortable="true" style="min-width: 150px">
                </Column>

                <Column field="region" header="Регион" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по региону"
                            @keyup.enter="applyFilters"
                        />
                    </template>
                </Column>

                <Column field="country" header="Страна" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по стране"
                            @keyup.enter="applyFilters"
                        />
                    </template>
                </Column>

                <Column header="Координаты" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i
                                :class="getCoordinatesIcon(data.hasCoordinates)"
                                :style="{ color: data.hasCoordinates ? '#22c55e' : '#ef4444' }"
                                class="mr-2"
                            ></i>
                            <span>{{ data.hasCoordinates ? 'Есть' : 'Нет' }}</span>
                        </div>
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editStation(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="deleteStation(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.station-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}
</style>
