<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { StationService } from '@/service/StationService';
import { GeographicService } from '@/service/GeographicService';
import AutoComplete from 'primevue/autocomplete';

const route = useRoute();
const router = useRouter();

const projectId = route.params.projectId;
const stationId = route.params.stationId;
const isEdit = computed(() => !!stationId);

const loading = ref(false);
const saving = ref(false);

// Форма данных
const form = ref({
    name: '',
    latinName: '',
    city: '',
    district: '',
    region: '',
    country: 'Россия',
    // UUID для отправки на бэкенд
    cityId: null,
    regionId: null,
    countryId: null,
    districtId: null,
    hasCoordinates: false,
    coordinates: ''
});

// Валидация
const errors = ref({});

// Географические справочники
const countries = ref([]);
const regions = ref([]);
const cities = ref([]);
const districts = ref([]);

// Предложения для автодополнения
const countrySuggestions = ref([]);
const regionSuggestions = ref([]);
const citySuggestions = ref([]);
const districtSuggestions = ref([]);

// Загрузка географических данных
const loadingCountries = ref(false);
const loadingRegions = ref(false);
const loadingCities = ref(false);
const loadingDistricts = ref(false);

onMounted(async () => {
    if (isEdit.value) {
        await loadStation();
    } else {
        // Очищаем форму при создании новой остановки
        clearForm();
    }
});

const searchCountries = async (event) => {
    try {
        const query = event.query;
        console.log('Поиск стран, запрос:', query);
        
        // Проверяем минимальную длину
        if (!query || query.length < 2) {
            console.log('Запрос слишком короткий, очищаем предложения');
            countrySuggestions.value = [];
            return;
        }
        
        loadingCountries.value = true;
        const results = await GeographicService.searchCountries(query);
        console.log('Результаты поиска стран:', results);
        console.log('Тип результатов:', typeof results);
        console.log('Длина результатов:', Array.isArray(results) ? results.length : 'не массив');
        if (Array.isArray(results) && results.length > 0) {
            console.log('Первый элемент:', results[0]);
            console.log('Структура первого элемента:', Object.keys(results[0]));
        }
        countrySuggestions.value = results || [];
    } catch (error) {
        console.error('Ошибка поиска стран:', error);
        countrySuggestions.value = [];
    } finally {
        loadingCountries.value = false;
    }
};

const searchRegions = async (event) => {
    try {
        const query = event.query;
        console.log('Поиск регионов, запрос:', query);
        
        // Проверяем минимальную длину
        if (!query || query.length < 2) {
            console.log('Запрос слишком короткий, очищаем предложения');
            regionSuggestions.value = [];
            return;
        }
        
        loadingRegions.value = true;
        const results = await GeographicService.searchRegions(query);
        console.log('Результаты поиска регионов:', results);
        regionSuggestions.value = results || [];
    } catch (error) {
        console.error('Ошибка поиска регионов:', error);
        regionSuggestions.value = [];
    } finally {
        loadingRegions.value = false;
    }
};

const searchCities = async (event) => {
    try {
        const query = event.query;
        console.log('Поиск городов, запрос:', query);
        
        // Проверяем минимальную длину
        if (!query || query.length < 2) {
            console.log('Запрос слишком короткий, очищаем предложения');
            citySuggestions.value = [];
            return;
        }
        
        loadingCities.value = true;
        const results = await GeographicService.searchCities(query);
        console.log('Результаты поиска городов:', results);
        citySuggestions.value = results || [];
    } catch (error) {
        console.error('Ошибка поиска городов:', error);
        citySuggestions.value = [];
    } finally {
        loadingCities.value = false;
    }
};

const searchDistricts = async (event) => {
    try {
        const query = event.query;
        console.log('Поиск районов, запрос:', query);
        
        // Проверяем минимальную длину
        if (!query || query.length < 2) {
            console.log('Запрос слишком короткий, очищаем предложения');
            districtSuggestions.value = [];
            return;
        }
        
        loadingDistricts.value = true;
        const results = await GeographicService.searchDistricts(query);
        console.log('Результаты поиска районов:', results);
        districtSuggestions.value = results || [];
    } catch (error) {
        console.error('Ошибка поиска районов:', error);
        districtSuggestions.value = [];
    } finally {
        loadingDistricts.value = false;
    }
};

const loadStation = async () => {
    try {
        console.log('Загружаем данные остановки, stationId:', stationId);
        loading.value = true;
        const station = await StationService.getStationById(stationId);
        console.log('Полученные данные остановки:', station);
        
        if (station) {
            // Сначала очищаем форму
            form.value = {
                name: '',
                latinName: '',
                city: '',
                region: '',
                country: '',
                district: '',
                // UUID для отправки на бэкенд
                cityId: null,
                regionId: null,
                countryId: null,
                districtId: null,
                hasCoordinates: false,
                coordinates: ''
            };
            
            // Затем заполняем только те поля, которые есть в данных
            if (station.name) {
                form.value.name = station.name;
            }
            if (station.latinName || station.stLatinName) {
                form.value.latinName = station.latinName || station.stLatinName;
            }
            // Загружаем названия для отображения
            if (station.city) {
                form.value.city = station.city;
            }
            if (station.region) {
                form.value.region = station.region;
            }
            if (station.country) {
                form.value.country = station.country;
            }
            if (station.district) {
                form.value.district = station.district;
            }
            
            // Загружаем UUID для отправки на бэкенд
            if (station.cityId) {
                form.value.cityId = station.cityId;
            }
            if (station.regionId) {
                form.value.regionId = station.regionId;
            }
            if (station.countryId) {
                form.value.countryId = station.countryId;
            }
            if (station.districtId) {
                form.value.districtId = station.districtId;
            }
            
            // Если есть координаты, парсим их
            if (station.latitude && station.longitude) {
                form.value.hasCoordinates = true;
                form.value.coordinates = `${station.latitude}, ${station.longitude}`;
                console.log('Координаты установлены:', form.value.coordinates);
            }
            
            console.log('Форма заполнена:', form.value);
        }
    } catch (error) {
        console.error('Ошибка загрузки остановки:', error);
        // Показать уведомление об ошибке
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    // Проверяем наименование
    if (!form.value.name || !form.value.name.trim()) {
        errors.value.name = 'Наименование обязательно для заполнения';
    }
    
    // Проверяем латинское наименование
    if (!form.value.latinName || !form.value.latinName.trim()) {
        errors.value.latinName = 'Латинское наименование обязательно для заполнения';
    }
    
    // Проверяем город
    if (!form.value.city || !form.value.city.trim()) {
        errors.value.city = 'Город обязателен для заполнения';
    }
    
    // Проверяем регион
    if (!form.value.region || !form.value.region.trim()) {
        errors.value.region = 'Регион обязателен для заполнения';
    }
    
    // Проверяем координаты
    if (form.value.hasCoordinates && (!form.value.coordinates || !form.value.coordinates.trim())) {
        errors.value.coordinates = 'Координаты обязательны при установленном признаке';
    }
    
    console.log('Результат валидации:', errors.value);
    return Object.keys(errors.value).length === 0;
};

const saveStation = async () => {
    console.log('Начинаем сохранение остановки...');
    console.log('isEdit:', isEdit.value);
    console.log('stationId:', stationId);
    console.log('projectId:', projectId);
    console.log('Форма:', form.value);
    console.log('Детали формы:', {
        name: form.value.name,
        latinName: form.value.latinName,
        city: form.value.city,
        region: form.value.region,
        country: form.value.country,
        district: form.value.district,
        hasCoordinates: form.value.hasCoordinates,
        coordinates: form.value.coordinates
    });
    
    if (!validateForm()) {
        console.log('Валидация не прошла, ошибки:', errors.value);
        return;
    }
    
    try {
        saving.value = true;
        console.log('Устанавливаем saving = true');
        
        // Подготавливаем данные для отправки
        const stationData = { ...form.value };
        console.log('Исходные данные формы:', stationData);
        
        // Обрабатываем координаты
        if (stationData.hasCoordinates && stationData.coordinates) {
            const coords = stationData.coordinates.split(',').map(coord => coord.trim());
            if (coords.length === 2) {
                stationData.latitude = parseFloat(coords[0]);
                stationData.longitude = parseFloat(coords[1]);
                console.log('Обработанные координаты:', stationData.latitude, stationData.longitude);
            }
        }
        
        // Используем UUID для географических полей
        stationData.countryId = stationData.countryId;
        stationData.regionId = stationData.regionId;
        stationData.cityId = stationData.cityId;
        stationData.districtId = stationData.districtId;
        
        // Удаляем вспомогательные поля
        delete stationData.hasCoordinates;
        delete stationData.coordinates;
        delete stationData.country;
        delete stationData.region;
        delete stationData.city;
        delete stationData.district;
        console.log('Данные для отправки:', stationData);
        
        if (isEdit.value) {
            console.log('Вызываем updateStation...');
            const result = await StationService.updateStation(stationId, stationData);
            console.log('Результат обновления:', result);
        } else {
            console.log('Вызываем createStation...');
            const result = await StationService.createStation(projectId, stationData);
            console.log('Результат создания:', result);
        }
        
        console.log('Сохранение успешно, переходим к списку...');
        // Возвращаемся к списку остановок
        router.push(`/pro/${projectId}/nsi/station`);
        
    } catch (error) {
        console.error('Ошибка сохранения остановки:', error);
        console.error('Детали ошибки:', {
            message: error.message,
            stack: error.stack,
            response: error.response
        });
        // Показать уведомление об ошибке
    } finally {
        console.log('Устанавливаем saving = false');
        saving.value = false;
    }
};

const cancel = () => {
            router.push(`/pro/${projectId}/nsi/station`);
};

const onCountrySelect = (event) => {
    console.log('Выбрана страна:', event);
    // Устанавливаем выбранную страну (название для отображения)
    form.value.country = event.value.name;
    // Сохраняем UUID для отправки на бэкенд
    form.value.countryId = event.value.id;
    
    // Очищаем зависимые поля
    form.value.region = '';
    form.value.city = '';
    form.value.district = '';
    form.value.regionId = null;
    form.value.cityId = null;
    form.value.districtId = null;
    regionSuggestions.value = [];
    citySuggestions.value = [];
    districtSuggestions.value = [];
};

const onRegionSelect = (event) => {
    console.log('Выбран регион:', event);
    // Устанавливаем выбранный регион (название для отображения)
    form.value.region = event.value.name;
    // Сохраняем UUID для отправки на бэкенд
    form.value.regionId = event.value.id;
    
    // Очищаем зависимые поля
    form.value.city = '';
    form.value.district = '';
    form.value.cityId = null;
    form.value.districtId = null;
    citySuggestions.value = [];
    districtSuggestions.value = [];
};

const onCitySelect = (event) => {
    console.log('Выбран город:', event);
    // Устанавливаем выбранный город (название для отображения)
    form.value.city = event.value.name;
    // Сохраняем UUID для отправки на бэкенд
    form.value.cityId = event.value.id;
    
    // Очищаем зависимые поля
    form.value.district = '';
    form.value.districtId = null;
    districtSuggestions.value = [];
};

const onDistrictSelect = (event) => {
    console.log('Выбран район:', event);
    // Устанавливаем выбранный район (название для отображения)
    form.value.district = event.value.name;
    // Сохраняем UUID для отправки на бэкенд
    form.value.districtId = event.value.id;
};

const onCoordinatesToggle = () => {
    if (!form.value.hasCoordinates) {
        form.value.coordinates = '';
        delete errors.value.coordinates;
    }
};

// Функция транслитерации русского текста в латиницу
const transliterate = (text) => {
    if (!text) return '';
    
    const translitMap = {
        'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
        'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
        'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
        'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch',
        'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya',
        'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'Yo',
        'Ж': 'Zh', 'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M',
        'Н': 'N', 'О': 'O', 'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U',
        'Ф': 'F', 'Х': 'H', 'Ц': 'Ts', 'Ч': 'Ch', 'Ш': 'Sh', 'Щ': 'Sch',
        'Ъ': '', 'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'Yu', 'Я': 'Ya'
    };
    
    return text.split('').map(char => translitMap[char] || char).join('');
};

// Функция генерации латинского наименования
const generateLatinName = () => {
    if (form.value.name) {
        const latinName = transliterate(form.value.name);
        form.value.latinName = latinName;
        console.log('Сгенерировано латинское наименование:', latinName);
    }
};

// Функция автоматической генерации при изменении названия
const onNameChange = () => {
    // Автоматически генерируем латинское наименование при изменении русского названия
    if (form.value.name && !form.value.latinName) {
        // Генерируем только если латинское наименование пустое
        const latinName = transliterate(form.value.name);
        form.value.latinName = latinName;
        console.log('Автоматически сгенерировано латинское наименование:', latinName);
    }
};

// Функция очистки формы
const clearForm = () => {
    form.value = {
        name: '',
        latinName: '',
        city: '',
        region: '',
        country: '',
        district: '',
        // UUID для отправки на бэкенд
        cityId: null,
        regionId: null,
        countryId: null,
        districtId: null,
        hasCoordinates: false,
        coordinates: ''
    };
    errors.value = {};
    console.log('Форма очищена');
};
</script>

<template>
    <div class="station-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">
                {{ isEdit ? 'Редактирование остановки' : 'Создание остановки' }}
            </h2>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>
        
        <div class="card" v-if="!loading">
            <form @submit.prevent="saveStation">
                <div class="grid">
                    <!-- Основная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="name" class="font-medium">Наименование *</label>
                            <div class="flex gap-2">
                                <InputText 
                                    id="name"
                                    v-model="form.name" 
                                    :class="{ 'p-invalid': errors.name }"
                                    placeholder="Введите наименование остановки"
                                    class="flex-1"
                                    @input="onNameChange"
                                />
                                <Button
                                    type="button"
                                    icon="pi pi-language"
                                    @click="generateLatinName"
                                    :disabled="!form.name"
                                    :tooltip="'Сгенерировать латинское наименование'"
                                    class="p-button-outlined"
                                />
                            </div>
                            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="latinName" class="font-medium">Латинское наименование *</label>
                            <InputText 
                                id="latinName"
                                v-model="form.latinName" 
                                :class="{ 'p-invalid': errors.latinName }"
                                placeholder="Введите латинское наименование"
                                class="w-full"
                            />
                            <small v-if="errors.latinName" class="p-error">{{ errors.latinName }}</small>
                        </div>
                    </div>
                    
                    <!-- Географическая информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Географическая информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="country" class="font-medium">Страна</label>
                            <AutoComplete
                                id="country"
                                v-model="form.country"
                                :suggestions="countrySuggestions"
                                @complete="searchCountries"
                                placeholder="Введите название страны"
                                :loading="loadingCountries"
                                class="w-full"
                                @item-select="onCountrySelect"
                                optionLabel="name"
                                :delay="300"
                                :minLength="2"
                                :forceSelection="false"
                            >
                                <template #item="slotProps">
                                    <div class="flex align-items-center">
                                        <div>{{ slotProps.item.name }}</div>
                                    </div>
                                </template>
                            </AutoComplete>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="region" class="font-medium">Регион *</label>
                            <AutoComplete
                                id="region"
                                v-model="form.region"
                                :suggestions="regionSuggestions"
                                @complete="searchRegions"
                                placeholder="Введите название региона"
                                :loading="loadingRegions"
                                :class="{ 'p-invalid': errors.region }"
                                class="w-full"
                                @item-select="onRegionSelect"
                                optionLabel="name"
                                :delay="300"
                                :minLength="2"
                                :forceSelection="false"
                            >
                                <template #item="slotProps">
                                    <div class="flex align-items-center">
                                        <div>{{ slotProps.item.name }}</div>
                                    </div>
                                </template>
                            </AutoComplete>
                            <small v-if="errors.region" class="p-error">{{ errors.region }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="city" class="font-medium">Город *</label>
                            <AutoComplete
                                id="city"
                                v-model="form.city"
                                :suggestions="citySuggestions"
                                @complete="searchCities"
                                placeholder="Введите название города"
                                :loading="loadingCities"
                                :class="{ 'p-invalid': errors.city }"
                                class="w-full"
                                @item-select="onCitySelect"
                                optionLabel="name"
                                :delay="300"
                                :minLength="2"
                                :forceSelection="false"
                            >
                                <template #item="slotProps">
                                    <div class="flex align-items-center">
                                        <div>{{ slotProps.item.name }}</div>
                                    </div>
                                </template>
                            </AutoComplete>
                            <small v-if="errors.city" class="p-error">{{ errors.city }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="district" class="font-medium">Район</label>
                            <AutoComplete
                                id="district"
                                v-model="form.district"
                                :suggestions="districtSuggestions"
                                @complete="searchDistricts"
                                @item-select="onDistrictSelect"
                                placeholder="Введите название района"
                                :loading="loadingDistricts"
                                class="w-full"
                                optionLabel="name"
                                :delay="300"
                                :minLength="2"
                                :forceSelection="false"
                            >
                                <template #item="slotProps">
                                    <div class="flex align-items-center">
                                        <div>{{ slotProps.item.name }}</div>
                                    </div>
                                </template>
                            </AutoComplete>
                        </div>
                    </div>
                    
                    <!-- Координаты -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Координаты</h3>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <div class="flex align-items-center">
                                <Checkbox 
                                    id="hasCoordinates" 
                                    v-model="form.hasCoordinates" 
                                    :binary="true"
                                    @change="onCoordinatesToggle"
                                />
                                <label for="hasCoordinates" class="ml-2 font-medium">
                                    Есть координаты
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6" v-if="form.hasCoordinates">
                        <div class="field">
                            <label for="coordinates" class="font-medium">Координаты *</label>
                            <InputText 
                                id="coordinates"
                                v-model="form.coordinates" 
                                :class="{ 'p-invalid': errors.coordinates }"
                                placeholder="Например: 55.7558, 37.6176"
                                class="w-full"
                            />
                            <small v-if="errors.coordinates" class="p-error">{{ errors.coordinates }}</small>
                            <small class="text-color-secondary">Формат: широта, долгота</small>
                        </div>
                    </div>
                </div>
                
                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        label="Отмена" 
                        icon="pi pi-times" 
                        outlined 
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button 
                        label="Очистить" 
                        icon="pi pi-refresh" 
                        outlined 
                        @click="clearForm"
                        :disabled="saving"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                        @click="saveStation"
                    />
                </div>
            </form>
        </div>
        
        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных остановки...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.station-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}
</style>
