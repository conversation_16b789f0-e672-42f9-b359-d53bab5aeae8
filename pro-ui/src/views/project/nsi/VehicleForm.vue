<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { VehicleService } from '@/service/VehicleService';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();

const projectCode = route.params.code;
const vehicleId = route.params.vehicleId;
const isEdit = computed(() => !!vehicleId);

const loading = ref(false);
const saving = ref(false);
const organizations = ref([]);

// Форма данных
const form = ref({
    registrationNumber: '',
    model: '',
    manufacturer: '',
    year: new Date().getFullYear(),
    capacity: null,
    fuelType: 'diesel',
    vin: '',
    organizationId: null,
    organizationName: '',
    status: 'active',
    lastMaintenanceDate: null,
    nextMaintenanceDate: null
});

// Валидация
const errors = ref({});

const statusOptions = ref([
    { label: 'Активное', value: 'active' },
    { label: 'На обслуживании', value: 'maintenance' },
    { label: 'Неактивное', value: 'inactive' }
]);

const fuelTypeOptions = ref([
    { label: 'Дизель', value: 'diesel' },
    { label: 'Бензин', value: 'gasoline' },
    { label: 'Газ', value: 'gas' },
    { label: 'Электричество', value: 'electric' }
]);

const yearOptions = ref(() => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = currentYear; year >= 1990; year--) {
        years.push({ label: year.toString(), value: year });
    }
    return years;
});

onMounted(async () => {
    await loadOrganizations();
    if (isEdit.value) {
        await loadVehicle();
    }
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations();
        organizations.value = data.filter(org => org.status === 'active').map(org => ({
            label: org.shortName,
            value: org.id,
            fullName: org.name
        }));
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
    }
};

const loadVehicle = async () => {
    try {
        loading.value = true;
        const vehicle = await VehicleService.getVehicleById(vehicleId);
        if (vehicle) {
            form.value = { ...vehicle };
            // Преобразуем даты в формат для Calendar
            if (vehicle.lastMaintenanceDate) {
                form.value.lastMaintenanceDate = new Date(vehicle.lastMaintenanceDate);
            }
            if (vehicle.nextMaintenanceDate) {
                form.value.nextMaintenanceDate = new Date(vehicle.nextMaintenanceDate);
            }
        }
    } catch (error) {
        console.error('Ошибка загрузки транспортного средства:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    if (!form.value.registrationNumber.trim()) {
        errors.value.registrationNumber = 'Государственный номер обязателен для заполнения';
    }
    
    if (!form.value.model.trim()) {
        errors.value.model = 'Модель обязательна для заполнения';
    }
    
    if (!form.value.manufacturer.trim()) {
        errors.value.manufacturer = 'Производитель обязателен для заполнения';
    }
    
    if (!form.value.year || form.value.year < 1990 || form.value.year > new Date().getFullYear()) {
        errors.value.year = 'Укажите корректный год выпуска';
    }
    
    if (!form.value.capacity || form.value.capacity < 1) {
        errors.value.capacity = 'Вместимость должна быть больше 0';
    }
    
    if (!form.value.vin.trim()) {
        errors.value.vin = 'VIN номер обязателен для заполнения';
    } else if (!/^[A-HJ-NPR-Z0-9]{17}$/.test(form.value.vin)) {
        errors.value.vin = 'VIN номер должен содержать 17 символов (латинские буквы и цифры)';
    }
    
    if (!form.value.organizationId) {
        errors.value.organizationId = 'Организация обязательна для выбора';
    }
    
    return Object.keys(errors.value).length === 0;
};

const saveVehicle = async () => {
    if (!validateForm()) {
        return;
    }
    
    try {
        saving.value = true;
        
        // Подготавливаем данные для сохранения
        const vehicleData = {
            ...form.value,
            lastMaintenanceDate: form.value.lastMaintenanceDate ? form.value.lastMaintenanceDate.toISOString() : null,
            nextMaintenanceDate: form.value.nextMaintenanceDate ? form.value.nextMaintenanceDate.toISOString() : null
        };
        
        if (isEdit.value) {
            await VehicleService.updateVehicle(vehicleId, vehicleData);
            console.log('Транспортное средство обновлено:', vehicleData);
        } else {
            await VehicleService.createVehicle(projectCode, vehicleData);
            console.log('Транспортное средство создано:', vehicleData);
        }
        
        router.push(`/pro/${projectCode}/nsi/vehicle`);
        
    } catch (error) {
        console.error('Ошибка сохранения транспортного средства:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectCode}/nsi/vehicle`);
};

const onOrganizationChange = () => {
    const selectedOrg = organizations.value.find(org => org.value === form.value.organizationId);
    if (selectedOrg) {
        form.value.organizationName = selectedOrg.fullName;
    }
    delete errors.value.organizationId;
};

// Форматирование государственного номера
const formatRegistrationNumber = (value) => {
    return value.toUpperCase().replace(/[^А-Я0-9]/g, '');
};

const onRegistrationNumberInput = (event) => {
    const formatted = formatRegistrationNumber(event.target.value);
    form.value.registrationNumber = formatted;
    event.target.value = formatted;
};

// Форматирование VIN номера
const onVinInput = (event) => {
    const cleaned = event.target.value.toUpperCase().replace(/[^A-HJ-NPR-Z0-9]/g, '').substring(0, 17);
    form.value.vin = cleaned;
    event.target.value = cleaned;
};

// Форматирование вместимости
const onCapacityInput = (event) => {
    const value = parseInt(event.target.value) || null;
    form.value.capacity = value;
};
</script>

<template>
    <div class="vehicle-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">
                {{ isEdit ? 'Редактирование транспортного средства' : 'Создание транспортного средства' }}
            </h2>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>
        
        <div class="card" v-if="!loading">
            <form @submit.prevent="saveVehicle">
                <div class="grid">
                    <!-- Основная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="registrationNumber" class="font-medium">Государственный номер *</label>
                            <InputText 
                                id="registrationNumber"
                                v-model="form.registrationNumber" 
                                :class="{ 'p-invalid': errors.registrationNumber }"
                                placeholder="А123БВ777"
                                @input="onRegistrationNumberInput"
                                class="w-full font-mono"
                            />
                            <small v-if="errors.registrationNumber" class="p-error">{{ errors.registrationNumber }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="model" class="font-medium">Модель *</label>
                            <InputText 
                                id="model"
                                v-model="form.model" 
                                :class="{ 'p-invalid': errors.model }"
                                placeholder="ПАЗ-3205"
                                class="w-full"
                            />
                            <small v-if="errors.model" class="p-error">{{ errors.model }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="manufacturer" class="font-medium">Производитель *</label>
                            <InputText 
                                id="manufacturer"
                                v-model="form.manufacturer" 
                                :class="{ 'p-invalid': errors.manufacturer }"
                                placeholder="ПАЗ"
                                class="w-full"
                            />
                            <small v-if="errors.manufacturer" class="p-error">{{ errors.manufacturer }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-3">
                        <div class="field">
                            <label for="year" class="font-medium">Год выпуска *</label>
                            <Dropdown 
                                id="year"
                                v-model="form.year" 
                                :options="yearOptions()" 
                                optionLabel="label" 
                                optionValue="value"
                                :class="{ 'p-invalid': errors.year }"
                                placeholder="Выберите год"
                                class="w-full"
                            />
                            <small v-if="errors.year" class="p-error">{{ errors.year }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-3">
                        <div class="field">
                            <label for="capacity" class="font-medium">Вместимость *</label>
                            <InputNumber 
                                id="capacity"
                                v-model="form.capacity" 
                                :class="{ 'p-invalid': errors.capacity }"
                                placeholder="25"
                                :min="1"
                                :max="300"
                                class="w-full"
                            />
                            <small v-if="errors.capacity" class="p-error">{{ errors.capacity }}</small>
                            <small v-else class="text-color-secondary">Количество пассажиров</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-3">
                        <div class="field">
                            <label for="fuelType" class="font-medium">Тип топлива</label>
                            <Dropdown 
                                id="fuelType"
                                v-model="form.fuelType" 
                                :options="fuelTypeOptions" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите тип топлива"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-3">
                        <div class="field">
                            <label for="status" class="font-medium">Статус</label>
                            <Dropdown 
                                id="status"
                                v-model="form.status" 
                                :options="statusOptions" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите статус"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <!-- Технические данные -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Технические данные</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="vin" class="font-medium">VIN номер *</label>
                            <InputText 
                                id="vin"
                                v-model="form.vin" 
                                :class="{ 'p-invalid': errors.vin }"
                                placeholder="XTT320500L0123456"
                                maxlength="17"
                                @input="onVinInput"
                                class="w-full font-mono"
                            />
                            <small v-if="errors.vin" class="p-error">{{ errors.vin }}</small>
                            <small v-else class="text-color-secondary">17 символов (латинские буквы и цифры)</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="organizationId" class="font-medium">Организация *</label>
                            <Dropdown 
                                id="organizationId"
                                v-model="form.organizationId" 
                                :options="organizations" 
                                optionLabel="label" 
                                optionValue="value"
                                :class="{ 'p-invalid': errors.organizationId }"
                                placeholder="Выберите организацию"
                                class="w-full"
                                @change="onOrganizationChange"
                            />
                            <small v-if="errors.organizationId" class="p-error">{{ errors.organizationId }}</small>
                        </div>
                    </div>
                    
                    <!-- Обслуживание -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Обслуживание</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="lastMaintenanceDate" class="font-medium">Дата последнего ТО</label>
                            <Calendar 
                                id="lastMaintenanceDate"
                                v-model="form.lastMaintenanceDate" 
                                placeholder="Выберите дату"
                                dateFormat="dd.mm.yy"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="nextMaintenanceDate" class="font-medium">Дата следующего ТО</label>
                            <Calendar 
                                id="nextMaintenanceDate"
                                v-model="form.nextMaintenanceDate" 
                                placeholder="Выберите дату"
                                dateFormat="dd.mm.yy"
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>
                
                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        label="Отмена" 
                        icon="pi pi-times" 
                        outlined 
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>
        
        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных транспортного средства...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.vehicle-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
