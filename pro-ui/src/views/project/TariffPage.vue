<template>
    <div class="tariff-page">
        <div class="flex justify-content-between align-items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold m-0 mb-2">Тарифы</h1>
                <p class="text-xl text-color-secondary m-0">
                    Управление тарифами проекта "{{ project?.name || 'Загрузка...' }}"
                </p>
            </div>
            <div class="flex gap-3">
                <Button
                    label="Назад к проекту"
                    icon="pi pi-arrow-left"
                    class="p-button-outlined"
                    @click="goBack"
                />
            </div>
        </div>

        <!-- Поиск и фильтры -->
        <div class="card mb-4">
            <div class="flex flex-column md:flex-row gap-4">
                <div class="flex-1">
                    <span class="p-input-icon-left w-full">
                        <i class="pi pi-search" />
                        <InputText
                            v-model="searchQuery"
                            placeholder="Поиск по маршруту или названию тарифа..."
                            class="w-full"
                            @input="onSearch"
                        />
                    </span>
                </div>
                <div class="flex gap-2">
                    <Dropdown
                        v-model="selectedRouteFilter"
                        :options="routeFilterOptions"
                        option-label="label"
                        option-value="value"
                        placeholder="Все маршруты"
                        class="w-12rem"
                        @change="onFilterChange"
                    />
                    <Dropdown
                        v-model="selectedTariffFilter"
                        :options="tariffFilterOptions"
                        option-label="label"
                        option-value="value"
                        placeholder="Все тарифы"
                        class="w-12rem"
                        @change="onFilterChange"
                    />
                </div>
            </div>
        </div>

        <!-- Таблица тарифов -->
        <div class="card">
            <DataTable
                :value="filteredTariffData"
                :loading="loading"
                :paginator="true"
                :rows="pageSize"
                :total-records="totalRecords"
                :rows-per-page-options="[10, 20, 50, 100]"
                paginator-template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                current-page-report-template="Показано {first} - {last} из {totalRecords} записей"
                @page="onPageChange"
                striped-rows
                class="p-datatable-sm"
            >
                <!-- Колонка с маршрутами -->
                <Column field="routeName" header="Маршрут" sortable>
                    <template #body="{ data }">
                        <div class="font-semibold">{{ data.routeName }}</div>
                        <div class="text-sm text-color-secondary">{{ data.routeDescription }}</div>
                    </template>
                </Column>

                <!-- Динамические колонки для тарифов -->
                <Column
                    v-for="tariff in availableTariffs"
                    :key="tariff.id"
                    :field="`tariffs.${tariff.id}`"
                    :header="tariff.name"
                    class="min-w-20rem"
                >
                    <template #body="{ data }">
                        <div v-if="data.tariffs[tariff.id]" class="tariff-cell">
                            <div class="tariff-info">
                                <div class="font-semibold text-primary">{{ data.tariffs[tariff.id].name }}</div>
                                <div class="text-sm text-color-secondary mt-1">
                                    Статус: 
                                    <Tag 
                                        :value="getStatusLabel(data.tariffs[tariff.id].status)"
                                        :severity="getStatusSeverity(data.tariffs[tariff.id].status)"
                                        class="ml-1"
                                    />
                                </div>
                                <div v-if="data.tariffs[tariff.id].tags" class="text-sm text-color-secondary mt-1">
                                    Тэги: {{ data.tariffs[tariff.id].tags }}
                                </div>
                            </div>
                            
                            <div class="payment-methods mt-2">
                                <div class="text-sm font-medium mb-1">Способы оплаты:</div>
                                <div class="flex flex-wrap gap-1">
                                    <Chip
                                        v-for="method in data.tariffs[tariff.id].paymentMethods"
                                        :key="method.id"
                                        :label="getPaymentMethodLabel(method.type)"
                                        class="text-xs"
                                    />
                                </div>
                            </div>
                            
                            <Button
                                label="Настроить"
                                icon="pi pi-cog"
                                size="small"
                                class="p-button-outlined mt-2"
                                @click="openTariffSettings(data.routeId, tariff.id)"
                            />
                        </div>
                        <div v-else class="no-tariff-cell">
                            <div class="text-center text-color-secondary">
                                <i class="pi pi-info-circle text-xl mb-2"></i>
                                <div class="font-medium">Тариф не задан</div>
                                <Button
                                    label="Добавить тариф"
                                    icon="pi pi-plus"
                                    size="small"
                                    class="p-button-text mt-2"
                                    @click="addTariffToRoute(data.routeId)"
                                />
                            </div>
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>

        <!-- Диалог настроек тарифа -->
        <TariffSettingsDialog
            v-model:visible="settingsDialogVisible"
            :tariff-id="selectedTariffId"
            :route-id="selectedRouteId"
            :project-id="project?.id"
            @saved="onTariffSettingsSaved"
        />

        <!-- Диалог добавления тарифа -->
        <AddTariffDialog
            v-model:visible="addTariffDialogVisible"
            :route-id="selectedRouteId"
            @saved="onTariffAdded"
        />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import { TariffStorageService } from '@/service/TariffStorageService';
import { ProjectService } from '@/service/ProjectService';
import TariffSettingsDialog from '@/components/TariffSettingsDialog.vue';
import AddTariffDialog from '@/components/AddTariffDialog.vue';
import Button from 'primevue/button';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';
import Tag from 'primevue/tag';
import Chip from 'primevue/chip';

const route = useRoute();
const router = useRouter();
const toast = useToast();

// Реактивные данные
const project = ref(null);
const loading = ref(false);
const searchQuery = ref('');
const selectedRouteFilter = ref(null);
const selectedTariffFilter = ref(null);
const pageSize = ref(20);
const currentPage = ref(0);
const totalRecords = ref(0);

// Диалоги
const settingsDialogVisible = ref(false);
const addTariffDialogVisible = ref(false);
const selectedTariffId = ref(null);
const selectedRouteId = ref(null);

// Данные из localStorage
const tariffData = ref([]);
const availableTariffs = ref([]);
const routes = ref([]);

// Фильтры
const routeFilterOptions = computed(() => [
    { label: 'Все маршруты', value: null },
    ...routes.value.map(route => ({
        label: route.name,
        value: route.id
    }))
]);

const tariffFilterOptions = computed(() => [
    { label: 'Все тарифы', value: null },
    ...availableTariffs.value.map(tariff => ({
        label: tariff.name,
        value: tariff.id
    }))
]);

// Отфильтрованные данные
const filteredTariffData = computed(() => {
    let filtered = tariffData.value;

    // Фильтр по поиску
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        filtered = filtered.filter(item => 
            item.routeName.toLowerCase().includes(query) ||
            Object.values(item.tariffs).some(tariff => 
                tariff.name.toLowerCase().includes(query)
            )
        );
    }

    // Фильтр по маршруту
    if (selectedRouteFilter.value) {
        filtered = filtered.filter(item => item.routeId === selectedRouteFilter.value);
    }

    // Фильтр по тарифу
    if (selectedTariffFilter.value) {
        filtered = filtered.filter(item => 
            Object.keys(item.tariffs).includes(selectedTariffFilter.value)
        );
    }

    return filtered;
});

// Методы
const loadProject = async () => {
    try {
        const projectId = route.params.projectId;
        project.value = await ProjectService.getProjectById(projectId);
    } catch (error) {
        console.error('Ошибка загрузки проекта:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить проект',
            life: 3000
        });
    }
};

const loadData = async () => {
    try {
        loading.value = true;
        
        // Загружаем данные из localStorage
        const storageService = new TariffStorageService();
        const data = await storageService.loadTariffData();
        
        tariffData.value = data.tariffData || [];
        availableTariffs.value = data.availableTariffs || [];
        routes.value = data.routes || [];
        
        totalRecords.value = tariffData.value.length;
        
    } catch (error) {
        console.error('Ошибка загрузки данных:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить данные',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

const onSearch = () => {
    currentPage.value = 0;
};

const onFilterChange = () => {
    currentPage.value = 0;
};

const onPageChange = (event) => {
    currentPage.value = event.page;
    pageSize.value = event.rows;
};

const openTariffSettings = (routeId, tariffId) => {
    selectedRouteId.value = routeId;
    selectedTariffId.value = tariffId;
    settingsDialogVisible.value = true;
};

const addTariffToRoute = (routeId) => {
    selectedRouteId.value = routeId;
    addTariffDialogVisible.value = true;
};

const onTariffSettingsSaved = () => {
    loadData();
    toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Настройки тарифа сохранены',
        life: 3000
    });
};

const onTariffAdded = () => {
    loadData();
    toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Тариф добавлен',
        life: 3000
    });
};

const getStatusLabel = (status) => {
    const labels = {
        'ACTIVE': 'Активен',
        'DISABLED': 'Отключен',
        'BLOCKED': 'Заблокирован',
        'IS_DELETED': 'Удален'
    };
    return labels[status] || status;
};

const getStatusSeverity = (status) => {
    const severities = {
        'ACTIVE': 'success',
        'DISABLED': 'warning',
        'BLOCKED': 'danger',
        'IS_DELETED': 'secondary'
    };
    return severities[status] || 'info';
};

const getPaymentMethodLabel = (method) => {
    const labels = {
        'CASH': 'Наличные',
        'EMV': 'Банковская карта',
        'TROIKA_TICKET': 'Тройка (билет)',
        'TROIKA_WALLET': 'Тройка (кошелек)',
        'ABT_TICKET': 'Транспортная карта (билет)',
        'ABT_WALLET': 'Транспортная карта (кошелек)',
        'PROSTOR_TICKET': 'Простор (билет)',
        'QR_TICKET': 'QR (билет)',
        'QR_WALLET': 'QR (кошелек)'
    };
    return labels[method] || method;
};

const goBack = () => {
    router.push(`/pro/${route.params.projectId}`);
};

// Инициализация
onMounted(() => {
    loadProject();
    loadData();
});
</script>

<style scoped>
.tariff-page {
    padding: 1.5rem;
}

.tariff-cell {
    padding: 0.5rem;
    border-radius: 0.375rem;
    background-color: var(--surface-50);
}

.no-tariff-cell {
    padding: 1rem;
    border-radius: 0.375rem;
    background-color: var(--surface-100);
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-methods {
    border-top: 1px solid var(--surface-200);
    padding-top: 0.5rem;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
    background-color: var(--surface-100);
    border-bottom: 2px solid var(--surface-200);
    font-weight: 600;
}

:deep(.p-datatable .p-datatable-tbody > tr:nth-child(even)) {
    background-color: var(--surface-50);
}

:deep(.p-chip) {
    font-size: 0.75rem;
    height: 1.5rem;
}
</style> 