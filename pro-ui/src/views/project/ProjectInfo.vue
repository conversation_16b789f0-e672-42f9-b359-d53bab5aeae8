<script setup>
import { useRoute } from 'vue-router';
import { ref, onMounted } from 'vue';

const route = useRoute();
const projectCode = route.params.code;

// Информация о проекте
const projectInfo = ref({
    name: 'СберТройка ПРО',
    code: projectCode,
    description: 'Система управления общественным транспортом для интеграции с платформой СберТройка',
    status: 'active',
    version: '2.1.4',
    createdDate: '2023-01-15T09:00:00Z',
    lastUpdated: '2024-01-20T14:30:00Z',
    environment: 'production',
    region: 'Москва и Московская область'
});

// Технические характеристики
const technicalSpecs = ref({
    database: 'PostgreSQL 14.2',
    backend: 'Spring Boot 3.0',
    frontend: 'Vue.js 3.4',
    deployment: 'Kubernetes',
    monitoring: 'Prometheus + Grafana',
    backup: 'Ежедневно в 02:00'
});

// Участники проекта
const projectTeam = ref([
    {
        name: 'Иванов Иван Иванович',
        role: 'Руководитель проекта',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-67',
        avatar: 'ИИ'
    },
    {
        name: 'Петрова Анна Сергеевна',
        role: 'Технический архитектор',
        email: '<EMAIL>',
        phone: '+7 (495) 234-56-78',
        avatar: 'ПА'
    },
    {
        name: 'Сидоров Михаил Владимирович',
        role: 'Ведущий разработчик',
        email: '<EMAIL>',
        phone: '+7 (495) 345-67-89',
        avatar: 'СМ'
    },
    {
        name: 'Козлова Елена Дмитриевна',
        role: 'Аналитик',
        email: '<EMAIL>',
        phone: '+7 (495) 456-78-90',
        avatar: 'КЕ'
    }
]);

// Статистика проекта
const projectStats = ref({
    totalUsers: 1247,
    activeUsers: 1089,
    totalTransactions: 45678,
    uptime: 99.8,
    responseTime: 120
});

// Последние обновления
const recentUpdates = ref([
    {
        version: '2.1.4',
        date: '2024-01-20',
        description: 'Добавлена поддержка новых типов транспорта',
        type: 'feature'
    },
    {
        version: '2.1.3',
        date: '2024-01-15',
        description: 'Исправлены ошибки в модуле маршрутизации',
        type: 'bugfix'
    },
    {
        version: '2.1.2',
        date: '2024-01-10',
        description: 'Улучшена производительность базы данных',
        type: 'improvement'
    },
    {
        version: '2.1.1',
        date: '2024-01-05',
        description: 'Обновления безопасности',
        type: 'security'
    }
]);

onMounted(() => {
    // Здесь можно загрузить актуальную информацию о проекте
});

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'maintenance': return 'warning';
        case 'inactive': return 'secondary';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'maintenance': return 'Обслуживание';
        case 'inactive': return 'Неактивный';
        default: return 'Неизвестно';
    }
};

const getUpdateTypeIcon = (type) => {
    switch (type) {
        case 'feature': return 'pi pi-plus-circle';
        case 'bugfix': return 'pi pi-wrench';
        case 'improvement': return 'pi pi-arrow-up';
        case 'security': return 'pi pi-shield';
        default: return 'pi pi-circle';
    }
};

const getUpdateTypeColor = (type) => {
    switch (type) {
        case 'feature': return '#10b981';
        case 'bugfix': return '#f59e0b';
        case 'improvement': return '#3b82f6';
        case 'security': return '#ef4444';
        default: return '#6b7280';
    }
};
</script>

<template>
    <div class="project-info">
        <!-- Заголовок -->
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Общая информация о проекте</h1>
            <div class="flex gap-2">
                <Button icon="pi pi-refresh" label="Обновить" outlined size="small" />
                <Button icon="pi pi-cog" label="Настройки" outlined size="small" />
            </div>
        </div>

        <div class="grid">
            <!-- Основная информация -->
            <div class="col-12 lg:col-8">
                <div class="card mb-4">
                    <div class="flex align-items-center mb-4">
                        <div class="w-4rem h-4rem bg-primary-100 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-chart-bar text-primary text-2xl"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-bold m-0">{{ projectInfo.name }}</h2>
                            <p class="text-color-secondary m-0">{{ projectInfo.description }}</p>
                        </div>
                    </div>

                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Код проекта</label>
                                <p class="m-0 font-mono text-lg">{{ projectInfo.code }}</p>
                            </div>
                        </div>
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Статус</label>
                                <div>
                                    <Tag
                                        :value="getStatusLabel(projectInfo.status)"
                                        :severity="getStatusSeverity(projectInfo.status)"
                                        class="text-sm"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Версия</label>
                                <p class="m-0 font-mono">{{ projectInfo.version }}</p>
                            </div>
                        </div>
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Среда</label>
                                <p class="m-0">{{ projectInfo.environment }}</p>
                            </div>
                        </div>
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Регион</label>
                                <p class="m-0">{{ projectInfo.region }}</p>
                            </div>
                        </div>
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-semibold text-color-secondary">Дата создания</label>
                                <p class="m-0">{{ formatDate(projectInfo.createdDate) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Технические характеристики -->
                <div class="card mb-4">
                    <h3 class="text-lg font-semibold mb-4">Технические характеристики</h3>
                    <div class="grid">
                        <div class="col-12 md:col-6" v-for="(value, key) in technicalSpecs" :key="key">
                            <div class="field">
                                <label class="font-semibold text-color-secondary text-sm">
                                    {{ key === 'database' ? 'База данных' :
                                       key === 'backend' ? 'Backend' :
                                       key === 'frontend' ? 'Frontend' :
                                       key === 'deployment' ? 'Развертывание' :
                                       key === 'monitoring' ? 'Мониторинг' :
                                       key === 'backup' ? 'Резервное копирование' : key }}
                                </label>
                                <p class="m-0 font-mono">{{ value }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Последние обновления -->
                <div class="card">
                    <h3 class="text-lg font-semibold mb-4">Последние обновления</h3>
                    <Timeline :value="recentUpdates" class="w-full">
                        <template #marker="{ item }">
                            <div class="w-2rem h-2rem border-round flex align-items-center justify-content-center"
                                 :style="{ backgroundColor: getUpdateTypeColor(item.type), color: 'white' }">
                                <i :class="getUpdateTypeIcon(item.type)" class="text-sm"></i>
                            </div>
                        </template>
                        <template #content="{ item }">
                            <div class="ml-3">
                                <div class="flex align-items-center mb-1">
                                    <span class="font-semibold mr-2">{{ item.version }}</span>
                                    <small class="text-color-secondary">{{ item.date }}</small>
                                </div>
                                <p class="m-0 text-sm">{{ item.description }}</p>
                            </div>
                        </template>
                    </Timeline>
                </div>
            </div>

            <!-- Боковая панель -->
            <div class="col-12 lg:col-4">
                <!-- Статистика -->
                <div class="card mb-4">
                    <h3 class="text-lg font-semibold mb-4">Статистика</h3>
                    <div class="space-y-4">
                        <div class="flex justify-content-between align-items-center">
                            <span class="text-color-secondary">Всего пользователей</span>
                            <span class="font-bold text-lg">{{ projectStats.totalUsers.toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-content-between align-items-center">
                            <span class="text-color-secondary">Активных пользователей</span>
                            <span class="font-bold text-lg text-green-600">{{ projectStats.activeUsers.toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-content-between align-items-center">
                            <span class="text-color-secondary">Транзакций</span>
                            <span class="font-bold text-lg">{{ projectStats.totalTransactions.toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-content-between align-items-center">
                            <span class="text-color-secondary">Время отклика</span>
                            <span class="font-bold text-lg">{{ projectStats.responseTime }}мс</span>
                        </div>
                        <div class="flex justify-content-between align-items-center">
                            <span class="text-color-secondary">Uptime</span>
                            <span class="font-bold text-lg text-green-600">{{ projectStats.uptime }}%</span>
                        </div>
                    </div>
                </div>

                <!-- Команда проекта -->
                <div class="card">
                    <h3 class="text-lg font-semibold mb-4">Команда проекта</h3>
                    <div class="space-y-3">
                        <div v-for="member in projectTeam" :key="member.email" class="flex align-items-center mb-3">
                            <Avatar
                                :label="member.avatar"
                                size="normal"
                                shape="circle"
                                class="mr-3"
                            />
                            <div class="flex-1">
                                <div class="font-semibold text-sm">{{ member.name }}</div>
                                <div class="text-color-secondary text-xs">{{ member.role }}</div>
                                <div class="text-color-secondary text-xs">{{ member.email }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.project-info {
    height: 100%;
    padding: 1rem;
    overflow-y: auto;
}

.field {
    margin-bottom: 1.5rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.card {
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.font-mono {
    font-family: 'Courier New', monospace;
}

.text-green-600 {
    color: #16a34a;
}

.bg-primary-100 {
    background-color: #dbeafe;
}

.text-primary {
    color: #3b82f6;
}

.space-y-4 > * + * {
    margin-top: 1rem;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

:deep(.p-timeline .p-timeline-event-content) {
    padding: 0.5rem 0;
}

:deep(.p-timeline .p-timeline-event-marker) {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.p-avatar) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
}
</style>
