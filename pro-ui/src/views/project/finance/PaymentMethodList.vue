<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { PaymentMethodService } from '@/service/PaymentMethodService';
import { ContractService } from '@/service/ContractService';

const route = useRoute();
const router = useRouter();
const projectCode = route.params.code;

const paymentMethods = ref([]);
const contract = ref(null);
const loading = ref(true);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    code: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        code: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};

onMounted(async () => {
    await loadProjectContract();
    if (contract.value) {
        await loadPaymentMethods();
    }
});

const loadProjectContract = async () => {
    try {
        const contracts = await ContractService.getContractsByProject(projectCode);
        if (contracts && contracts.length > 0) {
            contract.value = contracts[0];
        }
    } catch (error) {
        console.error('Ошибка загрузки договора проекта:', error);
    }
};

const loadPaymentMethods = async () => {
    try {
        loading.value = true;
        const data = await PaymentMethodService.getPaymentMethodsByContract(contract.value.id);
        paymentMethods.value = data;
    } catch (error) {
        console.error('Ошибка загрузки способов оплаты:', error);
        paymentMethods.value = [];
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const createPaymentMethod = () => {
    router.push(`/pro/${projectCode}/finance/payment-method/create`);
};

const editPaymentMethod = (method) => {
    router.push(`/pro/${projectCode}/finance/payment-method/${method.id}/edit`);
};

const deletePaymentMethod = async (method) => {
    if (confirm(`Вы уверены, что хотите удалить способ оплаты "${method.name}"?`)) {
        try {
            await PaymentMethodService.deletePaymentMethod(method.id);
            console.log('Способ оплаты удален:', method.id);
            await loadPaymentMethods();
        } catch (error) {
            console.error('Ошибка удаления способа оплаты:', error);
        }
    }
};

const goToContract = () => {
    router.push(`/pro/${projectCode}/finance/contract`);
};

const goToCashFlow = (method) => {
    router.push(`/pro/${projectCode}/finance/cash-flow?method=${method.id}`);
};
</script>

<template>
    <div class="payment-method-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Способы оплаты</h2>
            <div class="flex gap-2">
                <Button 
                    label="К договору" 
                    icon="pi pi-arrow-left" 
                    outlined
                    @click="goToContract"
                />
            </div>
        </div>

        <!-- Информация о договоре -->
        <div class="card mb-4" v-if="contract">
            <div class="flex align-items-center">
                <div class="w-3rem h-3rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                    <i class="pi pi-file-edit text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold m-0 mb-1">{{ contract.name }}</h3>
                    <p class="text-color-secondary m-0">Договор № {{ contract.number }}</p>
                </div>
            </div>
        </div>

        <!-- Нет договора -->
        <div v-if="!contract" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-color-secondary mb-3"></i>
                <h3 class="text-lg font-semibold mb-3">Договор не найден</h3>
                <p class="text-color-secondary mb-4">
                    Для настройки способов оплаты необходимо создать договор проекта.
                </p>
                <Button 
                    label="Перейти к договору" 
                    icon="pi pi-arrow-right" 
                    @click="goToContract"
                />
            </div>
        </div>

        <!-- Таблица способов оплаты -->
        <div v-else class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <Button 
                    label="Добавить способ оплаты" 
                    icon="pi pi-plus" 
                    @click="createPaymentMethod"
                />
                <div class="flex gap-2">
                    <Button 
                        type="button" 
                        icon="pi pi-filter-slash" 
                        label="Очистить" 
                        outlined 
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText 
                            v-model="filters.global.value" 
                            placeholder="Поиск по всем полям" 
                        />
                    </IconField>
                </div>
            </div>
            
            <DataTable
                :value="paymentMethods"
                :paginator="true"
                :rows="15"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['code', 'name']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Способы оплаты не добавлены</p>
                        <Button 
                            label="Добавить первый способ оплаты" 
                            icon="pi pi-plus" 
                            class="mt-3"
                            @click="createPaymentMethod"
                        />
                    </div>
                </template>
                
                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>
                
                <Column field="code" header="Код" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по коду" 
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.code }}</span>
                    </template>
                </Column>
                
                <Column field="name" header="Наименование" :sortable="true" style="min-width: 400px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по наименованию" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-credit-card mr-2 text-color-secondary"></i>
                            <span>{{ data.name }}</span>
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button 
                                icon="pi pi-chart-line" 
                                size="small" 
                                text 
                                @click="goToCashFlow(data)"
                                v-tooltip.top="'Движение средств'"
                            />
                            <Button 
                                icon="pi pi-pencil" 
                                size="small" 
                                text 
                                @click="editPaymentMethod(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                size="small" 
                                text 
                                severity="danger" 
                                @click="deletePaymentMethod(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.payment-method-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.text-blue-600 {
    color: #2563eb;
}
</style>
