<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { ContractService } from '@/service/ContractService';

const route = useRoute();
const router = useRouter();
const projectCode = route.params.code;

const loading = ref(true);
const contract = ref(null);
const syncing = ref(false);

onMounted(() => {
    loadProjectContract();
});

const loadProjectContract = async () => {
    try {
        loading.value = true;
        // Получаем договор для данного проекта (в реальности будет один договор на проект)
        const contracts = await ContractService.getContractsByProject(projectCode);
        if (contracts && contracts.length > 0) {
            contract.value = contracts[0]; // Берем первый (и единственный) договор проекта
        }
    } catch (error) {
        console.error('Ошибка загрузки договора проекта:', error);
    } finally {
        loading.value = false;
    }
};

const createContract = () => {
    router.push(`/pro/${projectCode}/finance/contract/create`);
};

const editContract = () => {
    if (contract.value) {
        router.push(`/pro/${projectCode}/finance/contract/${contract.value.id}/edit`);
    }
};

const syncContract = async () => {
    if (!contract.value) return;

    try {
        syncing.value = true;
        const result = await ContractService.syncContract(contract.value.id);

        if (result.success) {
            contract.value.syncStatus = 'synced';
            contract.value.lastSyncDate = result.syncDate;
            console.log('Синхронизация успешна:', result.message);
        } else {
            console.error('Ошибка синхронизации:', result.message);
        }
    } catch (error) {
        console.error('Ошибка синхронизации:', error);
    } finally {
        syncing.value = false;
    }
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'draft': return 'secondary';
        case 'expiring': return 'warning';
        case 'completed': return 'info';
        case 'terminated': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'draft': return 'Черновик';
        case 'expiring': return 'Истекает';
        case 'completed': return 'Завершен';
        case 'terminated': return 'Расторгнут';
        default: return 'Неизвестно';
    }
};

const getSyncStatusSeverity = (syncStatus) => {
    switch (syncStatus) {
        case 'synced': return 'success';
        case 'pending': return 'warning';
        case 'error': return 'danger';
        case 'never': return 'secondary';
        default: return 'secondary';
    }
};

const getSyncStatusLabel = (syncStatus) => {
    switch (syncStatus) {
        case 'synced': return 'Синхронизирован';
        case 'pending': return 'Ожидает синхронизации';
        case 'error': return 'Ошибка синхронизации';
        case 'never': return 'Не синхронизировался';
        default: return 'Неизвестно';
    }
};

const getContractTypeLabel = (type) => {
    switch (type) {
        case 'system_rules': return 'Правила системы';
        case 'development_agreement': return 'Договор разработки';
        case 'service_agreement': return 'Договор услуг';
        case 'supply_agreement': return 'Договор поставки';
        default: return 'Неизвестно';
    }
};

const getProjectTypeLabel = (type) => {
    switch (type) {
        case 'transport_system': return 'Транспортная система';
        case 'metro_system': return 'Система метрополитена';
        case 'bus_system': return 'Автобусная система';
        case 'innovation_system': return 'Инновационная система';
        default: return 'Неизвестно';
    }
};

const getRoleIcon = (role) => {
    switch (role) {
        case 'operator': return 'pi pi-star';
        case 'carrier': return 'pi pi-car';
        case 'processing_center': return 'pi pi-cog';
        case 'customer': return 'pi pi-user';
        case 'contractor': return 'pi pi-briefcase';
        default: return 'pi pi-circle';
    }
};

const getRoleColor = (role) => {
    switch (role) {
        case 'operator': return '#3b82f6';
        case 'carrier': return '#f59e0b';
        case 'processing_center': return '#10b981';
        case 'customer': return '#8b5cf6';
        case 'contractor': return '#ef4444';
        default: return '#6b7280';
    }
};

const getRoleLabel = (role) => {
    switch (role) {
        case 'operator': return 'Оператор';
        case 'carrier': return 'Перевозчик';
        case 'processing_center': return 'Процессинг';
        case 'customer': return 'Заказчик';
        case 'contractor': return 'Подрядчик';
        default: return 'Участник';
    }
};

const getRoleSeverity = (role) => {
    switch (role) {
        case 'operator': return 'info';
        case 'carrier': return 'warning';
        case 'processing_center': return 'success';
        case 'customer': return 'secondary';
        case 'contractor': return 'danger';
        default: return 'secondary';
    }
};
</script>

<template>
    <div class="project-contract">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Договор проекта</h2>
            <div class="flex gap-2" v-if="contract">
                <Button
                    label="Синхронизация с 1С"
                    icon="pi pi-refresh"
                    outlined
                    :loading="syncing"
                    @click="syncContract"
                />
                <Button
                    label="Редактировать"
                    icon="pi pi-pencil"
                    @click="editContract"
                />
            </div>
        </div>

        <!-- Загрузка -->
        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка договора проекта...</p>
            </div>
        </div>

        <!-- Нет договора -->
        <div v-else-if="!contract" class="card">
            <div class="text-center p-6">
                <i class="pi pi-file-edit text-6xl text-color-secondary mb-4"></i>
                <h3 class="text-xl font-semibold mb-3">Договор не найден</h3>
                <p class="text-color-secondary mb-4">
                    Для данного проекта не создан договор. Договор необходим для настройки способов оплаты и движения денежных средств.
                </p>
                <Button
                    label="Создать договор"
                    icon="pi pi-plus"
                    @click="createContract"
                />
            </div>
        </div>

        <!-- Информация о договоре -->
        <div v-else>
            <!-- Основная карточка договора -->
            <div class="card mb-4">
                <div class="flex align-items-start justify-content-between mb-4">
                    <div class="flex align-items-center">
                        <div class="w-4rem h-4rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-file-edit text-blue-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold m-0 mb-1">{{ contract.contractName }}</h3>
                            <p class="text-color-secondary m-0 mb-2">{{ getProjectTypeLabel(contract.projectType) }}</p>
                            <div class="flex align-items-center gap-2">
                                <Tag
                                    :value="getStatusLabel(contract.status)"
                                    :severity="getStatusSeverity(contract.status)"
                                />
                                <Tag
                                    :value="getSyncStatusLabel(contract.syncStatus)"
                                    :severity="getSyncStatusSeverity(contract.syncStatus)"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-primary mb-1">{{ formatAmount(contract.totalAmount) }}</div>
                        <div class="text-sm text-color-secondary">{{ contract.currency }}</div>
                    </div>
                </div>

                <Divider />

                <div class="grid">
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Номер договора</label>
                            <p class="m-0 font-mono text-lg">{{ contract.contractNumber }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Тип договора</label>
                            <p class="m-0">{{ getContractTypeLabel(contract.contractType) }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Идентификатор в 1С</label>
                            <p class="m-0 font-mono">{{ contract.externalId1C || 'Не синхронизирован' }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Дата заключения</label>
                            <p class="m-0">{{ formatDate(contract.conclusionDate) }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Дата подписи</label>
                            <p class="m-0">{{ contract.signatureDate ? formatDate(contract.signatureDate) : 'Не подписан' }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Дата завершения</label>
                            <p class="m-0">{{ formatDate(contract.completionDate) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Быстрые действия -->
            <div class="grid">
                <div class="col-12 md:col-4">
                    <div class="card quick-action-card cursor-pointer" @click="router.push(`/pro/${projectCode}/finance/payment-methods`)">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-green-100 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-credit-card text-green-600 text-xl"></i>
                            </div>
                            <div>
                                <h4 class="m-0 mb-1">Способы оплаты</h4>
                                <p class="m-0 text-sm text-color-secondary">Настройка методов оплаты проезда</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 md:col-4">
                    <div class="card quick-action-card cursor-pointer" @click="router.push(`/pro/${projectCode}/finance/cash-flow`)">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-purple-100 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-chart-line text-purple-600 text-xl"></i>
                            </div>
                            <div>
                                <h4 class="m-0 mb-1">Движение средств</h4>
                                <p class="m-0 text-sm text-color-secondary">Настройка движения денежных средств</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 md:col-4">
                    <div class="card quick-action-card cursor-pointer" @click="router.push(`/pro/${projectCode}/finance/reports`)">
                        <div class="flex align-items-center">
                            <div class="w-3rem h-3rem bg-orange-100 border-round flex align-items-center justify-content-center mr-3">
                                <i class="pi pi-chart-bar text-orange-600 text-xl"></i>
                            </div>
                            <div>
                                <h4 class="m-0 mb-1">Отчетность</h4>
                                <p class="m-0 text-sm text-color-secondary">Финансовые отчеты по договору</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Организации в договоре -->
            <div class="card mb-4" v-if="contract.contractOrganizations && contract.contractOrganizations.length > 0">
                <h4 class="text-lg font-semibold mb-4">Организации в договоре</h4>

                <div class="grid">
                    <div
                        v-for="org in contract.contractOrganizations"
                        :key="org.organizationId"
                        class="col-12 md:col-6 lg:col-4"
                    >
                        <div class="participant-card p-3 border-round border-1 border-200 h-full">
                            <div class="flex align-items-center mb-2">
                                <i :class="getRoleIcon(org.role)" :style="{ color: getRoleColor(org.role) }" class="mr-2"></i>
                                <span class="font-semibold">{{ org.organizationName }}</span>
                            </div>
                            <div class="text-sm mb-1">{{ org.roleDescription }}</div>
                            <Tag
                                :value="getRoleLabel(org.role)"
                                :severity="getRoleSeverity(org.role)"
                                class="text-xs"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Информация о синхронизации -->
            <div class="card" v-if="contract.lastSyncDate">
                <h4 class="text-lg font-semibold mb-3">Информация о синхронизации</h4>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Последняя синхронизация</label>
                            <p class="m-0">{{ formatDate(contract.lastSyncDate) }}</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold text-color-secondary text-sm">Статус синхронизации</label>
                            <div>
                                <Tag
                                    :value="getSyncStatusLabel(contract.syncStatus)"
                                    :severity="getSyncStatusSeverity(contract.syncStatus)"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.project-contract {
    height: 100%;
    padding: 1rem;
    overflow-y: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.quick-action-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.font-mono {
    font-family: 'Courier New', monospace;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.text-blue-600 {
    color: #2563eb;
}

.bg-green-100 {
    background-color: #dcfce7;
}

.text-green-600 {
    color: #16a34a;
}

.bg-purple-100 {
    background-color: #f3e8ff;
}

.text-purple-600 {
    color: #9333ea;
}

.bg-orange-100 {
    background-color: #ffedd5;
}

.text-orange-600 {
    color: #ea580c;
}

.participant-card {
    transition: all 0.3s ease;
    background: #fafafa;
}

.participant-card:hover {
    background: #f0f9ff;
    border-color: #3b82f6 !important;
}

.text-blue-600 {
    color: #2563eb;
}

.text-teal-600 {
    color: #0d9488;
}

.border-1 {
    border-width: 1px;
}

.border-200 {
    border-color: #e5e7eb;
}
</style>
