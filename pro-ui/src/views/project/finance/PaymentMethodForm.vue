<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { PaymentMethodService } from '@/service/PaymentMethodService';
import { ContractService } from '@/service/ContractService';

const route = useRoute();
const router = useRouter();

const projectCode = route.params.code;
const methodId = route.params.methodId;
const isEdit = computed(() => !!methodId);

const loading = ref(false);
const saving = ref(false);
const contract = ref(null);

// Форма данных
const form = ref({
    code: '',
    name: ''
});

// Валидация
const errors = ref({});

// Предустановленные варианты способов оплаты
const predefinedMethods = ref([
    { code: 'BANK_CARD', name: 'Банковская карта' },
    { code: 'CASH', name: 'Наличные денежные средства' },
    { code: 'TROIKA_SINGLE', name: 'Транспортная карта "Тройка" (разовые поездки)' },
    { code: 'TROIKA_SUBSCRIPTION', name: 'Транспортная карта "Тройка" (абонемент)' },
    { code: 'MPC_DISCOUNT', name: 'МПК Дисконт' },
    { code: 'MPC_SOCIAL', name: 'МПК Социальная карта' },
    { code: 'MPC_SCHOOL', name: 'МПК "Карта Школьника"' },
    { code: 'MPC_STUDENT_SINGLE', name: 'МПК "Карта Студента" (разовые поездки)' },
    { code: 'MPC_STUDENT_SUBSCRIPTION', name: 'МПК "Карта Студента" (абонемент)' },
    { code: 'TC_RESIDENT', name: 'ТК Карта жителя' },
    { code: 'MOBILE_BC', name: 'Мобильное приложение БК' },
    { code: 'MOBILE_VIRTUAL_TC', name: 'Мобильное приложение Виртуальная ТК' },
    { code: 'MOBILE_SBP', name: 'Мобильное приложение СБП' },
    { code: 'REGIONAL_TC', name: 'Транспортная карта региона' },
    { code: 'SOCIAL_TC', name: 'Социальная транспортная карта' },
    { code: 'OTHER_CARDS', name: 'Иные карты, предусмотренные договором' }
]);

onMounted(async () => {
    await loadProjectContract();
    if (isEdit.value && contract.value) {
        await loadPaymentMethod();
    }
});

const loadProjectContract = async () => {
    try {
        const contracts = await ContractService.getContractsByProject(projectCode);
        if (contracts && contracts.length > 0) {
            contract.value = contracts[0];
        }
    } catch (error) {
        console.error('Ошибка загрузки договора проекта:', error);
    }
};

const loadPaymentMethod = async () => {
    try {
        loading.value = true;
        const method = await PaymentMethodService.getPaymentMethodById(methodId);
        if (method) {
            form.value = { ...method };
        }
    } catch (error) {
        console.error('Ошибка загрузки способа оплаты:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    if (!form.value.code.trim()) {
        errors.value.code = 'Код способа оплаты обязателен для заполнения';
    }
    
    if (!form.value.name.trim()) {
        errors.value.name = 'Наименование способа оплаты обязательно для заполнения';
    }
    
    return Object.keys(errors.value).length === 0;
};

const savePaymentMethod = async () => {
    if (!validateForm()) {
        return;
    }
    
    if (!contract.value) {
        console.error('Договор не найден');
        return;
    }
    
    try {
        saving.value = true;
        
        if (isEdit.value) {
            await PaymentMethodService.updatePaymentMethod(methodId, {
                ...form.value,
                contractId: contract.value.id
            });
            console.log('Способ оплаты обновлен:', form.value);
        } else {
            await PaymentMethodService.createPaymentMethod(contract.value.id, form.value);
            console.log('Способ оплаты создан:', form.value);
        }
        
        router.push(`/pro/${projectCode}/finance/payment-methods`);
        
    } catch (error) {
        console.error('Ошибка сохранения способа оплаты:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectCode}/finance/payment-methods`);
};

const selectPredefinedMethod = (method) => {
    form.value.code = method.code;
    form.value.name = method.name;
    delete errors.value.code;
    delete errors.value.name;
};

const generateCode = () => {
    const name = form.value.name.trim();
    if (name) {
        // Простая генерация кода на основе названия
        const code = name
            .toUpperCase()
            .replace(/[^А-ЯA-Z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 20);
        form.value.code = code;
        delete errors.value.code;
    }
};
</script>

<template>
    <div class="payment-method-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование способа оплаты' : 'Создание способа оплаты' }}
            </h1>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>

        <!-- Информация о договоре -->
        <div class="card mb-4" v-if="contract">
            <div class="flex align-items-center">
                <div class="w-3rem h-3rem bg-blue-100 border-round flex align-items-center justify-content-center mr-3">
                    <i class="pi pi-file-edit text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold m-0 mb-1">{{ contract.name }}</h3>
                    <p class="text-color-secondary m-0">Договор № {{ contract.number }}</p>
                </div>
            </div>
        </div>
        
        <div class="grid">
            <!-- Форма -->
            <div class="col-12 lg:col-8">
                <div class="card" v-if="!loading">
                    <form @submit.prevent="savePaymentMethod">
                        <div class="grid">
                            <div class="col-12">
                                <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                            </div>
                            
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="code" class="font-medium">Код способа оплаты *</label>
                                    <div class="p-inputgroup">
                                        <InputText 
                                            id="code"
                                            v-model="form.code" 
                                            :class="{ 'p-invalid': errors.code }"
                                            placeholder="BANK_CARD"
                                            class="font-mono"
                                        />
                                        <Button 
                                            icon="pi pi-refresh" 
                                            @click="generateCode"
                                            v-tooltip.top="'Сгенерировать код'"
                                        />
                                    </div>
                                    <small v-if="errors.code" class="p-error">{{ errors.code }}</small>
                                    <small v-else class="text-color-secondary">Уникальный код для идентификации способа оплаты</small>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="field">
                                    <label for="name" class="font-medium">Наименование способа оплаты *</label>
                                    <InputText 
                                        id="name"
                                        v-model="form.name" 
                                        :class="{ 'p-invalid': errors.name }"
                                        placeholder="Банковская карта"
                                        class="w-full"
                                    />
                                    <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Кнопки действий -->
                        <div class="flex justify-content-end gap-2 mt-4">
                            <Button 
                                label="Отмена" 
                                icon="pi pi-times" 
                                outlined 
                                @click="cancel"
                                :disabled="saving"
                            />
                            <Button 
                                type="submit"
                                :label="isEdit ? 'Сохранить' : 'Создать'" 
                                :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                                :loading="saving"
                            />
                        </div>
                    </form>
                </div>
                
                <!-- Загрузка -->
                <div v-else class="card">
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных способа оплаты...</p>
                    </div>
                </div>
            </div>

            <!-- Предустановленные варианты -->
            <div class="col-12 lg:col-4" v-if="!isEdit">
                <div class="card">
                    <h3 class="text-lg font-medium mb-3">Стандартные способы оплаты</h3>
                    <p class="text-color-secondary text-sm mb-3">
                        Выберите один из предустановленных вариантов или создайте свой
                    </p>
                    
                    <div class="space-y-2">
                        <div 
                            v-for="method in predefinedMethods" 
                            :key="method.code"
                            class="predefined-method p-3 border-round cursor-pointer"
                            @click="selectPredefinedMethod(method)"
                        >
                            <div class="font-semibold text-sm">{{ method.name }}</div>
                            <div class="text-xs text-color-secondary font-mono">{{ method.code }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.payment-method-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}

.font-mono {
    font-family: 'Courier New', monospace;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.text-blue-600 {
    color: #2563eb;
}

.predefined-method {
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.predefined-method:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.space-y-2 > * + * {
    margin-top: 0.5rem;
}
</style>
