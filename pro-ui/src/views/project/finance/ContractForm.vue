<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { ContractService } from '@/service/ContractService';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();

const projectCode = route.params.code;
const contractId = route.params.contractId;
const isEdit = computed(() => !!contractId);

const loading = ref(false);
const saving = ref(false);
const organizations = ref([]);

// Форма данных
const form = ref({
    number: '',
    name: '',
    organizationId: null,
    organizationName: '',
    contractorName: 'ПАО Сбербанк',
    startDate: null,
    endDate: null,
    totalAmount: null,
    currency: 'RUB',
    status: 'draft',
    description: '',
    contractType: 'service',
    paymentTerms: 30,
    vatRate: 20
});

// Валидация
const errors = ref({});

const statusOptions = ref([
    { label: 'Черновик', value: 'draft' },
    { label: 'Активный', value: 'active' },
    { label: 'Истекает', value: 'expiring' },
    { label: 'Завершен', value: 'completed' },
    { label: 'Расторгнут', value: 'terminated' }
]);

const contractTypeOptions = ref([
    { label: 'Услуги', value: 'service' },
    { label: 'Разработка', value: 'development' },
    { label: 'Поставка', value: 'supply' },
    { label: 'Обслуживание', value: 'maintenance' }
]);

const currencyOptions = ref([
    { label: 'Российский рубль (RUB)', value: 'RUB' },
    { label: 'Доллар США (USD)', value: 'USD' },
    { label: 'Евро (EUR)', value: 'EUR' }
]);

onMounted(async () => {
    await loadOrganizations();
    if (isEdit.value) {
        await loadContract();
    }
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations();
        organizations.value = data.filter(org => org.status === 'active').map(org => ({
            label: org.shortName,
            value: org.id,
            fullName: org.name
        }));
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
    }
};

const loadContract = async () => {
    try {
        loading.value = true;
        const contract = await ContractService.getContractById(contractId);
        if (contract) {
            form.value = { ...contract };
            // Преобразуем даты в формат для input[type="date"]
            if (contract.startDate) {
                form.value.startDate = contract.startDate.split('T')[0];
            }
            if (contract.endDate) {
                form.value.endDate = contract.endDate.split('T')[0];
            }
        }
    } catch (error) {
        console.error('Ошибка загрузки договора:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    if (!form.value.number.trim()) {
        errors.value.number = 'Номер договора обязателен для заполнения';
    }
    
    if (!form.value.name.trim()) {
        errors.value.name = 'Наименование договора обязательно для заполнения';
    }
    
    if (!form.value.organizationId) {
        errors.value.organizationId = 'Организация обязательна для выбора';
    }
    
    if (!form.value.contractorName.trim()) {
        errors.value.contractorName = 'Контрагент обязателен для заполнения';
    }
    
    if (!form.value.startDate) {
        errors.value.startDate = 'Дата начала обязательна для заполнения';
    }
    
    if (!form.value.endDate) {
        errors.value.endDate = 'Дата окончания обязательна для заполнения';
    }
    
    if (form.value.startDate && form.value.endDate && form.value.startDate >= form.value.endDate) {
        errors.value.endDate = 'Дата окончания должна быть позже даты начала';
    }
    
    if (!form.value.totalAmount || form.value.totalAmount <= 0) {
        errors.value.totalAmount = 'Сумма договора должна быть больше 0';
    }
    
    if (!form.value.description.trim()) {
        errors.value.description = 'Описание договора обязательно для заполнения';
    }
    
    return Object.keys(errors.value).length === 0;
};

const saveContract = async () => {
    if (!validateForm()) {
        return;
    }
    
    try {
        saving.value = true;
        
        // Подготавливаем данные для сохранения
        const contractData = { ...form.value };
        
        // Преобразуем даты в ISO формат
        if (contractData.startDate) {
            contractData.startDate = new Date(contractData.startDate).toISOString();
        }
        if (contractData.endDate) {
            contractData.endDate = new Date(contractData.endDate).toISOString();
        }
        
        if (isEdit.value) {
            await ContractService.updateContract(contractId, contractData);
            console.log('Договор обновлен:', contractData);
        } else {
            await ContractService.createContract(projectCode, contractData);
            console.log('Договор создан:', contractData);
        }
        
        router.push(`/pro/${projectCode}/finance/contract`);
        
    } catch (error) {
        console.error('Ошибка сохранения договора:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectCode}/finance/contract`);
};

const onOrganizationChange = () => {
    const selectedOrg = organizations.value.find(org => org.value === form.value.organizationId);
    if (selectedOrg) {
        form.value.organizationName = selectedOrg.fullName;
    }
    delete errors.value.organizationId;
};

const generateContractNumber = () => {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const random = Math.floor(Math.random() * 999) + 1;
    form.value.number = `ДОГ-${year}-${String(random).padStart(3, '0')}`;
    delete errors.value.number;
};
</script>

<template>
    <div class="contract-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование договора' : 'Создание договора' }}
            </h1>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>
        
        <div class="card" v-if="!loading">
            <form @submit.prevent="saveContract">
                <div class="grid">
                    <!-- Основная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="number" class="font-medium">Номер договора *</label>
                            <div class="p-inputgroup">
                                <InputText 
                                    id="number"
                                    v-model="form.number" 
                                    :class="{ 'p-invalid': errors.number }"
                                    placeholder="ДОГ-2024-001"
                                    class="font-mono"
                                />
                                <Button 
                                    icon="pi pi-refresh" 
                                    @click="generateContractNumber"
                                    v-tooltip.top="'Сгенерировать номер'"
                                />
                            </div>
                            <small v-if="errors.number" class="p-error">{{ errors.number }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="contractType" class="font-medium">Тип договора</label>
                            <Dropdown 
                                id="contractType"
                                v-model="form.contractType" 
                                :options="contractTypeOptions" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите тип"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="status" class="font-medium">Статус</label>
                            <Dropdown 
                                id="status"
                                v-model="form.status" 
                                :options="statusOptions" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите статус"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <label for="name" class="font-medium">Наименование договора *</label>
                            <InputText 
                                id="name"
                                v-model="form.name" 
                                :class="{ 'p-invalid': errors.name }"
                                placeholder="Введите наименование договора"
                                class="w-full"
                            />
                            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <label for="description" class="font-medium">Описание договора *</label>
                            <Textarea 
                                id="description"
                                v-model="form.description" 
                                :class="{ 'p-invalid': errors.description }"
                                placeholder="Введите описание договора"
                                rows="3"
                                class="w-full"
                            />
                            <small v-if="errors.description" class="p-error">{{ errors.description }}</small>
                        </div>
                    </div>
                    
                    <!-- Стороны договора -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Стороны договора</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="organizationId" class="font-medium">Организация *</label>
                            <Dropdown 
                                id="organizationId"
                                v-model="form.organizationId" 
                                :options="organizations" 
                                optionLabel="label" 
                                optionValue="value"
                                :class="{ 'p-invalid': errors.organizationId }"
                                placeholder="Выберите организацию"
                                class="w-full"
                                @change="onOrganizationChange"
                            />
                            <small v-if="errors.organizationId" class="p-error">{{ errors.organizationId }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="contractorName" class="font-medium">Контрагент *</label>
                            <InputText 
                                id="contractorName"
                                v-model="form.contractorName" 
                                :class="{ 'p-invalid': errors.contractorName }"
                                placeholder="ПАО Сбербанк"
                                class="w-full"
                            />
                            <small v-if="errors.contractorName" class="p-error">{{ errors.contractorName }}</small>
                        </div>
                    </div>
                    
                    <!-- Финансовые условия -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Финансовые условия</h3>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="totalAmount" class="font-medium">Сумма договора *</label>
                            <InputNumber 
                                id="totalAmount"
                                v-model="form.totalAmount" 
                                :class="{ 'p-invalid': errors.totalAmount }"
                                :min="0"
                                :maxFractionDigits="2"
                                placeholder="0.00"
                                class="w-full"
                            />
                            <small v-if="errors.totalAmount" class="p-error">{{ errors.totalAmount }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="currency" class="font-medium">Валюта</label>
                            <Dropdown 
                                id="currency"
                                v-model="form.currency" 
                                :options="currencyOptions" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите валюту"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="vatRate" class="font-medium">Ставка НДС (%)</label>
                            <InputNumber 
                                id="vatRate"
                                v-model="form.vatRate" 
                                :min="0"
                                :max="100"
                                placeholder="20"
                                suffix="%"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <!-- Сроки -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Сроки</h3>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="startDate" class="font-medium">Дата начала *</label>
                            <Calendar 
                                id="startDate"
                                v-model="form.startDate" 
                                :class="{ 'p-invalid': errors.startDate }"
                                dateFormat="dd.mm.yy"
                                placeholder="Выберите дату"
                                class="w-full"
                                showIcon
                            />
                            <small v-if="errors.startDate" class="p-error">{{ errors.startDate }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="endDate" class="font-medium">Дата окончания *</label>
                            <Calendar 
                                id="endDate"
                                v-model="form.endDate" 
                                :class="{ 'p-invalid': errors.endDate }"
                                dateFormat="dd.mm.yy"
                                placeholder="Выберите дату"
                                class="w-full"
                                showIcon
                            />
                            <small v-if="errors.endDate" class="p-error">{{ errors.endDate }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="paymentTerms" class="font-medium">Срок оплаты (дней)</label>
                            <InputNumber 
                                id="paymentTerms"
                                v-model="form.paymentTerms" 
                                :min="1"
                                :max="365"
                                placeholder="30"
                                suffix=" дн."
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>
                
                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        label="Отмена" 
                        icon="pi pi-times" 
                        outlined 
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>
        
        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных договора...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.contract-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
