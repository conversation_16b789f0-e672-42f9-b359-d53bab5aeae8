<script setup>
import { useRoute } from 'vue-router';
import { ref } from 'vue';

const route = useRoute();
const projectCode = route.params.code;

const participants = ref([
    {
        id: 1,
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON> Ива<PERSON> Иван<PERSON>',
        role: 'Руководитель проекта',
        email: 'i<PERSON><PERSON>@company.com',
        phone: '+7 (999) 123-45-67',
        status: 'active'
    },
    {
        id: 2,
        name: 'Петров Петр Петрович',
        role: 'Аналитик',
        email: '<EMAIL>',
        phone: '+7 (999) 234-56-78',
        status: 'active'
    },
    {
        id: 3,
        name: 'Сидор<PERSON> Сидор Сидорович',
        role: 'Разработчик',
        email: '<EMAIL>',
        phone: '+7 (999) 345-67-89',
        status: 'inactive'
    }
]);

const getStatusSeverity = (status) => {
    return status === 'active' ? 'success' : 'secondary';
};

const getStatusLabel = (status) => {
    return status === 'active' ? 'Активен' : 'Неактивен';
};
</script>

<template>
    <div class="project-participants">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold">Участники проекта</h2>
            <Button label="Добавить участника" icon="pi pi-user-plus" />
        </div>
        
        <div class="grid">
            <div v-for="participant in participants" :key="participant.id" class="col-12 md:col-6 lg:col-4">
                <Card class="h-full">
                    <template #content>
                        <div class="text-center mb-3">
                            <Avatar 
                                :label="participant.name.split(' ').map(n => n[0]).join('')" 
                                size="large" 
                                shape="circle" 
                                class="mb-2"
                            />
                            <h4 class="m-0">{{ participant.name }}</h4>
                            <p class="text-color-secondary m-0">{{ participant.role }}</p>
                        </div>
                        
                        <div class="field">
                            <label class="font-medium">Email:</label>
                            <p class="m-0">{{ participant.email }}</p>
                        </div>
                        
                        <div class="field">
                            <label class="font-medium">Телефон:</label>
                            <p class="m-0">{{ participant.phone }}</p>
                        </div>
                        
                        <div class="field">
                            <label class="font-medium">Статус:</label>
                            <div>
                                <Tag 
                                    :value="getStatusLabel(participant.status)" 
                                    :severity="getStatusSeverity(participant.status)" 
                                />
                            </div>
                        </div>
                        
                        <div class="flex gap-2 mt-3">
                            <Button icon="pi pi-pencil" size="small" text />
                            <Button icon="pi pi-trash" size="small" text severity="danger" />
                        </div>
                    </template>
                </Card>
            </div>
        </div>
    </div>
</template>

<style scoped>
.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.field p {
    font-size: 0.875rem;
}
</style>
