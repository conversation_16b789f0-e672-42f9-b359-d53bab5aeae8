<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { ProcessingService } from '@/service/ProcessingService';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;

const payments = ref([]);
const loading = ref(true);
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    transactionId: { value: null, matchMode: FilterMatchMode.CONTAINS },
    paymentType: { value: null, matchMode: FilterMatchMode.EQUALS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS },
    route: { value: null, matchMode: FilterMatchMode.CONTAINS },
    station: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const paymentTypeOptions = [
    { label: 'Все типы', value: null },
    { label: 'Наличные', value: 'cash' },
    { label: 'Банковские карты', value: 'emv' },
    { label: 'Тройка', value: 'troika' }
];

const statusOptions = [
    { label: 'Все статусы', value: null },
    { label: 'Завершен', value: 'completed' },
    { label: 'Обработка', value: 'processing' },
    { label: 'Ошибка', value: 'error' }
];

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        transactionId: { value: null, matchMode: FilterMatchMode.CONTAINS },
        paymentType: { value: null, matchMode: FilterMatchMode.EQUALS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS },
        route: { value: null, matchMode: FilterMatchMode.CONTAINS },
        station: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};

onMounted(() => {
    loadPayments();
});

const loadPayments = async () => {
    try {
        loading.value = true;
        const data = await ProcessingService.getProjectPayments(projectId);
        payments.value = data;
    } catch (error) {
        console.error('Ошибка загрузки платежей:', error);
        payments.value = [];
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const refreshPayments = () => {
    loadPayments();
};

const exportData = () => {
    console.log('Экспорт платежей проекта:', projectId);
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount);
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'completed': return 'success';
        case 'processing': return 'warning';
        case 'error': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'completed': return 'Завершен';
        case 'processing': return 'Обработка';
        case 'error': return 'Ошибка';
        default: return status;
    }
};

const getPaymentTypeLabel = (type) => {
    switch (type) {
        case 'cash': return 'Наличные';
        case 'emv': return 'Банковская карта';
        case 'troika': return 'Тройка';
        default: return type;
    }
};

const getPaymentTypeColor = (type) => {
    switch (type) {
        case 'cash': return 'success';
        case 'emv': return 'info';
        case 'troika': return 'warning';
        default: return 'secondary';
    }
};

const getDelayStatus = (operationDate, receivedDate) => {
    const delay = new Date(receivedDate) - new Date(operationDate);
    const delayMinutes = Math.floor(delay / (1000 * 60));

    if (delayMinutes <= 5) return { severity: 'success', label: 'В срок' };
    if (delayMinutes <= 30) return { severity: 'warning', label: `+${delayMinutes}м` };
    return { severity: 'danger', label: `+${delayMinutes}м` };
};

const viewPaymentDetails = (payment) => {
    // Здесь можно открыть модальное окно с деталями платежа
    console.log('Детали платежа:', payment);
};

const getTariffSeverity = (tariffType) => {
    switch (tariffType) {
        case 'льготный': return 'success';
        case 'абонемент': return 'info';
        case 'пересадочный': return 'warning';
        case 'разовый': return 'secondary';
        default: return 'secondary';
    }
};

const formatTime = (dateString) => {
    if (!dateString) return '—';
    return new Date(dateString).toLocaleTimeString('ru-RU', {
        hour: '2-digit',
        minute: '2-digit'
    });
};
</script>

<template>
    <div class="payment-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="text-xl font-semibold m-0">Платежи проекта</h2>
                <p class="text-sm text-color-secondary mt-1">
                    Агрегированный вид всех платежей проекта из систем CASH и EMV.
                    Первичные данные поступают через TMS с терминального оборудования.
                </p>
            </div>
            <div class="flex gap-2">
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-refresh"
                        label="Обновить"
                        outlined
                        @click="refreshPayments"
                    />
                </div>
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="payments"
                :paginator="true"
                :rows="20"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['transactionId', 'route', 'station', 'terminalId', 'carrier', 'driver', 'licensePlate', 'boardNumber']"
                showGridlines
                responsiveLayout="scroll"
                sortField="operationDate"
                :sortOrder="-1"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Платежи не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="transactionId" header="ID транзакции" :sortable="true" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="font-mono text-sm">{{ data.transactionId }}</div>
                    </template>
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по ID"
                        />
                    </template>
                </Column>

                <Column field="paymentType" header="Тип платежа" :sortable="true" style="min-width: 140px">
                    <template #body="{ data }">
                        <Tag
                            :value="getPaymentTypeLabel(data.paymentType)"
                            :severity="getPaymentTypeColor(data.paymentType)"
                        />
                    </template>
                    <template #filter="{ filterModel }">
                        <Dropdown
                            v-model="filterModel.value"
                            :options="paymentTypeOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Выберите тип"
                            showClear
                        />
                    </template>
                </Column>

                <Column field="amount" header="Сумма" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="font-semibold">{{ formatAmount(data.amount) }}</div>
                        <div class="text-xs text-color-secondary">{{ data.ticketCount }} билет(ов)</div>
                    </template>
                </Column>

                <Column field="operationDate" header="Дата операции" :sortable="true" style="min-width: 160px">
                    <template #body="{ data }">
                        <div>{{ formatDate(data.operationDate) }}</div>
                    </template>
                </Column>

                <Column header="Задержка получения" style="min-width: 140px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <Tag
                                :value="getDelayStatus(data.operationDate, data.receivedDate).label"
                                :severity="getDelayStatus(data.operationDate, data.receivedDate).severity"
                                size="small"
                            />
                        </div>
                    </template>
                </Column>

                <Column field="route" header="Маршрут" :sortable="true" style="min-width: 100px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по маршруту"
                        />
                    </template>
                </Column>

                <Column field="station" header="Остановка" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по остановке"
                        />
                    </template>
                </Column>

                <Column field="vehicleType" header="Тип ТС" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <div>
                            <div class="font-medium">{{ data.vehicleType }}</div>
                            <div class="text-xs text-color-secondary">{{ data.licensePlate }}</div>
                        </div>
                    </template>
                </Column>

                <Column field="boardNumber" header="Борт. номер" :sortable="true" style="min-width: 100px">
                    <template #body="{ data }">
                        <span class="font-mono">{{ data.boardNumber }}</span>
                    </template>
                </Column>

                <Column field="carrier" header="Перевозчик" :sortable="true" style="min-width: 180px">
                </Column>

                <Column field="driver" header="Водитель" :sortable="true" style="min-width: 150px">
                </Column>

                <Column field="tariffType" header="Тип тарифа" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <Tag
                            :value="data.tariffType"
                            :severity="getTariffSeverity(data.tariffType)"
                            size="small"
                        />
                    </template>
                </Column>

                <Column field="direction" header="Направление" :sortable="true" style="min-width: 100px">
                </Column>

                <Column header="Вход/Выход" style="min-width: 200px">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div><strong>Вход:</strong> {{ data.entryStation }}</div>
                            <div class="text-xs text-color-secondary">{{ formatTime(data.entryTime) }}</div>
                            <div v-if="data.exitStation" class="mt-1">
                                <strong>Выход:</strong> {{ data.exitStation }}
                                <div class="text-xs text-color-secondary">{{ formatTime(data.exitTime) }}</div>
                            </div>
                        </div>
                    </template>
                </Column>

                <Column field="tripNumber" header="Номер рейса" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <span class="font-mono">{{ data.tripNumber }}</span>
                    </template>
                </Column>

                <Column header="Средство оплаты" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div v-if="data.troikaUid">
                                <strong>UID Тройки:</strong>
                                <div class="font-mono text-xs">{{ data.troikaUid }}</div>
                            </div>
                            <div v-else-if="data.cardNumber">
                                <strong>Номер карты:</strong>
                                <div class="font-mono text-xs">{{ data.cardNumber }}</div>
                            </div>
                            <div v-else>
                                <span class="text-color-secondary">Наличные</span>
                            </div>
                        </div>
                    </template>
                </Column>

                <Column field="subscription" header="Абонемент" style="min-width: 150px">
                    <template #body="{ data }">
                        <div v-if="data.subscription" class="text-sm">
                            <Tag :value="data.subscription.type" severity="info" size="small" />
                            <div class="text-xs text-color-secondary mt-1">
                                Поездок: {{ data.subscription.ridesLeft }}
                            </div>
                        </div>
                        <span v-else class="text-color-secondary">—</span>
                    </template>
                </Column>

                <Column field="status" header="Статус" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <Tag
                            :value="getStatusLabel(data.status)"
                            :severity="getStatusSeverity(data.status)"
                        />
                    </template>
                    <template #filter="{ filterModel }">
                        <Dropdown
                            v-model="filterModel.value"
                            :options="statusOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Выберите статус"
                            showClear
                        />
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button
                                icon="pi pi-eye"
                                size="small"
                                text
                                @click="viewPaymentDetails(data)"
                                v-tooltip.top="'Детали платежа'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.payment-list {
    padding: 0;
}

.table-card {
    background: var(--surface-card);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12);
}
</style>
