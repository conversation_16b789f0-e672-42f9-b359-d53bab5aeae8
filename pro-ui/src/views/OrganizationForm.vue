<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();

const organizationId = route.params.organizationId;
const isEdit = computed(() => !!organizationId);

const loading = ref(false);
const saving = ref(false);

// Форма данных
const form = ref({
    name: '',
    shortName: '',
    inn: '',
    kpp: '',
    ogrn: '',
    address: '',
    phone: '',
    email: '',
    director: '',
    status: 'active'
});

// Валидация
const errors = ref({});

const statusOptions = ref([
    { label: 'Активная', value: 'active' },
    { label: 'Неактивная', value: 'inactive' }
]);

onMounted(() => {
    if (isEdit.value) {
        loadOrganization();
    }
});

const loadOrganization = async () => {
    try {
        loading.value = true;
        const organization = await OrganizationService.getOrganizationById(organizationId);
        if (organization) {
            form.value = { ...organization };
        }
    } catch (error) {
        console.error('Ошибка загрузки организации:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    if (!form.value.name.trim()) {
        errors.value.name = 'Наименование обязательно для заполнения';
    }
    
    if (!form.value.shortName.trim()) {
        errors.value.shortName = 'Краткое наименование обязательно для заполнения';
    }
    
    if (!form.value.inn.trim()) {
        errors.value.inn = 'ИНН обязателен для заполнения';
    } else if (!/^\d{10}$|^\d{12}$/.test(form.value.inn)) {
        errors.value.inn = 'ИНН должен содержать 10 или 12 цифр';
    }
    
    if (form.value.kpp && !/^\d{9}$/.test(form.value.kpp)) {
        errors.value.kpp = 'КПП должен содержать 9 цифр';
    }
    
    if (!form.value.ogrn.trim()) {
        errors.value.ogrn = 'ОГРН обязателен для заполнения';
    } else if (!/^\d{13}$|^\d{15}$/.test(form.value.ogrn)) {
        errors.value.ogrn = 'ОГРН должен содержать 13 или 15 цифр';
    }
    
    if (!form.value.address.trim()) {
        errors.value.address = 'Адрес обязателен для заполнения';
    }
    
    if (!form.value.director.trim()) {
        errors.value.director = 'ФИО руководителя обязательно для заполнения';
    }
    
    if (form.value.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
        errors.value.email = 'Некорректный формат email';
    }
    
    return Object.keys(errors.value).length === 0;
};

const saveOrganization = async () => {
    if (!validateForm()) {
        return;
    }
    
    try {
        saving.value = true;
        
        if (isEdit.value) {
            await OrganizationService.updateOrganization(organizationId, form.value);
            console.log('Организация обновлена:', form.value);
        } else {
            await OrganizationService.createOrganization(form.value);
            console.log('Организация создана:', form.value);
        }
        
        router.push('/organizations');
        
    } catch (error) {
        console.error('Ошибка сохранения организации:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push('/organizations');
};

// Форматирование ИНН
const onInnInput = (event) => {
    const cleaned = event.target.value.replace(/\D/g, '').substring(0, 12);
    form.value.inn = cleaned;
    event.target.value = cleaned;
};

// Форматирование КПП
const onKppInput = (event) => {
    const cleaned = event.target.value.replace(/\D/g, '').substring(0, 9);
    form.value.kpp = cleaned;
    event.target.value = cleaned;
};

// Форматирование ОГРН
const onOgrnInput = (event) => {
    const cleaned = event.target.value.replace(/\D/g, '').substring(0, 15);
    form.value.ogrn = cleaned;
    event.target.value = cleaned;
};

// Форматирование телефона
const formatPhone = (value) => {
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length === 0) return '';
    if (cleaned.length <= 1) return `+${cleaned}`;
    if (cleaned.length <= 4) return `+${cleaned.slice(0, 1)} (${cleaned.slice(1)}`;
    if (cleaned.length <= 7) return `+${cleaned.slice(0, 1)} (${cleaned.slice(1, 4)}) ${cleaned.slice(4)}`;
    if (cleaned.length <= 9) return `+${cleaned.slice(0, 1)} (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    return `+${cleaned.slice(0, 1)} (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7, 9)}-${cleaned.slice(9, 11)}`;
};

const onPhoneInput = (event) => {
    const formatted = formatPhone(event.target.value);
    form.value.phone = formatted;
    event.target.value = formatted;
};
</script>

<template>
    <div class="organization-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование организации' : 'Создание организации' }}
            </h1>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>
        
        <div class="card" v-if="!loading">
            <form @submit.prevent="saveOrganization">
                <div class="grid">
                    <!-- Основная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <label for="name" class="font-medium">Полное наименование *</label>
                            <InputText 
                                id="name"
                                v-model="form.name" 
                                :class="{ 'p-invalid': errors.name }"
                                placeholder="Например: ООО 'Городской транспорт'"
                                class="w-full"
                            />
                            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="shortName" class="font-medium">Краткое наименование *</label>
                            <InputText 
                                id="shortName"
                                v-model="form.shortName" 
                                :class="{ 'p-invalid': errors.shortName }"
                                placeholder="Например: Городской транспорт"
                                class="w-full"
                            />
                            <small v-if="errors.shortName" class="p-error">{{ errors.shortName }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="status" class="font-medium">Статус</label>
                            <Dropdown 
                                id="status"
                                v-model="form.status" 
                                :options="statusOptions" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите статус"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <!-- Реквизиты -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Реквизиты</h3>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="inn" class="font-medium">ИНН *</label>
                            <InputText 
                                id="inn"
                                v-model="form.inn" 
                                :class="{ 'p-invalid': errors.inn }"
                                placeholder="10 или 12 цифр"
                                maxlength="12"
                                @input="onInnInput"
                                class="w-full font-mono"
                            />
                            <small v-if="errors.inn" class="p-error">{{ errors.inn }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="kpp" class="font-medium">КПП</label>
                            <InputText 
                                id="kpp"
                                v-model="form.kpp" 
                                :class="{ 'p-invalid': errors.kpp }"
                                placeholder="9 цифр"
                                maxlength="9"
                                @input="onKppInput"
                                class="w-full font-mono"
                            />
                            <small v-if="errors.kpp" class="p-error">{{ errors.kpp }}</small>
                            <small v-else class="text-color-secondary">Для ИП не заполняется</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="ogrn" class="font-medium">ОГРН *</label>
                            <InputText 
                                id="ogrn"
                                v-model="form.ogrn" 
                                :class="{ 'p-invalid': errors.ogrn }"
                                placeholder="13 или 15 цифр"
                                maxlength="15"
                                @input="onOgrnInput"
                                class="w-full font-mono"
                            />
                            <small v-if="errors.ogrn" class="p-error">{{ errors.ogrn }}</small>
                        </div>
                    </div>
                    
                    <!-- Контактная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Контактная информация</h3>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <label for="address" class="font-medium">Адрес *</label>
                            <Textarea 
                                id="address"
                                v-model="form.address" 
                                :class="{ 'p-invalid': errors.address }"
                                placeholder="Введите полный адрес организации"
                                rows="2"
                                class="w-full"
                            />
                            <small v-if="errors.address" class="p-error">{{ errors.address }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="phone" class="font-medium">Телефон</label>
                            <InputText 
                                id="phone"
                                v-model="form.phone" 
                                placeholder="+7 (999) 123-45-67"
                                @input="onPhoneInput"
                                class="w-full font-mono"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="email" class="font-medium">Email</label>
                            <InputText 
                                id="email"
                                v-model="form.email" 
                                :class="{ 'p-invalid': errors.email }"
                                placeholder="<EMAIL>"
                                type="email"
                                class="w-full"
                            />
                            <small v-if="errors.email" class="p-error">{{ errors.email }}</small>
                        </div>
                    </div>
                    
                    <!-- Руководство -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Руководство</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="director" class="font-medium">ФИО руководителя *</label>
                            <InputText 
                                id="director"
                                v-model="form.director" 
                                :class="{ 'p-invalid': errors.director }"
                                placeholder="Иванов Иван Иванович"
                                class="w-full"
                            />
                            <small v-if="errors.director" class="p-error">{{ errors.director }}</small>
                        </div>
                    </div>
                </div>
                
                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        label="Отмена" 
                        icon="pi pi-times" 
                        outlined 
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>
        
        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных организации...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.organization-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
