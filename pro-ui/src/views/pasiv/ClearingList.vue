<script setup>
import { ref, onMounted } from 'vue';

const clearingOperations = ref([]);
const loading = ref(true);

const mockData = [
    {
        id: 1,
        operationDate: '2024-01-20T00:00:00Z',
        operationType: 'daily_clearing',
        status: 'completed',
        totalAmount: 15750000.00,
        transactionCount: 125000,
        organizationCount: 5,
        processingTime: '00:15:32',
        startTime: '2024-01-20T23:00:00Z',
        endTime: '2024-01-20T23:15:32Z',
        reportUrl: '/reports/clearing_2024-01-20.pdf'
    },
    {
        id: 2,
        operationDate: '2024-01-19T00:00:00Z',
        operationType: 'daily_clearing',
        status: 'completed',
        totalAmount: 14250000.00,
        transactionCount: 118000,
        organizationCount: 5,
        processingTime: '00:12:45',
        startTime: '2024-01-19T23:00:00Z',
        endTime: '2024-01-19T23:12:45Z',
        reportUrl: '/reports/clearing_2024-01-19.pdf'
    },
    {
        id: 3,
        operationDate: '2024-01-18T00:00:00Z',
        operationType: 'weekly_clearing',
        status: 'completed',
        totalAmount: 98500000.00,
        transactionCount: 875000,
        organizationCount: 5,
        processingTime: '01:25:18',
        startTime: '2024-01-18T22:00:00Z',
        endTime: '2024-01-18T23:25:18Z',
        reportUrl: '/reports/clearing_weekly_2024-01-18.pdf'
    },
    {
        id: 4,
        operationDate: '2024-01-21T00:00:00Z',
        operationType: 'daily_clearing',
        status: 'processing',
        totalAmount: 0,
        transactionCount: 0,
        organizationCount: 0,
        processingTime: null,
        startTime: '2024-01-21T23:00:00Z',
        endTime: null,
        reportUrl: null
    }
];

onMounted(() => {
    setTimeout(() => {
        clearingOperations.value = mockData;
        loading.value = false;
    }, 1000);
});

const startClearing = () => {
    console.log('Запуск клиринга...');
    // Здесь будет логика запуска клиринга
};

const viewReport = (operation) => {
    if (operation.reportUrl) {
        console.log('Открытие отчета:', operation.reportUrl);
        // Здесь будет логика открытия отчета
    }
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const formatNumber = (number) => {
    return new Intl.NumberFormat('ru-RU').format(number);
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatTime = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleTimeString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'completed': return 'success';
        case 'processing': return 'warning';
        case 'error': return 'danger';
        case 'scheduled': return 'info';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'completed': return 'Завершен';
        case 'processing': return 'Выполняется';
        case 'error': return 'Ошибка';
        case 'scheduled': return 'Запланирован';
        default: return status;
    }
};

const getOperationTypeLabel = (type) => {
    switch (type) {
        case 'daily_clearing': return 'Ежедневный клиринг';
        case 'weekly_clearing': return 'Недельный клиринг';
        case 'monthly_clearing': return 'Месячный клиринг';
        case 'manual_clearing': return 'Ручной клиринг';
        default: return type;
    }
};
</script>

<template>
    <div class="clearing-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Клиринг</h1>
                <p class="text-color-secondary m-0">Операции клиринга и взаиморасчетов</p>
            </div>
            <div class="flex gap-2">
                <Button 
                    label="Настройки клиринга" 
                    icon="pi pi-cog" 
                    outlined
                />
                <Button 
                    label="Запустить клиринг" 
                    icon="pi pi-play" 
                    @click="startClearing"
                />
            </div>
        </div>

        <!-- Статистика -->
        <div class="grid mb-4">
            <div class="col-12 md:col-3">
                <div class="card bg-blue-50 border-blue-200">
                    <div class="flex align-items-center">
                        <div class="w-3rem h-3rem bg-blue-500 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-clock text-white text-xl"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-blue-900">23:00</div>
                            <div class="text-blue-700">Время клиринга</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 md:col-3">
                <div class="card bg-green-50 border-green-200">
                    <div class="flex align-items-center">
                        <div class="w-3rem h-3rem bg-green-500 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-check-circle text-white text-xl"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-green-900">125K</div>
                            <div class="text-green-700">Транзакций сегодня</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 md:col-3">
                <div class="card bg-orange-50 border-orange-200">
                    <div class="flex align-items-center">
                        <div class="w-3rem h-3rem bg-orange-500 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-wallet text-white text-xl"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-orange-900">15.7M ₽</div>
                            <div class="text-orange-700">Сумма к клирингу</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 md:col-3">
                <div class="card bg-purple-50 border-purple-200">
                    <div class="flex align-items-center">
                        <div class="w-3rem h-3rem bg-purple-500 border-round flex align-items-center justify-content-center mr-3">
                            <i class="pi pi-building text-white text-xl"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-purple-900">5</div>
                            <div class="text-purple-700">Организаций</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3 class="text-lg font-semibold mb-4">История операций клиринга</h3>
            
            <DataTable 
                :value="clearingOperations" 
                :loading="loading" 
                :paginator="true" 
                :rows="15" 
                responsiveLayout="scroll"
            >
                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>
                
                <Column field="operationDate" header="Дата" :sortable="true">
                    <template #body="{ data }">
                        {{ formatDate(data.operationDate) }}
                    </template>
                </Column>
                
                <Column field="operationType" header="Тип операции" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getOperationTypeLabel(data.operationType)" 
                            severity="info"
                        />
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column field="totalAmount" header="Сумма" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ data.totalAmount > 0 ? formatAmount(data.totalAmount) : '-' }}
                        </div>
                    </template>
                </Column>
                
                <Column field="transactionCount" header="Транзакций" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right">
                            {{ data.transactionCount > 0 ? formatNumber(data.transactionCount) : '-' }}
                        </div>
                    </template>
                </Column>
                
                <Column field="organizationCount" header="Организаций" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-center">
                            {{ data.organizationCount > 0 ? data.organizationCount : '-' }}
                        </div>
                    </template>
                </Column>
                
                <Column header="Время выполнения">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div v-if="data.startTime">Начало: {{ formatTime(data.startTime) }}</div>
                            <div v-if="data.endTime">Окончание: {{ formatTime(data.endTime) }}</div>
                            <div v-if="data.processingTime" class="font-semibold">{{ data.processingTime }}</div>
                            <div v-else-if="data.status === 'processing'" class="text-orange-600">Выполняется...</div>
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                v-if="data.reportUrl"
                                icon="pi pi-file-pdf" 
                                size="small" 
                                text 
                                @click="viewReport(data)"
                                v-tooltip.top="'Скачать отчет'"
                            />
                            <Button 
                                v-if="data.status === 'error'"
                                icon="pi pi-refresh" 
                                size="small" 
                                text 
                                severity="warning"
                                v-tooltip.top="'Повторить'"
                            />
                            <Button 
                                icon="pi pi-eye" 
                                size="small" 
                                text 
                                v-tooltip.top="'Детали'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.clearing-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.bg-blue-50 { background-color: #eff6ff; }
.bg-green-50 { background-color: #f0fdf4; }
.bg-orange-50 { background-color: #fff7ed; }
.bg-purple-50 { background-color: #faf5ff; }

.border-blue-200 { border-color: #bfdbfe; }
.border-green-200 { border-color: #bbf7d0; }
.border-orange-200 { border-color: #fed7aa; }
.border-purple-200 { border-color: #e9d5ff; }

.text-blue-900 { color: #1e3a8a; }
.text-green-900 { color: #14532d; }
.text-orange-900 { color: #9a3412; }
.text-purple-900 { color: #581c87; }

.text-blue-700 { color: #1d4ed8; }
.text-green-700 { color: #15803d; }
.text-orange-700 { color: #c2410c; }
.text-purple-700 { color: #7c3aed; }
</style>
