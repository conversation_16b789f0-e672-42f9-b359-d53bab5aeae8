<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ServiceService } from '@/service/ServiceService';

const router = useRouter();
const services = ref([]);
const loading = ref(true);
const selectedService = ref(null);

// Фильтры
const filters = ref({
    search: '',
    subscriptionType: null,
    isSocial: null
});

// Опции для фильтров
const subscriptionTypeOptions = [
    { label: 'Все типы', value: null },
    { label: 'Кошелек', value: 'WALLET' },
    { label: 'Поездочный', value: 'TRAVEL' },
    { label: 'Безлимитный', value: 'UNLIMITED' }
];

const socialOptions = [
    { label: 'Все', value: null },
    { label: 'Социальные', value: true },
    { label: 'Обычные', value: false }
];

onMounted(async () => {
    try {
        const data = await ServiceService.getServices();
        services.value = data;
    } catch (error) {
        console.error('Ошибка загрузки услуг:', error);
    } finally {
        loading.value = false;
    }
});

// Фильтрованные данные
const filteredServices = computed(() => {
    let filtered = services.value;

    if (filters.value.search) {
        const search = filters.value.search.toLowerCase();
        filtered = filtered.filter(service => 
            service.name.toLowerCase().includes(search) ||
            service.serviceCode.toLowerCase().includes(search) ||
            (service.description && service.description.toLowerCase().includes(search))
        );
    }

    if (filters.value.subscriptionType) {
        filtered = filtered.filter(service => service.subscriptionType === filters.value.subscriptionType);
    }

    if (filters.value.isSocial !== null) {
        filtered = filtered.filter(service => service.isSocial === filters.value.isSocial);
    }

    return filtered;
});

const getSubscriptionTypeLabel = (type) => {
    switch (type) {
        case 'WALLET': return 'Кошелек';
        case 'TRAVEL': return 'Поездочный';
        case 'UNLIMITED': return 'Безлимитный';
        default: return type;
    }
};

const getSubscriptionTypeSeverity = (type) => {
    switch (type) {
        case 'WALLET': return 'info';
        case 'TRAVEL': return 'success';
        case 'UNLIMITED': return 'warning';
        default: return 'secondary';
    }
};

const formatCurrency = (amount) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount / 100); // Конвертируем из копеек в рубли
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const clearFilters = () => {
    filters.value = {
        search: '',
        subscriptionType: null,
        isSocial: null
    };
};

const createService = () => {
    router.push('/abt/services/create');
};

const editService = (service) => {
    router.push(`/abt/services/${service.id}/edit`);
};

const viewService = (service) => {
    router.push(`/abt/services/${service.id}`);
};

const deleteService = async (service) => {
    try {
        await ServiceService.deleteService(service.id);
        // Обновляем список после удаления
        const data = await ServiceService.getServices();
        services.value = data;
    } catch (error) {
        console.error('Ошибка удаления услуги:', error);
    }
};
</script>

<template>
    <div class="service-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Услуги</h1>
                <p class="text-color-secondary m-0">Управление услугами и товарами</p>
            </div>
            <Button 
                label="Создать услугу" 
                icon="pi pi-plus" 
                @click="createService"
                class="p-button-success"
            />
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3>Фильтры</h3>
            <div class="grid">
                <div class="col-12 md:col-4">
                    <label class="font-semibold">Поиск</label>
                    <InputText 
                        v-model="filters.search" 
                        placeholder="Название, код, описание..."
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-4">
                    <label class="font-semibold">Тип подписки</label>
                    <Dropdown 
                        v-model="filters.subscriptionType" 
                        :options="subscriptionTypeOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите тип"
                        class="w-full"
                    />
                </div>
                
                <div class="col-12 md:col-4">
                    <label class="font-semibold">Категория</label>
                    <Dropdown 
                        v-model="filters.isSocial" 
                        :options="socialOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите категорию"
                        class="w-full"
                    />
                </div>
            </div>
            
            <div class="flex justify-content-end mt-3">
                <Button 
                    label="Очистить фильтры" 
                    icon="pi pi-times" 
                    @click="clearFilters"
                    class="p-button-secondary"
                />
            </div>
        </div>

        <!-- Таблица -->
        <div class="card">
            <DataTable 
                :value="filteredServices" 
                :loading="loading" 
                :paginator="true" 
                :rows="15" 
                responsiveLayout="scroll"
                v-model:selection="selectedService"
                selectionMode="single"
                dataKey="id"
            >
                <Column field="serviceCode" header="Код услуги" :sortable="true">
                    <template #body="{ data }">
                        <div class="font-semibold cursor-pointer" @click="viewService(data)">
                            {{ data.serviceCode }}
                        </div>
                    </template>
                </Column>
                
                <Column field="name" header="Название" :sortable="true">
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.name }}</div>
                            <div v-if="data.description" class="text-sm text-color-secondary">
                                {{ data.description }}
                            </div>
                        </div>
                    </template>
                </Column>
                
                <Column field="subscriptionType" header="Тип" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getSubscriptionTypeLabel(data.subscriptionType)" 
                            :severity="getSubscriptionTypeSeverity(data.subscriptionType)" 
                        />
                    </template>
                </Column>
                
                <Column field="isSocial" header="Социальный" :sortable="true">
                    <template #body="{ data }">
                        <i v-if="data.isSocial" class="pi pi-check text-green-500"></i>
                        <i v-else class="pi pi-times text-red-500"></i>
                    </template>
                </Column>
                
                <Column field="templateId" header="Шаблон" :sortable="true">
                    <template #body="{ data }">
                        <div v-if="data.templateId" class="text-sm">
                            <div class="font-semibold">{{ data.templateId }}</div>
                            <div class="text-color-secondary">ID шаблона</div>
                        </div>
                        <div v-else class="text-color-secondary">
                            Не указан
                        </div>
                    </template>
                </Column>
                
                <Column field="cost" header="Стоимость" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right">
                            {{ formatCurrency(data.cost) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="actionStartDate" header="Период действия" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div v-if="data.actionStartDate && data.actionEndDate">
                                <div>С: {{ formatDate(data.actionStartDate) }}</div>
                                <div>По: {{ formatDate(data.actionEndDate) }}</div>
                            </div>
                            <div v-else class="text-color-secondary">
                                Не ограничен
                            </div>
                        </div>
                    </template>
                </Column>
                
                <Column field="minReplenishmentAmount" header="Пополнение" :sortable="true">
                    <template #body="{ data }">
                        <div v-if="data.subscriptionType === 'WALLET'" class="text-sm">
                            <div>Мин: {{ formatCurrency(data.minReplenishmentAmount) }}</div>
                            <div>Макс: {{ formatCurrency(data.maxReplenishmentAmount) }}</div>
                            <div v-if="data.recommendedAmount" class="text-color-secondary">
                                Рекомендуемое: {{ formatCurrency(data.recommendedAmount) }}
                            </div>
                        </div>
                        <div v-else class="text-color-secondary">
                            -
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" :exportable="false" style="min-width:8rem">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button 
                                icon="pi pi-eye" 
                                class="p-button-text p-button-sm" 
                                @click="viewService(data)"
                            />
                            <Button 
                                icon="pi pi-pencil" 
                                class="p-button-text p-button-sm p-button-success" 
                                @click="editService(data)"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                class="p-button-text p-button-sm p-button-danger" 
                                @click="deleteService(data)"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.service-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-pointer:hover {
    text-decoration: underline;
}
</style> 