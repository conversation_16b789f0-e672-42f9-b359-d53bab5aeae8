<script setup>
import { ref, onMounted } from 'vue';
import { ProcessingService } from '@/service/ProcessingService';

const transactions = ref([]);
const loading = ref(true);

onMounted(async () => {
    try {
        const data = await ProcessingService.getTransactionsByType('abt');
        transactions.value = data;
    } catch (error) {
        console.error('Ошибка загрузки транзакций:', error);
    } finally {
        loading.value = false;
    }
});

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount);
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'completed': return 'success';
        case 'processing': return 'warning';
        case 'error': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'completed': return 'Завершена';
        case 'processing': return 'Обработка';
        case 'error': return 'Ошибка';
        default: return status;
    }
};
</script>

<template>
    <div class="transaction-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Транзакции ABT</h1>
                <p class="text-color-secondary m-0">Транзакции через мобильные приложения</p>
            </div>
            <Button label="Обновить" icon="pi pi-refresh" />
        </div>

        <div class="card">
            <DataTable :value="transactions" :loading="loading" :paginator="true" :rows="15" responsiveLayout="scroll">
                <Column field="transactionId" header="ID транзакции" :sortable="true">
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.transactionId }}</span>
                    </template>
                </Column>
                
                <Column field="amount" header="Сумма" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatAmount(data.amount) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column field="timestamp" header="Время" :sortable="true">
                    <template #body="{ data }">
                        {{ formatDate(data.timestamp) }}
                    </template>
                </Column>
                
                <Column header="Мобильные данные">
                    <template #body="{ data }">
                        <div class="text-sm" v-if="data.paymentDetails">
                            <div>{{ data.paymentDetails.phoneNumber }}</div>
                            <div v-if="data.paymentDetails.operatorCode">{{ data.paymentDetails.operatorCode }}</div>
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.transaction-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}
.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
