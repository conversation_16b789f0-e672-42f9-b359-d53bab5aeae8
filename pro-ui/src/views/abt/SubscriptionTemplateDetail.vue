<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { SubscriptionTemplateService } from '@/service/SubscriptionTemplateService';

const router = useRouter();
const route = useRoute();
const loading = ref(true);

const template = ref(null);
const counters = ref([]);
const rules = ref([]);

onMounted(async () => {
    await loadTemplate();
});

const loadTemplate = async () => {
    loading.value = true;
    try {
        const [templateData, templateCounters, templateRules] = await Promise.all([
            SubscriptionTemplateService.getTemplate(route.params.id),
            SubscriptionTemplateService.getTemplateCounters(route.params.id),
            SubscriptionTemplateService.getTemplateRules(route.params.id)
        ]);
        
        template.value = templateData;
        counters.value = templateCounters;
        rules.value = templateRules;
        
        loading.value = false;
    } catch (error) {
        console.error('Ошибка загрузки шаблона:', error);
        loading.value = false;
    }
};

const getTypeLabel = (type) => {
    switch (type) {
        case 'WALLET': return 'Кошелек';
        case 'TRAVEL': return 'Поездочный';
        case 'UNLIMITED': return 'Безлимитный';
        default: return type;
    }
};

const getValidTimeTypeLabel = (type) => {
    switch (type) {
        case 'INTERVAL': return 'Календарный месяц';
        case 'DAYS': return 'Дни от первой поездки';
        case 'INTERVAL_AND_DAYS': return 'Дни с ограничением';
        default: return type;
    }
};

const getCounterTypeLabel = (type) => {
    switch (type) {
        case 'TRAVEL_COUNT': return 'Количество поездок';
        case 'AMOUNT': return 'Сумма списаний';
        case 'TIME_LIMIT': return 'Время действия';
        default: return type;
    }
};

const getPassActionLabel = (action) => {
    switch (action) {
        case 'raPass': return 'Проход за единицы';
        case 'raPay': return 'Проход за деньги';
        case 'raStop': return 'Проход запрещен';
        default: return action;
    }
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'inactive': return 'secondary';
        case 'draft': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'inactive': return 'Неактивный';
        case 'draft': return 'Черновик';
        default: return status;
    }
};

const formatDate = (date) => {
    if (!date) return '-';
    return new Date(date).toLocaleString('ru-RU');
};

const editTemplate = () => {
    router.push(`/abt/templates/${template.value.id}/edit`);
};

const backToList = () => {
    router.push('/abt/templates');
};
</script>

<template>
    <div class="subscription-template-detail">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Шаблон абонемента</h1>
                <p class="text-color-secondary m-0">Детальная информация о шаблоне</p>
            </div>
            <div class="flex gap-2">
                <Button 
                    label="Назад" 
                    icon="pi pi-arrow-left" 
                    @click="backToList"
                    class="p-button-secondary"
                />
                <Button 
                    label="Редактировать" 
                    icon="pi pi-pencil" 
                    @click="editTemplate"
                    class="p-button-success"
                />
            </div>
        </div>

        <div v-if="loading" class="flex justify-content-center">
            <ProgressSpinner />
        </div>

        <div v-else-if="template" class="grid">
            <!-- Основная информация -->
            <div class="col-12 lg:col-8">
                <div class="card">
                    <h3>Основная информация</h3>
                    
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold text-color-secondary">Название</label>
                            <div class="text-lg font-semibold">{{ template.stName }}</div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold text-color-secondary">Статус</label>
                            <div>
                                <Tag 
                                    :value="getStatusLabel(template.status)" 
                                    :severity="getStatusSeverity(template.status)" 
                                />
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <label class="font-semibold text-color-secondary">Описание</label>
                            <div class="text-lg">{{ template.description || '-' }}</div>
                        </div>
                        
                        <div class="col-12 md:col-4">
                            <label class="font-semibold text-color-secondary">Тип абонемента</label>
                            <div class="text-lg">
                                <Tag :value="getTypeLabel(template.type)" severity="info" />
                            </div>
                        </div>
                        
                        <div class="col-12 md:col-4">
                            <label class="font-semibold text-color-secondary">Тип срока действия</label>
                            <div class="text-lg">{{ getValidTimeTypeLabel(template.validTimeType) }}</div>
                        </div>
                        
                        <div class="col-12 md:col-4">
                            <label class="font-semibold text-color-secondary">Социальный</label>
                            <div class="text-lg">
                                <i v-if="template.isSocial" class="pi pi-check text-green-500"></i>
                                <i v-else class="pi pi-times text-red-500"></i>
                            </div>
                        </div>
                        
                        <div class="col-12 md:col-3">
                            <label class="font-semibold text-color-secondary">Код приложения</label>
                            <div class="text-lg font-mono">{{ template.appCode || '-' }}</div>
                        </div>
                        
                        <div class="col-12 md:col-3">
                            <label class="font-semibold text-color-secondary">Код билета</label>
                            <div class="text-lg font-mono">{{ template.crdCode || '-' }}</div>
                        </div>
                        
                        <div class="col-12 md:col-3">
                            <label class="font-semibold text-color-secondary">Количество дней</label>
                            <div class="text-lg">{{ template.validTimeDays || '-' }}</div>
                        </div>
                        
                        <div class="col-12 md:col-3">
                            <label class="font-semibold text-color-secondary">Версия</label>
                            <div class="text-lg">{{ template.version }}</div>
                        </div>
                    </div>
                </div>
                
                <!-- Период действия -->
                <div class="card">
                    <h3>Период действия</h3>
                    
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold text-color-secondary">Действует с</label>
                            <div class="text-lg">{{ formatDate(template.activeFrom) }}</div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold text-color-secondary">Действует по</label>
                            <div class="text-lg">{{ formatDate(template.activeTill) }}</div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold text-color-secondary">Время начала действия</label>
                            <div class="text-lg">{{ formatDate(template.validTimeStart) }}</div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold text-color-secondary">Время окончания действия</label>
                            <div class="text-lg">{{ formatDate(template.validTimeEnd) }}</div>
                        </div>
                    </div>
                </div>
                
                <!-- Метаданные -->
                <div class="card">
                    <h3>Метаданные</h3>
                    
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold text-color-secondary">Создано</label>
                            <div class="text-lg">{{ formatDate(template.versionCreatedAt) }}</div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold text-color-secondary">Создано пользователем</label>
                            <div class="text-lg">{{ template.versionCreatedBy }}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Счетчики -->
            <div class="col-12 lg:col-4">
                <div class="card">
                    <h3>Счетчики ({{ counters.length }})</h3>
                    
                    <div v-if="counters.length === 0" class="text-center text-color-secondary py-4">
                        <i class="pi pi-info-circle text-2xl mb-2"></i>
                        <p>Счетчики не настроены</p>
                    </div>
                    
                    <div v-else class="space-y-3">
                        <div 
                            v-for="counter in counters" 
                            :key="counter.id"
                            class="p-3 border-round border-1 surface-border"
                        >
                            <div class="font-semibold mb-2">{{ getCounterTypeLabel(counter.type) }}</div>
                            
                            <div class="grid">
                                <div class="col-12">
                                    <label class="text-sm text-color-secondary">Значение</label>
                                    <div class="font-semibold">{{ counter.value }}</div>
                                </div>
                                
                                <div class="col-12">
                                    <label class="text-sm text-color-secondary">Типы транспорта</label>
                                    <div class="flex flex-wrap gap-2 mt-1">
                                        <Tag 
                                            v-if="counter.isBus" 
                                            value="Автобус" 
                                            severity="info" 
                                        />
                                        <Tag 
                                            v-if="counter.isTrolleybus" 
                                            value="Троллейбус" 
                                            severity="info" 
                                        />
                                        <Tag 
                                            v-if="counter.isTram" 
                                            value="Трамвай" 
                                            severity="info" 
                                        />
                                        <Tag 
                                            v-if="counter.isMetro" 
                                            value="Метро" 
                                            severity="info" 
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Правила -->
                <div class="card">
                    <h3>Правила прохода ({{ rules.length }})</h3>
                    
                    <div v-if="rules.length === 0" class="text-center text-color-secondary py-4">
                        <i class="pi pi-info-circle text-2xl mb-2"></i>
                        <p>Правила не настроены</p>
                    </div>
                    
                    <div v-else class="space-y-3">
                        <div 
                            v-for="rule in rules" 
                            :key="rule.id"
                            class="p-3 border-round border-1 surface-border"
                        >
                            <div class="font-semibold mb-2">Правило {{ rule.passIndex }}</div>
                            
                            <div class="grid">
                                <div class="col-6">
                                    <label class="text-sm text-color-secondary">Индекс прохода</label>
                                    <div class="font-semibold">{{ rule.passIndex }}</div>
                                </div>
                                
                                <div class="col-6">
                                    <label class="text-sm text-color-secondary">Действие</label>
                                    <div class="font-semibold">{{ getPassActionLabel(rule.passAction) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.subscription-template-detail {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.card h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style> 