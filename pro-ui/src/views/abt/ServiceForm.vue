<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ServiceService } from '@/service/ServiceService';
import { SubscriptionTemplateService } from '@/service/SubscriptionTemplateService';

const router = useRouter();
const route = useRoute();
const loading = ref(false);
const saving = ref(false);

const isEdit = computed(() => route.params.id !== undefined);

// Форма
const form = ref({
    projectId: 'proj-1',
    templateId: '',
    serviceCode: '',
    name: '',
    description: '',
    isSocial: false,
    subscriptionType: 'TRAVEL',
    cost: null,
    actionStartDate: null,
    actionEndDate: null,
    minReplenishmentAmount: null,
    maxReplenishmentAmount: null,
    recommendedAmount: null
});

// Опции
const subscriptionTypeOptions = ref([]);
const templateOptions = ref([]);
const selectedTemplate = ref(null);

onMounted(async () => {
    try {
        loading.value = true;
        
        // Загружаем опции
        subscriptionTypeOptions.value = await ServiceService.getSubscriptionTypes();
        await loadTemplateOptions();
        
        // Если редактирование, загружаем данные
        if (isEdit.value) {
            const service = await ServiceService.getService(route.params.id);
            form.value = {
                ...service,
                actionStartDate: service.actionStartDate ? new Date(service.actionStartDate) : null,
                actionEndDate: service.actionEndDate ? new Date(service.actionEndDate) : null
            };
            
            // Загружаем выбранный шаблон
            if (service.templateId) {
                await loadSelectedTemplate(service.templateId);
            }
        }
    } catch (error) {
        console.error('Ошибка загрузки данных:', error);
    } finally {
        loading.value = false;
    }
});

// Загрузка опций шаблонов
const loadTemplateOptions = async () => {
    try {
        const templates = await SubscriptionTemplateService.getTemplates({ status: 'active' });
        templateOptions.value = templates.map(template => ({
            label: `${template.stName} (${template.type})`,
            value: template.id,
            template: template
        }));
    } catch (error) {
        console.error('Ошибка загрузки шаблонов:', error);
    }
};

// Загрузка выбранного шаблона
const loadSelectedTemplate = async (templateId) => {
    try {
        const template = await SubscriptionTemplateService.getTemplate(templateId);
        selectedTemplate.value = template;
    } catch (error) {
        console.error('Ошибка загрузки шаблона:', error);
    }
};

// Обработчик изменения шаблона
const onTemplateChange = async () => {
    if (form.value.templateId) {
        await loadSelectedTemplate(form.value.templateId);
        
        // Автозаполнение полей из шаблона
        const template = selectedTemplate.value;
        if (template) {
            form.value.subscriptionType = template.type;
            form.value.isSocial = template.isSocial;
            form.value.description = template.description;
            
            // Устанавливаем даты действия из шаблона
            if (template.activeFrom) {
                form.value.actionStartDate = new Date(template.activeFrom);
            }
            if (template.activeTill) {
                form.value.actionEndDate = new Date(template.activeTill);
            }
        }
    } else {
        selectedTemplate.value = null;
    }
};

const save = async () => {
    try {
        saving.value = true;
        
        const serviceData = {
            ...form.value,
            actionStartDate: form.value.actionStartDate ? form.value.actionStartDate.toISOString().split('T')[0] : null,
            actionEndDate: form.value.actionEndDate ? form.value.actionEndDate.toISOString().split('T')[0] : null
        };
        
        if (isEdit.value) {
            await ServiceService.updateService(route.params.id, serviceData);
        } else {
            await ServiceService.createService(serviceData);
        }
        
        router.push('/abt/services');
    } catch (error) {
        console.error('Ошибка сохранения услуги:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push('/abt/services');
};

const formatCurrency = (amount) => {
    if (!amount) return '';
    return (amount / 100).toString(); // Конвертируем из копеек в рубли
};

const parseCurrency = (value) => {
    if (!value) return null;
    return Math.round(parseFloat(value) * 100); // Конвертируем рубли в копейки
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ru-RU');
};
</script>

<template>
    <div class="service-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">
                    {{ isEdit ? 'Редактирование услуги' : 'Создание услуги' }}
                </h1>
                <p class="text-color-secondary m-0">Управление услугами и товарами</p>
            </div>
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="save">
                <!-- Основная информация -->
                <div class="mb-4">
                    <h3>Основная информация</h3>
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Код услуги *</label>
                            <InputText 
                                v-model="form.serviceCode" 
                                placeholder="Введите код услуги"
                                class="w-full"
                                required
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Название *</label>
                            <InputText 
                                v-model="form.name" 
                                placeholder="Введите название услуги"
                                class="w-full"
                                required
                            />
                        </div>
                        
                        <div class="col-12">
                            <label class="font-semibold">Шаблон абонемента</label>
                            <Dropdown 
                                v-model="form.templateId" 
                                :options="templateOptions"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Выберите шаблон абонемента (необязательно)"
                                class="w-full"
                                @change="onTemplateChange"
                            />
                            <small class="text-color-secondary">
                                При выборе шаблона некоторые поля будут заполнены автоматически
                            </small>
                        </div>
                        
                        <div class="col-12">
                            <label class="font-semibold">Описание</label>
                            <Textarea 
                                v-model="form.description" 
                                placeholder="Введите описание услуги"
                                class="w-full"
                                rows="3"
                            />
                        </div>
                        
                        <!-- Информация о выбранном шаблоне -->
                        <div class="col-12" v-if="selectedTemplate">
                            <div class="p-3 border-round surface-100">
                                <h4 class="m-0 mb-2">Информация о шаблоне</h4>
                                <div class="grid">
                                    <div class="col-12 md:col-6">
                                        <div class="field">
                                            <label class="font-semibold text-sm">Название шаблона</label>
                                            <div>{{ selectedTemplate.stName }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <div class="field">
                                            <label class="font-semibold text-sm">Тип</label>
                                            <div>{{ selectedTemplate.type }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <div class="field">
                                            <label class="font-semibold text-sm">Социальный</label>
                                            <div>{{ selectedTemplate.isSocial ? 'Да' : 'Нет' }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <div class="field">
                                            <label class="font-semibold text-sm">Срок действия</label>
                                            <div>{{ selectedTemplate.validTimeDays }} дней</div>
                                        </div>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <div class="field">
                                            <label class="font-semibold text-sm">Активен с</label>
                                            <div>{{ formatDate(selectedTemplate.activeFrom) }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <div class="field">
                                            <label class="font-semibold text-sm">Активен до</label>
                                            <div>{{ formatDate(selectedTemplate.activeTill) }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Тип подписки *</label>
                            <Dropdown 
                                v-model="form.subscriptionType" 
                                :options="subscriptionTypeOptions"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Выберите тип подписки"
                                class="w-full"
                                required
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Социальная категория</label>
                            <div class="flex align-items-center mt-2">
                                <Checkbox 
                                    v-model="form.isSocial" 
                                    :binary="true"
                                    inputId="isSocial"
                                />
                                <label for="isSocial" class="ml-2">Социальная услуга</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Параметры для абонементов -->
                <div class="mb-4" v-if="form.subscriptionType !== 'WALLET'">
                    <h3>Параметры абонемента</h3>
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Стоимость (руб.)</label>
                            <InputNumber 
                                v-model="form.cost" 
                                :minFractionDigits="2"
                                :maxFractionDigits="2"
                                placeholder="0.00"
                                class="w-full"
                                mode="currency"
                                currency="RUB"
                                locale="ru-RU"
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Дата начала действия</label>
                            <Calendar 
                                v-model="form.actionStartDate" 
                                dateFormat="dd.mm.yy"
                                placeholder="Выберите дату"
                                class="w-full"
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Дата окончания действия</label>
                            <Calendar 
                                v-model="form.actionEndDate" 
                                dateFormat="dd.mm.yy"
                                placeholder="Выберите дату"
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>

                <!-- Параметры для кошельков -->
                <div class="mb-4" v-if="form.subscriptionType === 'WALLET'">
                    <h3>Параметры пополнения</h3>
                    <div class="grid">
                        <div class="col-12 md:col-4">
                            <label class="font-semibold">Минимальная сумма пополнения (руб.)</label>
                            <InputNumber 
                                v-model="form.minReplenishmentAmount" 
                                :minFractionDigits="2"
                                :maxFractionDigits="2"
                                placeholder="0.00"
                                class="w-full"
                                mode="currency"
                                currency="RUB"
                                locale="ru-RU"
                            />
                        </div>
                        
                        <div class="col-12 md:col-4">
                            <label class="font-semibold">Максимальная сумма пополнения (руб.)</label>
                            <InputNumber 
                                v-model="form.maxReplenishmentAmount" 
                                :minFractionDigits="2"
                                :maxFractionDigits="2"
                                placeholder="0.00"
                                class="w-full"
                                mode="currency"
                                currency="RUB"
                                locale="ru-RU"
                            />
                        </div>
                        
                        <div class="col-12 md:col-4">
                            <label class="font-semibold">Рекомендуемая сумма (руб.)</label>
                            <InputNumber 
                                v-model="form.recommendedAmount" 
                                :minFractionDigits="2"
                                :maxFractionDigits="2"
                                placeholder="0.00"
                                class="w-full"
                                mode="currency"
                                currency="RUB"
                                locale="ru-RU"
                            />
                        </div>
                    </div>
                </div>

                <!-- Кнопки -->
                <div class="flex justify-content-end gap-2">
                    <Button 
                        type="button"
                        label="Отмена" 
                        icon="pi pi-times" 
                        @click="cancel"
                        class="p-button-secondary"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        icon="pi pi-check" 
                        :loading="saving"
                        class="p-button-success"
                    />
                </div>
            </form>
        </div>

        <div class="card" v-else>
            <ProgressSpinner />
        </div>
    </div>
</template>

<style scoped>
.service-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}
</style> 