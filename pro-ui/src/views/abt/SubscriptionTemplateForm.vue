<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { SubscriptionTemplateService } from '@/service/SubscriptionTemplateService';

const router = useRouter();
const route = useRoute();

const isEdit = computed(() => route.params.id !== undefined);
const loading = ref(false);
const saving = ref(false);

// Форма шаблона
const form = ref({
    stName: '',
    description: '',
    type: 'TRAVEL',
    appCode: null,
    crdCode: null,
    isSocial: false,
    validTimeType: 'INTERVAL',
    validTimeStart: null,
    validTimeDays: null,
    validTimeEnd: null,
    activeFrom: null,
    activeTill: null
});

// Счетчики шаблона
const counters = ref([]);

// Правила шаблона
const rules = ref([]);

// Опции для селектов
const typeOptions = [
    { label: 'Кошелек', value: 'WALLET' },
    { label: 'Поездочный', value: 'TRAVEL' },
    { label: 'Безлимитный', value: 'UNLIMITED' }
];

const validTimeTypeOptions = [
    { label: 'Календарный месяц', value: 'INTERVAL' },
    { label: 'Дни от первой поездки', value: 'DAYS' },
    { label: 'Дни с ограничением', value: 'INTERVAL_AND_DAYS' }
];

const counterTypeOptions = [
    { label: 'Количество поездок', value: 'TRAVEL_COUNT' },
    { label: 'Сумма списаний', value: 'AMOUNT' },
    { label: 'Время действия', value: 'TIME_LIMIT' }
];

const transportTypeOptions = [
    { label: 'Автобус', value: 'isBus' },
    { label: 'Троллейбус', value: 'isTrolleybus' },
    { label: 'Трамвай', value: 'isTram' },
    { label: 'Метро', value: 'isMetro' }
];

onMounted(async () => {
    if (isEdit.value) {
        await loadTemplate();
    }
});

const loadTemplate = async () => {
    loading.value = true;
    try {
        const [template, templateCounters, templateRules] = await Promise.all([
            SubscriptionTemplateService.getTemplate(route.params.id),
            SubscriptionTemplateService.getTemplateCounters(route.params.id),
            SubscriptionTemplateService.getTemplateRules(route.params.id)
        ]);
        
        form.value = {
            stName: template.stName,
            description: template.description,
            type: template.type,
            appCode: template.appCode,
            crdCode: template.crdCode,
            isSocial: template.isSocial,
            validTimeType: template.validTimeType,
            validTimeStart: template.validTimeStart ? new Date(template.validTimeStart) : null,
            validTimeDays: template.validTimeDays,
            validTimeEnd: template.validTimeEnd ? new Date(template.validTimeEnd) : null,
            activeFrom: template.activeFrom ? new Date(template.activeFrom) : null,
            activeTill: template.activeTill ? new Date(template.activeTill) : null
        };
        
        counters.value = templateCounters;
        rules.value = templateRules;
        
        loading.value = false;
    } catch (error) {
        console.error('Ошибка загрузки шаблона:', error);
        alert(`Ошибка загрузки шаблона: ${error.message}`);
        loading.value = false;
    }
};

const addCounter = () => {
    counters.value.push({
        id: Date.now().toString(),
        type: 'TRAVEL_COUNT',
        value: 0,
        isBus: false,
        isTrolleybus: false,
        isTram: false,
        isMetro: false
    });
};

const removeCounter = async (index) => {
    const counter = counters.value[index];
    
    // Если это новый счетчик (временный ID), просто удаляем из массива
    if (counter.id && counter.id.toString().length < 20) {
        counters.value.splice(index, 1);
        return;
    }
    
    // Если это существующий счетчик, удаляем через API
    try {
        await SubscriptionTemplateService.deleteCounter(counter.id);
        counters.value.splice(index, 1);
        alert('Счетчик успешно удален');
    } catch (error) {
        console.error('Ошибка удаления счетчика:', error);
        alert(`Ошибка удаления счетчика: ${error.message}`);
    }
};

const addRule = () => {
    rules.value.push({
        id: Date.now().toString(),
        passIndex: rules.value.length + 1,
        passAction: 'raPass'
    });
};

const removeRule = async (index) => {
    const rule = rules.value[index];
    
    // Если это новое правило (временный ID), просто удаляем из массива
    if (rule.id && rule.id.toString().length < 20) {
        rules.value.splice(index, 1);
        return;
    }
    
    // Если это существующее правило, удаляем через API
    try {
        await SubscriptionTemplateService.deleteRule(rule.id);
        rules.value.splice(index, 1);
        alert('Правило успешно удалено');
    } catch (error) {
        console.error('Ошибка удаления правила:', error);
        alert(`Ошибка удаления правила: ${error.message}`);
    }
};

const saveTemplate = async () => {
    saving.value = true;
    try {
        let templateId;
        
        if (isEdit.value) {
            const updatedTemplate = await SubscriptionTemplateService.updateTemplate(route.params.id, form.value);
            templateId = updatedTemplate.id;
            alert('Шаблон успешно обновлен');
        } else {
            const createdTemplate = await SubscriptionTemplateService.createTemplate(form.value);
            templateId = createdTemplate.id;
            alert('Шаблон успешно создан');
        }
        
        // Сохраняем счетчики, если они есть
        if (counters.value.length > 0) {
            try {
                // Фильтруем только новые счетчики (с временными ID)
                const newCounters = counters.value.filter(counter => 
                    counter.id && counter.id.toString().length < 20
                );
                
                if (newCounters.length > 0) {
                    // Подготавливаем данные счетчиков для API
                    const countersData = newCounters.map(counter => ({
                        subscriptionTemplateId: templateId,
                        type: counter.type,
                        value: counter.value,
                        isBus: counter.isBus,
                        isTrolleybus: counter.isTrolleybus,
                        isTram: counter.isTram,
                        isMetro: counter.isMetro
                    }));
                    
                    await SubscriptionTemplateService.createCountersForTemplate(templateId, countersData);
                    alert('Счетчики успешно сохранены');
                }
            } catch (error) {
                console.error('Ошибка сохранения счетчиков:', error);
                alert(`Ошибка сохранения счетчиков: ${error.message}`);
            }
        }
        
        // Сохраняем правила, если они есть
        if (rules.value.length > 0) {
            try {
                // Фильтруем только новые правила (с временными ID)
                const newRules = rules.value.filter(rule => 
                    rule.id && rule.id.toString().length < 20
                );
                
                if (newRules.length > 0) {
                    // Подготавливаем данные правил для API
                    const rulesData = newRules.map(rule => ({
                        subscriptionTemplateId: templateId,
                        passIndex: rule.passIndex,
                        passAction: rule.passAction
                    }));
                    
                    await SubscriptionTemplateService.createRulesForTemplate(templateId, rulesData);
                    alert('Правила успешно сохранены');
                }
            } catch (error) {
                console.error('Ошибка сохранения правил:', error);
                alert(`Ошибка сохранения правил: ${error.message}`);
            }
        }
        
        // Перенаправление на список
        router.push('/abt/templates');
    } catch (error) {
        console.error('Ошибка сохранения:', error);
        alert(`Ошибка сохранения: ${error.message}`);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push('/abt/templates');
};

const getTypeLabel = (type) => {
    const option = typeOptions.find(opt => opt.value === type);
    return option ? option.label : type;
};

const getValidTimeTypeLabel = (type) => {
    const option = validTimeTypeOptions.find(opt => opt.value === type);
    return option ? option.label : type;
};

const getCounterTypeLabel = (type) => {
    const option = counterTypeOptions.find(opt => opt.value === type);
    return option ? option.label : type;
};
</script>

<template>
    <div class="subscription-template-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">
                    {{ isEdit ? 'Редактирование шаблона' : 'Создание шаблона' }}
                </h1>
                <p class="text-color-secondary m-0">
                    {{ isEdit ? 'Изменение параметров шаблона абонемента' : 'Создание нового шаблона абонемента' }}
                </p>
            </div>
            <div class="flex gap-2">
                <Button 
                    label="Отмена" 
                    icon="pi pi-times" 
                    @click="cancel"
                    class="p-button-secondary"
                />
                <Button 
                    :label="isEdit ? 'Сохранить' : 'Создать'" 
                    icon="pi pi-check" 
                    @click="saveTemplate"
                    :loading="saving"
                    class="p-button-success"
                />
            </div>
        </div>

        <div v-if="loading" class="flex justify-content-center">
            <ProgressSpinner />
        </div>

        <div v-else class="grid">
            <!-- Основная информация -->
            <div class="col-12 lg:col-8">
                <div class="card">
                    <h3>Основная информация</h3>
                    
                    <div class="grid">
                        <div class="col-12">
                            <label for="stName" class="font-semibold">Название *</label>
                            <InputText 
                                id="stName" 
                                v-model="form.stName" 
                                class="w-full"
                                placeholder="Введите название шаблона"
                            />
                        </div>
                        
                        <div class="col-12">
                            <label for="description" class="font-semibold">Описание</label>
                            <Textarea 
                                id="description" 
                                v-model="form.description" 
                                rows="3"
                                class="w-full"
                                placeholder="Введите описание шаблона"
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label for="type" class="font-semibold">Тип абонемента *</label>
                            <Dropdown 
                                id="type" 
                                v-model="form.type" 
                                :options="typeOptions"
                                optionLabel="label"
                                optionValue="value"
                                class="w-full"
                                placeholder="Выберите тип"
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label for="validTimeType" class="font-semibold">Тип срока действия *</label>
                            <Dropdown 
                                id="validTimeType" 
                                v-model="form.validTimeType" 
                                :options="validTimeTypeOptions"
                                optionLabel="label"
                                optionValue="value"
                                class="w-full"
                                placeholder="Выберите тип срока"
                            />
                        </div>
                        
                        <div class="col-12 md:col-4">
                            <label for="appCode" class="font-semibold">Код приложения</label>
                            <InputNumber 
                                id="appCode" 
                                v-model="form.appCode" 
                                class="w-full"
                                placeholder="Код приложения"
                            />
                        </div>
                        
                        <div class="col-12 md:col-4">
                            <label for="crdCode" class="font-semibold">Код билета</label>
                            <InputNumber 
                                id="crdCode" 
                                v-model="form.crdCode" 
                                class="w-full"
                                placeholder="Код билета"
                            />
                        </div>
                        
                        <div class="col-12 md:col-4">
                            <label for="validTimeDays" class="font-semibold">Количество дней</label>
                            <InputNumber 
                                id="validTimeDays" 
                                v-model="form.validTimeDays" 
                                class="w-full"
                                placeholder="Количество дней"
                            />
                        </div>
                        
                        <div class="col-12">
                            <div class="flex align-items-center">
                                <Checkbox 
                                    id="isSocial" 
                                    v-model="form.isSocial" 
                                    :binary="true"
                                />
                                <label for="isSocial" class="ml-2 font-semibold">Социальный абонемент</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Период действия -->
                <div class="card">
                    <h3>Период действия</h3>
                    
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label for="activeFrom" class="font-semibold">Действует с</label>
                            <Calendar 
                                id="activeFrom" 
                                v-model="form.activeFrom" 
                                class="w-full"
                                dateFormat="dd.mm.yy"
                                placeholder="Выберите дату"
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label for="activeTill" class="font-semibold">Действует по</label>
                            <Calendar 
                                id="activeTill" 
                                v-model="form.activeTill" 
                                class="w-full"
                                dateFormat="dd.mm.yy"
                                placeholder="Выберите дату"
                            />
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Счетчики -->
            <div class="col-12 lg:col-4">
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-3">
                        <h3>Счетчики</h3>
                        <Button 
                            icon="pi pi-plus" 
                            @click="addCounter"
                            class="p-button-sm p-button-success"
                        />
                    </div>
                    
                    <div v-if="counters.length === 0" class="text-center text-color-secondary py-4">
                        <i class="pi pi-info-circle text-2xl mb-2"></i>
                        <p>Счетчики не добавлены</p>
                    </div>
                    
                    <div v-else class="space-y-3">
                        <div 
                            v-for="(counter, index) in counters" 
                            :key="counter.id"
                            class="p-3 border-round border-1 surface-border"
                        >
                            <div class="flex justify-content-between align-items-center mb-2">
                                <span class="font-semibold">{{ getCounterTypeLabel(counter.type) }}</span>
                                <Button 
                                    icon="pi pi-trash" 
                                    @click="removeCounter(index)"
                                    class="p-button-text p-button-sm p-button-danger"
                                />
                            </div>
                            
                            <div class="grid">
                                <div class="col-12">
                                    <label class="text-sm">Тип счетчика</label>
                                    <Dropdown 
                                        v-model="counter.type" 
                                        :options="counterTypeOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        class="w-full"
                                    />
                                </div>
                                
                                <div class="col-12">
                                    <label class="text-sm">Значение</label>
                                    <InputNumber 
                                        v-model="counter.value" 
                                        class="w-full"
                                    />
                                </div>
                                
                                <div class="col-12">
                                    <label class="text-sm">Типы транспорта</label>
                                    <div class="grid">
                                        <div 
                                            v-for="option in transportTypeOptions" 
                                            :key="option.value"
                                            class="col-6"
                                        >
                                            <div class="flex align-items-center">
                                                <Checkbox 
                                                    v-model="counter[option.value]" 
                                                    :binary="true"
                                                />
                                                <label class="ml-2 text-sm">{{ option.label }}</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Правила -->
                <div class="card">
                    <div class="flex justify-content-between align-items-center mb-3">
                        <h3>Правила прохода</h3>
                        <Button 
                            icon="pi pi-plus" 
                            @click="addRule"
                            class="p-button-sm p-button-success"
                        />
                    </div>
                    
                    <div v-if="rules.length === 0" class="text-center text-color-secondary py-4">
                        <i class="pi pi-info-circle text-2xl mb-2"></i>
                        <p>Правила не добавлены</p>
                    </div>
                    
                    <div v-else class="space-y-3">
                        <div 
                            v-for="(rule, index) in rules" 
                            :key="rule.id"
                            class="p-3 border-round border-1 surface-border"
                        >
                            <div class="flex justify-content-between align-items-center mb-2">
                                <span class="font-semibold">Правило {{ rule.passIndex }}</span>
                                <Button 
                                    icon="pi pi-trash" 
                                    @click="removeRule(index)"
                                    class="p-button-text p-button-sm p-button-danger"
                                />
                            </div>
                            
                            <div class="grid">
                                <div class="col-6">
                                    <label class="text-sm">Индекс прохода</label>
                                    <InputNumber 
                                        v-model="rule.passIndex" 
                                        class="w-full"
                                    />
                                </div>
                                
                                <div class="col-6">
                                    <label class="text-sm">Действие</label>
                                    <Dropdown 
                                        v-model="rule.passAction" 
                                        :options="[
                                            { label: 'Проход за единицы', value: 'raPass' },
                                            { label: 'Проход за деньги', value: 'raPay' },
                                            { label: 'Проход запрещен', value: 'raStop' }
                                        ]"
                                        optionLabel="label"
                                        optionValue="value"
                                        class="w-full"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.subscription-template-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.card h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: var(--text-color);
}
</style> 