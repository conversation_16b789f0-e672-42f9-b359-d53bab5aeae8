<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { SaleRuleService } from '@/service/SaleRuleService';

const router = useRouter();
const route = useRoute();
const loading = ref(false);
const saving = ref(false);

const isEdit = computed(() => route.params.id !== undefined);

// Форма
const form = ref({
    serviceId: '',
    ruleType: 'MONTHLY_RANGE',
    ruleLogic: 'AND',
    startDay: null,
    endDay: null,
    startMonth: null,
    endMonth: null,
    startWeek: null,
    endWeek: null,
    minCardBalance: null,
    maxCardBalance: null,
    isActive: true
});

// Опции
const ruleTypeOptions = ref([]);
const ruleLogicOptions = ref([]);
const serviceOptions = ref([]);

onMounted(async () => {
    try {
        loading.value = true;
        
        // Загружаем опции
        ruleTypeOptions.value = await SaleRuleService.getRuleTypes();
        ruleLogicOptions.value = await SaleRuleService.getRuleLogics();
        serviceOptions.value = await SaleRuleService.getServices();
        
        // Если редактирование, загружаем данные
        if (isEdit.value) {
            const rule = await SaleRuleService.getSaleRule(route.params.id);
            form.value = { ...rule };
        }
    } catch (error) {
        console.error('Ошибка загрузки данных:', error);
    } finally {
        loading.value = false;
    }
});

const save = async () => {
    try {
        saving.value = true;
        
        if (isEdit.value) {
            await SaleRuleService.updateSaleRule(route.params.id, form.value);
        } else {
            await SaleRuleService.createSaleRule(form.value);
        }
        
        router.push('/abt/sale-rules');
    } catch (error) {
        console.error('Ошибка сохранения правила:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push('/abt/sale-rules');
};

const formatCurrency = (amount) => {
    if (!amount) return '';
    return (amount / 100).toString(); // Конвертируем из копеек в рубли
};

const parseCurrency = (value) => {
    if (!value) return null;
    return Math.round(parseFloat(value) * 100); // Конвертируем рубли в копейки
};
</script>

<template>
    <div class="sale-rule-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">
                    {{ isEdit ? 'Редактирование правила продаж' : 'Создание правила продаж' }}
                </h1>
                <p class="text-color-secondary m-0">Управление правилами продаж услуг</p>
            </div>
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="save">
                <!-- Основная информация -->
                <div class="mb-4">
                    <h3>Основная информация</h3>
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Услуга *</label>
                            <Dropdown 
                                v-model="form.serviceId" 
                                :options="serviceOptions"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Выберите услугу"
                                class="w-full"
                                required
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Тип правила *</label>
                            <Dropdown 
                                v-model="form.ruleType" 
                                :options="ruleTypeOptions"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Выберите тип правила"
                                class="w-full"
                                required
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Логика правила *</label>
                            <Dropdown 
                                v-model="form.ruleLogic" 
                                :options="ruleLogicOptions"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Выберите логику"
                                class="w-full"
                                required
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Статус</label>
                            <div class="flex align-items-center mt-2">
                                <Checkbox 
                                    v-model="form.isActive" 
                                    :binary="true"
                                    inputId="isActive"
                                />
                                <label for="isActive" class="ml-2">Активное правило</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Параметры для временных диапазонов -->
                <div class="mb-4" v-if="['MONTHLY_RANGE', 'DAILY_RANGE'].includes(form.ruleType)">
                    <h3>Временные параметры</h3>
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Начальный день</label>
                            <InputNumber 
                                v-model="form.startDay" 
                                :min="1"
                                :max="31"
                                placeholder="1-31"
                                class="w-full"
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Конечный день</label>
                            <InputNumber 
                                v-model="form.endDay" 
                                :min="1"
                                :max="31"
                                placeholder="1-31"
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>

                <!-- Параметры для месячных диапазонов -->
                <div class="mb-4" v-if="form.ruleType === 'MONTHLY_RANGE'">
                    <h3>Месячные параметры</h3>
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Начальный месяц</label>
                            <InputNumber 
                                v-model="form.startMonth" 
                                :min="1"
                                :max="12"
                                placeholder="1-12"
                                class="w-full"
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Конечный месяц</label>
                            <InputNumber 
                                v-model="form.endMonth" 
                                :min="1"
                                :max="12"
                                placeholder="1-12"
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>

                <!-- Параметры для недельных диапазонов -->
                <div class="mb-4" v-if="form.ruleType === 'WEEKLY_RANGE'">
                    <h3>Недельные параметры</h3>
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Начальная неделя</label>
                            <InputNumber 
                                v-model="form.startWeek" 
                                :min="1"
                                :max="53"
                                placeholder="1-53"
                                class="w-full"
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Конечная неделя</label>
                            <InputNumber 
                                v-model="form.endWeek" 
                                :min="1"
                                :max="53"
                                placeholder="1-53"
                                class="w-full"
                            />
                        </div>
                    </div>
                </div>

                <!-- Параметры для диапазона баланса -->
                <div class="mb-4" v-if="form.ruleType === 'BALANCE_RANGE'">
                    <h3>Параметры баланса</h3>
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Минимальный баланс карты (руб.)</label>
                            <InputNumber 
                                v-model="form.minCardBalance" 
                                :minFractionDigits="2"
                                :maxFractionDigits="2"
                                placeholder="0.00"
                                class="w-full"
                                mode="currency"
                                currency="RUB"
                                locale="ru-RU"
                            />
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Максимальный баланс карты (руб.)</label>
                            <InputNumber 
                                v-model="form.maxCardBalance" 
                                :minFractionDigits="2"
                                :maxFractionDigits="2"
                                placeholder="0.00"
                                class="w-full"
                                mode="currency"
                                currency="RUB"
                                locale="ru-RU"
                            />
                        </div>
                    </div>
                </div>

                <!-- Кнопки -->
                <div class="flex justify-content-end gap-2">
                    <Button 
                        type="button"
                        label="Отмена" 
                        icon="pi pi-times" 
                        @click="cancel"
                        class="p-button-secondary"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        icon="pi pi-check" 
                        :loading="saving"
                        class="p-button-success"
                    />
                </div>
            </form>
        </div>

        <div class="card" v-else>
            <ProgressSpinner />
        </div>
    </div>
</template>

<style scoped>
.sale-rule-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}
</style> 