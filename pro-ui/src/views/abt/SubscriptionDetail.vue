<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { SubscriptionService } from '@/service/SubscriptionService';
import { SubscriptionTemplateService } from '@/service/SubscriptionTemplateService';

const route = useRoute();
const router = useRouter();
const subscription = ref(null);
const counters = ref([]);
const template = ref(null);
const loading = ref(true);

onMounted(async () => {
    try {
        loading.value = true;
        const subscriptionId = route.params.id;
        
        // Загружаем данные абонемента
        const [subscriptionData, countersData] = await Promise.all([
            SubscriptionService.getSubscription(subscriptionId),
            SubscriptionService.getSubscriptionCounters(subscriptionId)
        ]);
        
        subscription.value = subscriptionData;
        counters.value = countersData;
        
        // Загружаем шаблон абонемента
        if (subscriptionData.ttId) {
            try {
                template.value = await SubscriptionTemplateService.getTemplate(subscriptionData.ttId);
            } catch (error) {
                console.error('Ошибка загрузки шаблона:', error);
            }
        }
    } catch (error) {
        console.error('Ошибка загрузки данных абонемента:', error);
    } finally {
        loading.value = false;
    }
});

const getTypeLabel = (type) => {
    switch (type) {
        case 'WALLET': return 'Кошелек';
        case 'TRAVEL': return 'Поездочный';
        case 'UNLIMITED': return 'Безлимитный';
        default: return type;
    }
};

const getStatusSeverity = (subscription) => {
    if (!subscription) return 'secondary';
    
    const now = new Date();
    const activeFrom = new Date(subscription.activeFrom);
    const activeTill = new Date(subscription.activeTill);
    
    if (now >= activeFrom && now <= activeTill) {
        return 'success';
    } else if (now > activeTill) {
        return 'danger';
    } else {
        return 'secondary';
    }
};

const getStatusLabel = (subscription) => {
    if (!subscription) return 'Неизвестно';
    
    const now = new Date();
    const activeFrom = new Date(subscription.activeFrom);
    const activeTill = new Date(subscription.activeTill);
    
    if (now >= activeFrom && now <= activeTill) {
        return 'Активный';
    } else if (now > activeTill) {
        return 'Просрочен';
    } else {
        return 'Неактивный';
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const getTransportTypes = (counter) => {
    const types = [];
    if (counter.isBus) types.push('Автобус');
    if (counter.isTrolleybus) types.push('Троллейбус');
    if (counter.isTram) types.push('Трамвай');
    if (counter.isMetro) types.push('Метро');
    return types.join(', ');
};

const goBack = () => {
    router.push('/abt/subscriptions');
};
</script>

<template>
    <div class="subscription-detail">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <Button 
                    icon="pi pi-arrow-left" 
                    class="p-button-text" 
                    @click="goBack"
                />
                <h1 class="text-2xl font-bold m-0 mb-2">Детали абонемента</h1>
                <p class="text-color-secondary m-0">Подробная информация об абонементе</p>
            </div>
            <div class="flex gap-2">
                <Button 
                    label="Редактировать" 
                    icon="pi pi-pencil" 
                    class="p-button-success"
                />
                <Button 
                    label="Удалить" 
                    icon="pi pi-trash" 
                    class="p-button-danger"
                />
            </div>
        </div>

        <div v-if="loading" class="flex justify-content-center">
            <ProgressSpinner />
        </div>

        <div v-else-if="subscription" class="grid">
            <!-- Основная информация -->
            <div class="col-12 lg:col-8">
                <div class="card">
                    <h3>Основная информация</h3>
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Номер абонемента</label>
                            <div class="text-lg font-bold">{{ subscription.abonementId || 'N/A' }}</div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Статус</label>
                            <div>
                                <Tag 
                                    :value="getStatusLabel(subscription)" 
                                    :severity="getStatusSeverity(subscription)" 
                                />
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <label class="font-semibold">Название</label>
                            <div class="text-lg">{{ subscription.name }}</div>
                        </div>
                        
                        <div class="col-12" v-if="subscription.description">
                            <label class="font-semibold">Описание</label>
                            <div>{{ subscription.description }}</div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Дата создания</label>
                            <div>{{ formatDateTime(subscription.createdAt) }}</div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">ID карты</label>
                            <div class="font-mono">{{ subscription.crdId }}</div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Период действия</label>
                            <div>
                                <div>С: {{ formatDate(subscription.activeFrom) }}</div>
                                <div>По: {{ formatDate(subscription.activeTill) }}</div>
                            </div>
                        </div>
                        
                        <div class="col-12 md:col-6">
                            <label class="font-semibold">Категория</label>
                            <div>
                                <Tag v-if="subscription.isSocial" value="Социальный" severity="warning" />
                                <Tag v-else value="Обычный" severity="info" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Счетчики -->
                <div class="card mt-4">
                    <h3>Счетчики</h3>
                    <div v-if="counters.length === 0" class="text-center text-color-secondary">
                        Счетчики не найдены
                    </div>
                    <div v-else class="grid">
                        <div 
                            v-for="counter in counters" 
                            :key="counter.id" 
                            class="col-12 md:col-6"
                        >
                            <div class="border-round border-1 surface-border p-3">
                                <div class="flex justify-content-between align-items-center mb-2">
                                    <h4 class="m-0">{{ counter.counterType }}</h4>
                                    <Tag :value="counter.counterValue" severity="success" />
                                </div>
                                <div class="text-sm text-color-secondary">
                                    <div>Типы транспорта: {{ getTransportTypes(counter) }}</div>
                                    <div>Создан: {{ formatDateTime(counter.createdAt) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Шаблон -->
            <div class="col-12 lg:col-4">
                <div class="card">
                    <h3>Шаблон абонемента</h3>
                    <div v-if="template">
                        <div class="mb-3">
                            <label class="font-semibold">Название шаблона</label>
                            <div>{{ template.stName }}</div>
                        </div>
                        
                        <div class="mb-3" v-if="template.description">
                            <label class="font-semibold">Описание</label>
                            <div>{{ template.description }}</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="font-semibold">Тип</label>
                            <div>
                                <Tag :value="getTypeLabel(template.type)" severity="info" />
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="font-semibold">Версия</label>
                            <div>{{ subscription.ttVersion }}</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="font-semibold">Код приложения</label>
                            <div>{{ template.appCode }}</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="font-semibold">Код карты</label>
                            <div>{{ template.crdCode }}</div>
                        </div>
                    </div>
                    <div v-else class="text-center text-color-secondary">
                        Шаблон не найден
                    </div>
                </div>
            </div>
        </div>

        <div v-else class="card">
            <div class="text-center text-color-secondary">
                Абонемент не найден
            </div>
        </div>
    </div>
</template>

<style scoped>
.subscription-detail {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}
</style> 