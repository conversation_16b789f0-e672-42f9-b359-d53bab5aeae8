<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { SalesService } from '@/service/SalesService';

const router = useRouter();
const route = useRoute();
const loading = ref(true);
const sale = ref(null);

onMounted(async () => {
    try {
        const data = await SalesService.getSale(route.params.id);
        sale.value = data;
    } catch (error) {
        console.error('Ошибка загрузки продажи:', error);
    } finally {
        loading.value = false;
    }
});

const getInvoiceStatusLabel = (status) => {
    switch (status) {
        case 'CREATED': return 'Создан';
        case 'PAID': return 'Оплачен';
        case 'CANCELED': return 'Отменен';
        case 'OUTDATED': return 'Устарел';
        default: return status;
    }
};

const getInvoiceStatusSeverity = (status) => {
    switch (status) {
        case 'CREATED': return 'info';
        case 'PAID': return 'success';
        case 'CANCELED': return 'danger';
        case 'OUTDATED': return 'warning';
        default: return 'secondary';
    }
};

const getOrderStatusLabel = (status) => {
    switch (status) {
        case 'PENDING': return 'В ожидании';
        case 'COMPLETED': return 'Завершен';
        case 'CANCELED': return 'Отменен';
        default: return status;
    }
};

const getOrderStatusSeverity = (status) => {
    switch (status) {
        case 'PENDING': return 'warning';
        case 'COMPLETED': return 'success';
        case 'CANCELED': return 'danger';
        default: return 'secondary';
    }
};

const getCardTypeLabel = (type) => {
    switch (type) {
        case 'TRANSPORT': return 'Транспортная';
        case 'IPS': return 'IPS';
        default: return type;
    }
};

const formatCurrency = (amount) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount / 100); // Конвертируем из копеек в рубли
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('ru-RU');
};

const backToList = () => {
    router.push('/abt/sales');
};
</script>

<template>
    <div class="sales-detail">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Детали продажи</h1>
                <p class="text-color-secondary m-0">Информация о продаже</p>
            </div>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                @click="backToList"
                class="p-button-secondary"
            />
        </div>

        <div class="card" v-if="!loading && sale">
            <!-- Основная информация -->
            <div class="mb-4">
                <h3>Основная информация</h3>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold">ID счета</label>
                            <div class="text-lg">{{ sale.invoiceId }}</div>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold">ID заказа</label>
                            <div class="text-lg">{{ sale.orderId }}</div>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold">ID транзакции агента</label>
                            <div>{{ sale.agentTransactionId }}</div>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold">Регион</label>
                            <div>{{ sale.regionId }}</div>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold">Статус счета</label>
                            <Tag 
                                :value="getInvoiceStatusLabel(sale.invoiceStatus)" 
                                :severity="getInvoiceStatusSeverity(sale.invoiceStatus)" 
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold">Статус заказа</label>
                            <Tag 
                                :value="getOrderStatusLabel(sale.orderStatus)" 
                                :severity="getOrderStatusSeverity(sale.orderStatus)" 
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold">Сумма счета</label>
                            <div class="text-lg font-semibold">{{ formatCurrency(sale.invoiceAmount) }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Товары -->
            <div class="mb-4">
                <h3>Товары</h3>
                <div v-if="sale.items.length === 0" class="text-center p-4">
                    <i class="pi pi-info-circle text-4xl text-blue-500 mb-3"></i>
                    <h4>Товары не найдены</h4>
                    <p>В данной продаже нет товаров.</p>
                </div>
                
                <div v-else>
                    <DataTable 
                        :value="sale.items" 
                        :paginator="true" 
                        :rows="10" 
                        responsiveLayout="scroll"
                    >
                        <Column field="type" header="Тип" :sortable="true">
                            <template #body="{ data }">
                                <Tag 
                                    :value="data.type === 'PURCHASE' ? 'Покупка' : 'Пополнение'" 
                                    :severity="data.type === 'PURCHASE' ? 'success' : 'info'"
                                />
                            </template>
                        </Column>
                        
                        <Column field="serviceName" header="Услуга/Описание" :sortable="true">
                            <template #body="{ data }">
                                <div>
                                    <div class="font-semibold">{{ data.serviceName || data.description }}</div>
                                    <div v-if="data.descriptionText" class="text-sm text-color-secondary">
                                        {{ data.descriptionText }}
                                    </div>
                                </div>
                            </template>
                        </Column>
                        
                        <Column field="cost" header="Стоимость" :sortable="true">
                            <template #body="{ data }">
                                <div class="text-right">
                                    {{ formatCurrency(data.cost || data.replenishmentAmount) }}
                                </div>
                            </template>
                        </Column>
                        
                        <Column field="actionStartDate" header="Период действия" :sortable="true">
                            <template #body="{ data }">
                                <div v-if="data.actionStartDate && data.actionEndDate" class="text-sm">
                                    <div>С: {{ formatDateTime(data.actionStartDate) }}</div>
                                    <div>По: {{ formatDateTime(data.actionEndDate) }}</div>
                                </div>
                                <div v-else class="text-color-secondary">
                                    -
                                </div>
                            </template>
                        </Column>
                        
                        <Column field="createdAt" header="Дата создания" :sortable="true">
                            <template #body="{ data }">
                                <div class="text-sm">
                                    {{ formatDateTime(data.createdAt) }}
                                </div>
                            </template>
                        </Column>
                    </DataTable>
                </div>
            </div>

            <!-- Карты -->
            <div class="mb-4">
                <h3>Карты</h3>
                <div v-if="sale.cards.length === 0" class="text-center p-4">
                    <i class="pi pi-info-circle text-4xl text-blue-500 mb-3"></i>
                    <h4>Карты не найдены</h4>
                    <p>В данной продаже нет карт.</p>
                </div>
                
                <div v-else>
                    <DataTable 
                        :value="sale.cards" 
                        :paginator="true" 
                        :rows="10" 
                        responsiveLayout="scroll"
                    >
                        <Column field="cardType" header="Тип карты" :sortable="true">
                            <template #body="{ data }">
                                <Tag 
                                    :value="getCardTypeLabel(data.cardType)" 
                                    severity="info"
                                />
                            </template>
                        </Column>
                        
                        <Column field="pan" header="Номер карты" :sortable="true">
                            <template #body="{ data }">
                                <div>
                                    <div v-if="data.pan" class="font-semibold">{{ data.pan }}</div>
                                    <div v-else-if="data.panHash" class="text-sm text-color-secondary">
                                        Хэш: {{ data.panHash }}
                                    </div>
                                    <div v-else class="text-color-secondary">
                                        -
                                    </div>
                                </div>
                            </template>
                        </Column>
                        
                        <Column field="paymentSystem" header="Платежная система" :sortable="true">
                            <template #body="{ data }">
                                <div v-if="data.paymentSystem" class="font-semibold">
                                    {{ data.paymentSystem }}
                                </div>
                                <div v-else class="text-color-secondary">
                                    -
                                </div>
                            </template>
                        </Column>
                    </DataTable>
                </div>
            </div>

            <!-- Временная информация -->
            <div class="mb-4">
                <h3>Временная информация</h3>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold">Дата создания</label>
                            <div>{{ formatDateTime(sale.createdAt) }}</div>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-semibold">Дата обновления</label>
                            <div>{{ formatDateTime(sale.updatedAt) }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card" v-else-if="loading">
            <ProgressSpinner />
        </div>

        <div class="card" v-else>
            <div class="text-center">
                <i class="pi pi-exclamation-triangle text-4xl text-orange-500 mb-3"></i>
                <h3>Продажа не найдена</h3>
                <p>Запрашиваемая продажа не существует или была удалена.</p>
                <Button 
                    label="Вернуться к списку" 
                    icon="pi pi-arrow-left" 
                    @click="backToList"
                    class="mt-3"
                />
            </div>
        </div>
    </div>
</template>

<style scoped>
.sales-detail {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color-secondary);
}

.field div {
    color: var(--text-color);
}
</style> 