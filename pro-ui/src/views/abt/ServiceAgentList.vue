<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ServiceAgentService } from '@/service/ServiceAgentService';
import { ServiceService } from '@/service/ServiceService';
import { AgentService } from '@/service/AgentService';
import { useToast } from 'primevue/usetoast';

const router = useRouter();
const toast = useToast();

const serviceAgents = ref([]);
const services = ref([]);
const agents = ref([]);
const loading = ref(false);
const totalRecords = ref(0);
const currentPage = ref(0);
const deleteDialogVisible = ref(false);
const serviceAgentToDelete = ref(null);

const filters = ref({
    serviceId: null,
    agentId: null
});

const serviceOptions = computed(() => {
    return services.value.map(service => ({
        id: service.id,
        name: `${service.serviceCode} - ${service.name}`
    }));
});

const agentOptions = computed(() => {
    return agents.value.map(agent => ({
        id: agent.uuid, // Используем UUID как ID
        name: agent.name,
        code: agent.code,
        status: agent.status
    }));
});

onMounted(async () => {
    await Promise.all([
        loadServices(),
        loadAgents(),
        loadData()
    ]);
});

const loadServices = async () => {
    try {
        const response = await ServiceService.getServices();
        services.value = response.content || response;
    } catch (error) {
        console.error('Ошибка загрузки услуг:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить список услуг',
            life: 3000
        });
    }
};

const loadAgents = async () => {
    try {
        const agentsData = await AgentService.getAgents();
        agents.value = agentsData.map(agent => ({
            id: agent.uuid, // Используем UUID как ID
            name: agent.name,
            code: agent.code,
            status: agent.status
        }));
    } catch (error) {
        console.error('Ошибка загрузки агентов:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить список агентов',
            life: 3000
        });
    }
};

const loadData = async () => {
    loading.value = true;
    try {
        const response = await ServiceAgentService.getServiceAgents(currentPage.value, 20);
        serviceAgents.value = response.content || response;
        totalRecords.value = response.totalElements || response.length;
    } catch (error) {
        console.error('Ошибка загрузки связей услуг с агентами:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить связи услуг с агентами',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

const onPage = (event) => {
    currentPage.value = event.page;
    loadData();
};

const getServiceName = (serviceId) => {
    const service = services.value.find(s => s.id === serviceId);
    return service ? `${service.serviceCode} - ${service.name}` : serviceId;
};

const getAgentName = (agentId) => {
    const agent = agents.value.find(a => a.id === agentId);
    return agent ? agent.name : agentId;
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleString('ru-RU');
};

const createServiceAgent = () => {
    router.push('/abt/service-agents/create');
};

const editServiceAgent = (serviceAgent) => {
    router.push(`/abt/service-agents/edit/${serviceAgent.id}`);
};

const deleteServiceAgent = (serviceAgent) => {
    serviceAgentToDelete.value = serviceAgent;
    deleteDialogVisible.value = true;
};

const confirmDelete = async () => {
    if (!serviceAgentToDelete.value) return;

    try {
        await ServiceAgentService.deleteServiceAgent(serviceAgentToDelete.value.id);
        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Связь услуги с агентом удалена',
            life: 3000
        });
        deleteDialogVisible.value = false;
        serviceAgentToDelete.value = null;
        loadData();
    } catch (error) {
        console.error('Ошибка удаления связи услуги с агентом:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось удалить связь услуги с агентом',
            life: 3000
        });
    }
};
</script>

<template>
  <div class="service-agent-list">
    <div class="header">
      <h1>Связи услуг с агентами</h1>
      <Button 
        label="Создать связь" 
        icon="pi pi-plus" 
        @click="createServiceAgent"
        class="p-button-success"
      />
    </div>

    <div class="filters">
      <div class="filter-group">
        <label for="serviceFilter">Услуга:</label>
        <Dropdown
          id="serviceFilter"
          v-model="filters.serviceId"
          :options="serviceOptions"
          optionLabel="name"
          optionValue="id"
          placeholder="Выберите услугу"
          showClear
          @change="loadData"
        />
      </div>
      <div class="filter-group">
        <label for="agentFilter">Агент:</label>
        <Dropdown
          id="agentFilter"
          v-model="filters.agentId"
          :options="agentOptions"
          optionLabel="name"
          optionValue="id"
          placeholder="Выберите агента"
          showClear
          @change="loadData"
        />
      </div>
    </div>

    <DataTable
      :value="serviceAgents"
      :loading="loading"
      :paginator="true"
      :rows="20"
      :totalRecords="totalRecords"
      :lazy="true"
      @page="onPage"
      stripedRows
      responsiveLayout="scroll"
    >
      <Column field="serviceName" header="Услуга" sortable>
        <template #body="{ data }">
          {{ getServiceName(data.serviceId) }}
        </template>
      </Column>
      <Column field="agentName" header="Агент" sortable>
        <template #body="{ data }">
          {{ getAgentName(data.agentId) }}
        </template>
      </Column>
      <Column field="agentVersion" header="Версия агента" sortable>
        <template #body="{ data }">
          {{ data.agentVersion }}
        </template>
      </Column>
      <Column field="createdAt" header="Дата создания" sortable>
        <template #body="{ data }">
          {{ formatDate(data.createdAt) }}
        </template>
      </Column>
      <Column header="Действия" :exportable="false" style="min-width:8rem">
        <template #body="{ data }">
          <Button
            icon="pi pi-pencil"
            class="p-button-rounded p-button-success mr-2"
            @click="editServiceAgent(data)"
          />
          <Button
            icon="pi pi-trash"
            class="p-button-rounded p-button-warning"
            @click="deleteServiceAgent(data)"
          />
        </template>
      </Column>
    </DataTable>

    <Dialog
      v-model:visible="deleteDialogVisible"
      modal
      header="Подтверждение удаления"
      :style="{ width: '450px' }"
    >
      <div class="confirmation-content">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
        <span>Вы уверены, что хотите удалить эту связь услуги с агентом?</span>
      </div>
      <template #footer>
        <Button
          label="Нет"
          icon="pi pi-times"
          class="p-button-text"
          @click="deleteDialogVisible = false"
        />
        <Button
          label="Да"
          icon="pi pi-check"
          class="p-button-danger"
          @click="confirmDelete"
        />
      </template>
    </Dialog>
  </div>
</template>

<style scoped>
.service-agent-list {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  color: #333;
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.filter-group label {
  font-weight: 600;
  color: #495057;
}

.confirmation-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.p-datatable) {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

:deep(.p-datatable .p-datatable-header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

:deep(.p-datatable .p-datatable-tbody > tr:nth-child(even)) {
  background-color: #f8f9fa;
}

:deep(.p-button) {
  border-radius: 6px;
}

:deep(.p-dropdown) {
  min-width: 200px;
}
</style> 