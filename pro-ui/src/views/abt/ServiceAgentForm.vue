<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ServiceAgentService } from '@/service/ServiceAgentService';
import { ServiceService } from '@/service/ServiceService';
import { AgentService } from '@/service/AgentService';

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const services = ref([]);
const agents = ref([]);

const form = ref({
    serviceId: null,
    agentId: null,
    agentVersion: 1
});

const isEdit = computed(() => route.params.id !== 'create');

onMounted(async () => {
    try {
        // Загружаем списки услуг и агентов
        const [servicesResponse, agentsData] = await Promise.all([
            ServiceService.getServices(),
            AgentService.getAgents()
        ]);
        
        services.value = servicesResponse.content || servicesResponse;
        agents.value = agentsData.map(agent => ({
            id: agent.uuid, // Используем UUID как ID
            name: agent.name,
            code: agent.code,
            status: agent.status
        }));
        
        // Если это редактирование, загружаем данные
        if (isEdit.value) {
            loading.value = true;
            const serviceAgent = await ServiceAgentService.getServiceAgent(route.params.id);
            form.value = {
                serviceId: serviceAgent.serviceId,
                agentId: serviceAgent.agentId,
                agentVersion: serviceAgent.agentVersion
            };
        }
    } catch (error) {
        console.error('Ошибка загрузки данных:', error);
    } finally {
        loading.value = false;
    }
});

const save = async () => {
    try {
        loading.value = true;
        
        if (isEdit.value) {
            await ServiceAgentService.updateServiceAgent(route.params.id, form.value);
        } else {
            await ServiceAgentService.createServiceAgent(form.value);
        }
        
        router.push('/abt/service-agents');
    } catch (error) {
        console.error('Ошибка сохранения:', error);
    } finally {
        loading.value = false;
    }
};

const cancel = () => {
    router.push('/abt/service-agents');
};
</script>

<template>
    <div class="service-agent-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">
                    {{ isEdit ? 'Редактирование связки услуга-агент' : 'Создание связки услуга-агент' }}
                </h1>
                <p class="text-color-secondary m-0">
                    {{ isEdit ? 'Изменение параметров доступности услуги для агента' : 'Настройка доступности услуги для агента' }}
                </p>
            </div>
        </div>

        <div class="card">
            <form @submit.prevent="save">
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Услуга *</label>
                        <Dropdown 
                            v-model="form.serviceId" 
                            :options="services"
                            optionLabel="name"
                            optionValue="id"
                            placeholder="Выберите услугу"
                            class="w-full"
                            :class="{ 'p-invalid': !form.serviceId }"
                            required
                        />
                        <small v-if="!form.serviceId" class="p-error">Услуга обязательна для выбора</small>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label class="font-semibold">Агент *</label>
                        <Dropdown 
                            v-model="form.agentId" 
                            :options="agents"
                            optionLabel="name"
                            optionValue="id"
                            placeholder="Выберите агента"
                            class="w-full"
                            :class="{ 'p-invalid': !form.agentId }"
                            required
                        />
                        <small v-if="!form.agentId" class="p-error">Агент обязателен для выбора</small>
                    </div>
                </div>
                
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        type="button"
                        label="Отмена" 
                        icon="pi pi-times" 
                        @click="cancel"
                        class="p-button-secondary"
                    />
                    <Button 
                        type="submit"
                        label="Сохранить" 
                        icon="pi pi-check" 
                        :loading="loading"
                        class="p-button-success"
                    />
                </div>
            </form>
        </div>
    </div>
</template>

<style scoped>
.service-agent-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}
</style> 