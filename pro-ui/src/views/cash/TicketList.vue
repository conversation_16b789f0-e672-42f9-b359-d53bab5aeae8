<script setup>
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { TicketService } from '@/service/TicketService';

const tickets = ref([]);
const loading = ref(true);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    ticketNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
    tariffName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS },
    organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    passengerCategory: { value: null, matchMode: FilterMatchMode.EQUALS }
});

const statuses = ref([
    { label: 'Активный', value: 'active' },
    { label: 'Использован', value: 'used' },
    { label: 'Просрочен', value: 'expired' }
]);

const passengerCategories = ref([
    { label: 'Взрослый', value: 'adult' },
    { label: 'Студент', value: 'student' },
    { label: 'Пенсионер', value: 'pension' },
    { label: 'Школьник', value: 'school' }
]);

onMounted(() => {
    loadTickets();
});

const loadTickets = async () => {
    try {
        loading.value = true;
        const data = await TicketService.getTicketsByPaymentMethod('cash');
        tickets.value = data;
    } catch (error) {
        console.error('Ошибка загрузки билетов:', error);
        tickets.value = [];
    } finally {
        loading.value = false;
    }
};

const validateTicket = async (ticket) => {
    try {
        const result = await TicketService.validateTicket(ticket.ticketNumber);
        console.log('Результат валидации:', result.message);
    } catch (error) {
        console.error('Ошибка валидации билета:', error);
    }
};

const useTicket = async (ticket) => {
    if (confirm(`Вы уверены, что хотите использовать билет "${ticket.ticketNumber}"?`)) {
        try {
            const result = await TicketService.useTicket(ticket.ticketNumber);
            console.log('Билет использован:', result.message);
            await loadTickets();
        } catch (error) {
            console.error('Ошибка использования билета:', error);
        }
    }
};

const refundTicket = async (ticket) => {
    const reason = prompt('Укажите причину возврата:');
    if (reason) {
        try {
            const result = await TicketService.refundTicket(ticket.id, reason);
            console.log('Билет возвращен:', result.message);
            await loadTickets();
        } catch (error) {
            console.error('Ошибка возврата билета:', error);
        }
    }
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount);
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'used': return 'secondary';
        case 'expired': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'used': return 'Использован';
        case 'expired': return 'Просрочен';
        default: return 'Неизвестно';
    }
};

const getCategoryLabel = (category) => {
    switch (category) {
        case 'adult': return 'Взрослый';
        case 'student': return 'Студент';
        case 'pension': return 'Пенсионер';
        case 'school': return 'Школьник';
        default: return category;
    }
};

const getTicketTypeLabel = (type) => {
    switch (type) {
        case 'single': return 'Разовый';
        case 'daily': return 'Суточный';
        case 'weekly': return 'Недельный';
        case 'monthly': return 'Месячный';
        default: return type;
    }
};

const clearFilter = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        ticketNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
        tariffName: { value: null, matchMode: FilterMatchMode.CONTAINS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS },
        organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
        passengerCategory: { value: null, matchMode: FilterMatchMode.EQUALS }
    };
};
</script>

<template>
    <div class="ticket-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Билеты (наличные)</h1>
                <p class="text-color-secondary m-0">Билеты, купленные за наличные деньги</p>
            </div>
            <div class="flex gap-2">
                <Button 
                    label="Экспорт" 
                    icon="pi pi-download" 
                    outlined
                />
                <Button 
                    label="Обновить" 
                    icon="pi pi-refresh" 
                    @click="loadTickets"
                />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <div class="flex gap-2">
                    <Button 
                        type="button" 
                        icon="pi pi-filter-slash" 
                        label="Очистить" 
                        outlined 
                        @click="clearFilter"
                    />
                </div>
                <IconField>
                    <InputIcon>
                        <i class="pi pi-search" />
                    </InputIcon>
                    <InputText 
                        v-model="filters.global.value" 
                        placeholder="Поиск по всем полям" 
                    />
                </IconField>
            </div>
            
            <DataTable
                :value="tickets"
                :paginator="true"
                :rows="15"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['ticketNumber', 'tariffName', 'route', 'organizationName']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-ticket text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Билеты не найдены</p>
                    </div>
                </template>
                
                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>
                
                <Column field="ticketNumber" header="Номер билета" :sortable="true" style="min-width: 180px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по номеру" 
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.ticketNumber }}</span>
                    </template>
                </Column>
                
                <Column field="tariffName" header="Тариф" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по тарифу" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.tariffName }}</div>
                            <small class="text-color-secondary">{{ getTicketTypeLabel(data.ticketType) }}</small>
                        </div>
                    </template>
                </Column>
                
                <Column field="amount" header="Стоимость" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatAmount(data.amount) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="passengerCategory" header="Категория" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <Dropdown 
                            v-model="filterModel.value" 
                            :options="passengerCategories" 
                            optionLabel="label" 
                            optionValue="value"
                            placeholder="Выберите категорию" 
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag 
                            :value="getCategoryLabel(data.passengerCategory)" 
                            severity="info"
                        />
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <Dropdown 
                            v-model="filterModel.value" 
                            :options="statuses" 
                            optionLabel="label" 
                            optionValue="value"
                            placeholder="Выберите статус" 
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column field="route" header="Маршрут" :sortable="true" style="min-width: 150px">
                    <template #body="{ data }">
                        {{ data.route }}
                    </template>
                </Column>
                
                <Column field="purchaseDate" header="Дата покупки" :sortable="true" style="min-width: 180px">
                    <template #body="{ data }">
                        {{ formatDate(data.purchaseDate) }}
                    </template>
                </Column>
                
                <Column field="organizationName" header="Организация" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по организации" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-building mr-2 text-color-secondary"></i>
                            <span>{{ data.organizationName }}</span>
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" style="min-width: 180px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                icon="pi pi-check-circle" 
                                size="small" 
                                text 
                                @click="validateTicket(data)"
                                v-tooltip.top="'Проверить'"
                            />
                            <Button 
                                v-if="data.status === 'active'"
                                icon="pi pi-play" 
                                size="small" 
                                text 
                                severity="success"
                                @click="useTicket(data)"
                                v-tooltip.top="'Использовать'"
                            />
                            <Button 
                                v-if="data.status === 'active'"
                                icon="pi pi-undo" 
                                size="small" 
                                text 
                                severity="warning"
                                @click="refundTicket(data)"
                                v-tooltip.top="'Вернуть'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.ticket-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
