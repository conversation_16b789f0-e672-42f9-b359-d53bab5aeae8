<script setup>
import { ref, onMounted } from 'vue';
import { TicketService } from '@/service/TicketService';

const tickets = ref([]);
const loading = ref(true);

onMounted(async () => {
    try {
        const data = await TicketService.getTicketsByPaymentMethod('cbt');
        tickets.value = data;
    } catch (error) {
        console.error('Ошибка загрузки билетов:', error);
    } finally {
        loading.value = false;
    }
});

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount);
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'used': return 'secondary';
        case 'expired': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'used': return 'Использован';
        case 'expired': return 'Просрочен';
        default: return status;
    }
};
</script>

<template>
    <div class="ticket-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold m-0 mb-2">Билеты (Тройка)</h1>
                <p class="text-color-secondary m-0">Билеты, купленные картами Тройка</p>
            </div>
            <Button label="Обновить" icon="pi pi-refresh" />
        </div>

        <div class="card">
            <DataTable :value="tickets" :loading="loading" :paginator="true" :rows="15" responsiveLayout="scroll">
                <Column field="ticketNumber" header="Номер билета" :sortable="true">
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.ticketNumber }}</span>
                    </template>
                </Column>
                
                <Column field="tariffName" header="Тариф" :sortable="true">
                    <template #body="{ data }">
                        {{ data.tariffName }}
                    </template>
                </Column>
                
                <Column field="amount" header="Стоимость" :sortable="true">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatAmount(data.amount) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true">
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column field="purchaseDate" header="Дата покупки" :sortable="true">
                    <template #body="{ data }">
                        {{ formatDate(data.purchaseDate) }}
                    </template>
                </Column>
                
                <Column header="Карта Тройка">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div>{{ data.troikaCardNumber }}</div>
                            <div v-if="data.balanceBefore">
                                {{ formatAmount(data.balanceBefore) }} → {{ formatAmount(data.balanceAfter) }}
                            </div>
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.ticket-list {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}
.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
