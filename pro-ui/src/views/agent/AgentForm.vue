<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { AgentService } from '@/service/AgentService';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();

const agentId = route.params.agentId;
const isEdit = computed(() => !!agentId);

const loading = ref(false);
const saving = ref(false);
const organizations = ref([]);

const form = ref({
    name: '',
    code: '',
    type: 'legal_entity',
    inn: '',
    kpp: '',
    ogrn: '',
    legalAddress: '',
    actualAddress: '',
    contactPerson: '',
    phone: '',
    email: '',
    organizationId: null,
    serviceTypes: [],
    commissionRate: 2.5,
    contractNumber: '',
    contractDate: '',
    description: ''
});

const errors = ref({});

const agentTypes = ref([
    { label: 'Юридическое лицо', value: 'legal_entity' },
    { label: 'Индивидуальный предприниматель', value: 'individual' }
]);

const serviceTypeOptions = ref([
    { label: 'Продажа карт', value: 'card_sales' },
    { label: 'Пополнение карт', value: 'card_refill' },
    { label: 'Продажа абонементов', value: 'subscription_sales' },
    { label: 'Мобильное приложение', value: 'mobile_app' },
    { label: 'Онлайн пополнение', value: 'online_refill' },
    { label: 'Обслуживание терминалов', value: 'terminal_maintenance' },
    { label: 'Клиентский сервис', value: 'customer_service' }
]);

onMounted(async () => {
    await loadOrganizations();
    if (isEdit.value) {
        await loadAgent();
    } else {
        // Установим дефолтную дату договора
        const now = new Date();
        form.value.contractDate = now.toISOString().split('T')[0];
    }
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations();
        organizations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
    }
};

const loadAgent = async () => {
    try {
        loading.value = true;
        const agent = await AgentService.getAgentById(agentId);
        if (agent) {
            form.value = { 
                ...agent,
                contractDate: agent.contractDate ? agent.contractDate.split('T')[0] : ''
            };
        }
    } catch (error) {
        console.error('Ошибка загрузки агента:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    if (!form.value.name.trim()) {
        errors.value.name = 'Наименование обязательно';
    }
    
    if (!form.value.code.trim()) {
        errors.value.code = 'Код обязателен';
    }
    
    if (!form.value.inn.trim()) {
        errors.value.inn = 'ИНН обязателен';
    } else if (!/^\d{10}$|^\d{12}$/.test(form.value.inn)) {
        errors.value.inn = 'ИНН должен содержать 10 или 12 цифр';
    }
    
    if (form.value.type === 'legal_entity' && !form.value.kpp.trim()) {
        errors.value.kpp = 'КПП обязателен для юридических лиц';
    } else if (form.value.kpp && !/^\d{9}$/.test(form.value.kpp)) {
        errors.value.kpp = 'КПП должен содержать 9 цифр';
    }
    
    if (!form.value.organizationId) {
        errors.value.organizationId = 'Организация обязательна';
    }
    
    if (!form.value.contactPerson.trim()) {
        errors.value.contactPerson = 'Контактное лицо обязательно';
    }
    
    if (!form.value.phone.trim()) {
        errors.value.phone = 'Телефон обязателен';
    }
    
    if (!form.value.email.trim()) {
        errors.value.email = 'Email обязателен';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
        errors.value.email = 'Некорректный формат email';
    }
    
    if (form.value.commissionRate < 0 || form.value.commissionRate > 100) {
        errors.value.commissionRate = 'Комиссия должна быть от 0 до 100%';
    }
    
    return Object.keys(errors.value).length === 0;
};

const saveAgent = async () => {
    if (!validateForm()) {
        return;
    }
    
    try {
        saving.value = true;
        
        const agentData = {
            ...form.value,
            contractDate: form.value.contractDate ? form.value.contractDate + 'T00:00:00Z' : null
        };
        
        if (isEdit.value) {
            await AgentService.updateAgent(agentId, agentData);
        } else {
            await AgentService.createAgent(agentData);
        }
        
        router.push('/agent/registry');
        
    } catch (error) {
        console.error('Ошибка сохранения агента:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push('/agent/registry');
};

const generateCode = () => {
    if (form.value.name) {
        const code = form.value.name
            .toUpperCase()
            .replace(/[^А-ЯA-Z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 15);
        form.value.code = `AG_${code}`;
    }
};
</script>

<template>
    <div class="agent-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование агента' : 'Создание агента' }}
            </h1>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="saveAgent">
                <div class="grid">
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-8">
                        <div class="field">
                            <label for="name" class="font-medium">Наименование *</label>
                            <InputText 
                                id="name"
                                v-model="form.name" 
                                :class="{ 'p-invalid': errors.name }"
                                placeholder="ООО 'Агент Плюс'"
                                class="w-full"
                                @input="generateCode"
                            />
                            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="code" class="font-medium">Код *</label>
                            <InputText 
                                id="code"
                                v-model="form.code" 
                                :class="{ 'p-invalid': errors.code }"
                                placeholder="AG_AGENT_PLUS"
                                class="w-full"
                            />
                            <small v-if="errors.code" class="p-error">{{ errors.code }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="type" class="font-medium">Тип агента *</label>
                            <Dropdown 
                                id="type"
                                v-model="form.type" 
                                :options="agentTypes" 
                                optionLabel="label" 
                                optionValue="value"
                                placeholder="Выберите тип"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="organizationId" class="font-medium">Организация *</label>
                            <Dropdown 
                                id="organizationId"
                                v-model="form.organizationId" 
                                :options="organizations" 
                                optionLabel="name" 
                                optionValue="id"
                                placeholder="Выберите организацию"
                                :class="{ 'p-invalid': errors.organizationId }"
                                class="w-full"
                            />
                            <small v-if="errors.organizationId" class="p-error">{{ errors.organizationId }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Реквизиты</h3>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="inn" class="font-medium">ИНН *</label>
                            <InputText 
                                id="inn"
                                v-model="form.inn" 
                                :class="{ 'p-invalid': errors.inn }"
                                placeholder="1234567890"
                                class="w-full"
                            />
                            <small v-if="errors.inn" class="p-error">{{ errors.inn }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4" v-if="form.type === 'legal_entity'">
                        <div class="field">
                            <label for="kpp" class="font-medium">КПП *</label>
                            <InputText 
                                id="kpp"
                                v-model="form.kpp" 
                                :class="{ 'p-invalid': errors.kpp }"
                                placeholder="123456789"
                                class="w-full"
                            />
                            <small v-if="errors.kpp" class="p-error">{{ errors.kpp }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="ogrn" class="font-medium">ОГРН</label>
                            <InputText 
                                id="ogrn"
                                v-model="form.ogrn" 
                                placeholder="1234567890123"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="contactPerson" class="font-medium">Контактное лицо *</label>
                            <InputText 
                                id="contactPerson"
                                v-model="form.contactPerson" 
                                :class="{ 'p-invalid': errors.contactPerson }"
                                placeholder="Иванов И.И."
                                class="w-full"
                            />
                            <small v-if="errors.contactPerson" class="p-error">{{ errors.contactPerson }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="phone" class="font-medium">Телефон *</label>
                            <InputText 
                                id="phone"
                                v-model="form.phone" 
                                :class="{ 'p-invalid': errors.phone }"
                                placeholder="+7 (495) 123-45-67"
                                class="w-full"
                            />
                            <small v-if="errors.phone" class="p-error">{{ errors.phone }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="email" class="font-medium">Email *</label>
                            <InputText 
                                id="email"
                                v-model="form.email" 
                                :class="{ 'p-invalid': errors.email }"
                                placeholder="<EMAIL>"
                                class="w-full"
                            />
                            <small v-if="errors.email" class="p-error">{{ errors.email }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="commissionRate" class="font-medium">Комиссия (%)</label>
                            <InputNumber 
                                id="commissionRate"
                                v-model="form.commissionRate" 
                                :class="{ 'p-invalid': errors.commissionRate }"
                                :min="0" 
                                :max="100"
                                :minFractionDigits="1"
                                :maxFractionDigits="2"
                                class="w-full"
                            />
                            <small v-if="errors.commissionRate" class="p-error">{{ errors.commissionRate }}</small>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        label="Отмена" 
                        icon="pi pi-times" 
                        outlined 
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>
        
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных агента...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.agent-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}
</style>
