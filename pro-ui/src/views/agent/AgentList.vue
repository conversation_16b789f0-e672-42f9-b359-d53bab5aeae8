<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { FilterMatchMode } from '@primevue/core/api';
import { AgentService } from '@/service/AgentService';

const router = useRouter();

const agents = ref([]);
const loading = ref(true);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    code: { value: null, matchMode: FilterMatchMode.CONTAINS },
    type: { value: null, matchMode: FilterMatchMode.EQUALS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS },
    organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const agentTypes = ref([
    { label: 'Юридическое лицо', value: 'legal_entity' },
    { label: 'Индивидуальный предприниматель', value: 'individual' }
]);

const statuses = ref([
    { label: 'Активный', value: 'active' },
    { label: 'Приостановлен', value: 'suspended' },
    { label: 'Черновик', value: 'draft' }
]);

onMounted(() => {
    loadAgents();
});

const loadAgents = async () => {
    try {
        loading.value = true;
        const data = await AgentService.getAgents();
        agents.value = data;
    } catch (error) {
        console.error('Ошибка загрузки агентов:', error);
        agents.value = [];
    } finally {
        loading.value = false;
    }
};

const createAgent = () => {
    router.push('/agent/registry/create');
};

const viewAgent = (agent) => {
    router.push(`/agent/registry/${agent.id}`);
};

const editAgent = (agent) => {
    router.push(`/agent/registry/${agent.id}/edit`);
};

const deleteAgent = async (agent) => {
    if (confirm(`Вы уверены, что хотите удалить агента "${agent.name}"?`)) {
        try {
            await AgentService.deleteAgent(agent.id);
            console.log('Агент удален:', agent.id);
            await loadAgents();
        } catch (error) {
            console.error('Ошибка удаления агента:', error);
        }
    }
};

const activateAgent = async (agent) => {
    if (confirm(`Вы уверены, что хотите активировать агента "${agent.name}"?`)) {
        try {
            const result = await AgentService.activateAgent(agent.id);
            console.log('Агент активирован:', result.message);
            await loadAgents();
        } catch (error) {
            console.error('Ошибка активации агента:', error);
        }
    }
};

const suspendAgent = async (agent) => {
    const reason = prompt('Укажите причину приостановки:');
    if (reason) {
        try {
            const result = await AgentService.suspendAgent(agent.id, reason);
            console.log('Агент приостановлен:', result.message);
            await loadAgents();
        } catch (error) {
            console.error('Ошибка приостановки агента:', error);
        }
    }
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'suspended': return 'danger';
        case 'draft': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'suspended': return 'Приостановлен';
        case 'draft': return 'Черновик';
        default: return 'Неизвестно';
    }
};

const getTypeLabel = (type) => {
    switch (type) {
        case 'legal_entity': return 'Юр. лицо';
        case 'individual': return 'ИП';
        default: return type;
    }
};

const getServiceTypesLabels = (serviceTypes) => {
    const labels = {
        'card_sales': 'Продажа карт',
        'card_refill': 'Пополнение карт',
        'subscription_sales': 'Продажа абонементов',
        'mobile_app': 'Мобильное приложение',
        'online_refill': 'Онлайн пополнение',
        'terminal_maintenance': 'Обслуживание терминалов',
        'customer_service': 'Клиентский сервис'
    };
    
    return serviceTypes.map(type => labels[type] || type).join(', ');
};

const clearFilter = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        code: { value: null, matchMode: FilterMatchMode.CONTAINS },
        type: { value: null, matchMode: FilterMatchMode.EQUALS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS },
        organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};
</script>

<template>
    <div class="agent-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Реестр агентов</h1>
            <Button 
                label="Добавить агента" 
                icon="pi pi-plus" 
                @click="createAgent"
            />
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <div class="flex gap-2">
                    <Button 
                        type="button" 
                        icon="pi pi-filter-slash" 
                        label="Очистить" 
                        outlined 
                        @click="clearFilter"
                    />
                </div>
                <IconField>
                    <InputIcon>
                        <i class="pi pi-search" />
                    </InputIcon>
                    <InputText 
                        v-model="filters.global.value" 
                        placeholder="Поиск по всем полям" 
                    />
                </IconField>
            </div>
            
            <DataTable
                :value="agents"
                :paginator="true"
                :rows="15"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['name', 'code', 'inn', 'contactPerson', 'organizationName']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-users text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Агенты не найдены</p>
                        <Button 
                            label="Добавить первого агента" 
                            icon="pi pi-plus" 
                            class="mt-3"
                            @click="createAgent"
                        />
                    </div>
                </template>
                
                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>
                
                <Column field="name" header="Наименование" :sortable="true" style="min-width: 250px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по наименованию" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div>
                            <div class="flex align-items-center mb-1">
                                <Tag 
                                    :value="getTypeLabel(data.type)" 
                                    :severity="data.type === 'legal_entity' ? 'info' : 'warning'"
                                    class="mr-2"
                                />
                                <span class="font-semibold">{{ data.name }}</span>
                            </div>
                            <small class="text-color-secondary">{{ data.contactPerson }}</small>
                        </div>
                    </template>
                </Column>
                
                <Column field="code" header="Код" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по коду" 
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.code }}</span>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <Dropdown 
                            v-model="filterModel.value" 
                            :options="statuses" 
                            optionLabel="label" 
                            optionValue="value"
                            placeholder="Выберите статус" 
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column header="Точки обслуживания" style="min-width: 150px">
                    <template #body="{ data }">
                        <div class="text-center">
                            <div class="font-semibold">{{ data.activeServicePoints }} / {{ data.totalServicePoints }}</div>
                            <small class="text-color-secondary">активных / всего</small>
                        </div>
                    </template>
                </Column>
                
                <Column header="Услуги" style="min-width: 200px">
                    <template #body="{ data }">
                        <div class="text-sm">
                            {{ getServiceTypesLabels(data.serviceTypes) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="monthlyTurnover" header="Оборот в месяц" :sortable="true" style="min-width: 150px">
                    <template #body="{ data }">
                        <div class="text-right font-semibold">
                            {{ formatAmount(data.monthlyTurnover) }}
                        </div>
                    </template>
                </Column>
                
                <Column field="commissionRate" header="Комиссия" :sortable="true" style="min-width: 100px">
                    <template #body="{ data }">
                        <div class="text-center font-semibold">
                            {{ data.commissionRate }}%
                        </div>
                    </template>
                </Column>
                
                <Column field="organizationName" header="Организация" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по организации" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-building mr-2 text-color-secondary"></i>
                            <span>{{ data.organizationName }}</span>
                        </div>
                    </template>
                </Column>
                
                <Column header="Действия" style="min-width: 200px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                icon="pi pi-eye" 
                                size="small" 
                                text 
                                @click="viewAgent(data)"
                                v-tooltip.top="'Просмотр'"
                            />
                            <Button 
                                icon="pi pi-pencil" 
                                size="small" 
                                text 
                                @click="editAgent(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button 
                                v-if="data.status === 'draft'"
                                icon="pi pi-check" 
                                size="small" 
                                text 
                                severity="success"
                                @click="activateAgent(data)"
                                v-tooltip.top="'Активировать'"
                            />
                            <Button 
                                v-if="data.status === 'active'"
                                icon="pi pi-pause" 
                                size="small" 
                                text 
                                severity="warning"
                                @click="suspendAgent(data)"
                                v-tooltip.top="'Приостановить'"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                size="small" 
                                text 
                                severity="danger" 
                                @click="deleteAgent(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.agent-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
