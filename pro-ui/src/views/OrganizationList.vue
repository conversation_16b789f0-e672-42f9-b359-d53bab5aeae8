<script setup>
import { useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { OrganizationService } from '@/service/OrganizationService';

const router = useRouter();

const organizations = ref([]);
const loading = ref(true);
const syncing = ref(false);
const syncingId = ref(null);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    inn: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
});

const statuses = ref([
    { label: 'Активная', value: 'active' },
    { label: 'Неактивная', value: 'inactive' }
]);

const syncStatuses = ref([
    { label: 'Синхронизирована', value: 'synced' },
    { label: 'Ожидает синхронизации', value: 'pending' },
    { label: 'Ошибка синхронизации', value: 'error' },
    { label: 'Не синхронизировалась', value: 'never' }
]);

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        inn: { value: null, matchMode: FilterMatchMode.CONTAINS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS }
    };
};

onMounted(() => {
    loadOrganizations();
});

const loadOrganizations = async () => {
    try {
        loading.value = true;
        const data = await OrganizationService.getOrganizations();
        organizations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const createOrganization = () => {
    router.push('/organizations/create');
};

const viewOrganization = (organization) => {
    router.push(`/organizations/${organization.id}`);
};

const editOrganization = (organization) => {
    router.push(`/organizations/${organization.id}/edit`);
};

const deleteOrganization = async (organization) => {
    if (confirm(`Вы уверены, что хотите удалить организацию "${organization.name}"?`)) {
        try {
            await OrganizationService.deleteOrganization(organization.id);
            console.log('Организация удалена:', organization.id);
            await loadOrganizations();
        } catch (error) {
            console.error('Ошибка удаления организации:', error);
        }
    }
};

const syncOrganization = async (organization) => {
    try {
        syncingId.value = organization.id;
        const result = await OrganizationService.syncOrganization(organization.id);

        if (result.success) {
            // Обновляем статус синхронизации в локальных данных
            const org = organizations.value.find(o => o.id === organization.id);
            if (org) {
                org.syncStatus = 'synced';
                org.lastSyncDate = result.syncDate;
            }
            console.log('Синхронизация успешна:', result.message);
        } else {
            console.error('Ошибка синхронизации:', result.message);
        }
    } catch (error) {
        console.error('Ошибка синхронизации:', error);
    } finally {
        syncingId.value = null;
    }
};

const syncAllOrganizations = async () => {
    try {
        syncing.value = true;
        const result = await OrganizationService.syncAllOrganizations();

        console.log('Массовая синхронизация завершена:', result.message);

        // Перезагружаем список после массовой синхронизации
        await loadOrganizations();
    } catch (error) {
        console.error('Ошибка массовой синхронизации:', error);
    } finally {
        syncing.value = false;
    }
};

const importFromFile = () => {
    console.log('Загрузить организации из файла');
};

const exportData = () => {
    console.log('Экспорт организаций');
};

const getStatusSeverity = (status) => {
    return status === 'active' ? 'success' : 'secondary';
};

const getStatusLabel = (status) => {
    return status === 'active' ? 'Активная' : 'Неактивная';
};

const getSyncStatusSeverity = (syncStatus) => {
    switch (syncStatus) {
        case 'synced': return 'success';
        case 'pending': return 'warning';
        case 'error': return 'danger';
        case 'never': return 'secondary';
        default: return 'secondary';
    }
};

const getSyncStatusLabel = (syncStatus) => {
    switch (syncStatus) {
        case 'synced': return 'Синхронизирована';
        case 'pending': return 'Ожидает';
        case 'error': return 'Ошибка';
        case 'never': return 'Не синхронизировалась';
        default: return 'Неизвестно';
    }
};

const formatDate = (dateString) => {
    if (!dateString) return 'Никогда';
    return new Date(dateString).toLocaleString('ru-RU');
};
</script>

<template>
    <div class="organization-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Организации</h1>
            <div class="flex gap-2">
                <Button
                    label="Синхронизация с 1С"
                    icon="pi pi-refresh"
                    outlined
                    :loading="syncing"
                    @click="syncAllOrganizations"
                />
                <Button
                    label="Загрузить из файла"
                    icon="pi pi-upload"
                    outlined
                    @click="importFromFile"
                />
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <Button
                    label="Создать"
                    icon="pi pi-plus"
                    @click="createOrganization"
                />
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="organizations"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['name', 'shortName', 'inn', 'director']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Организации не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="name" header="Наименование" :sortable="true" style="min-width: 300px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по наименованию"
                        />
                    </template>
                    <template #body="{ data }">
                        <div>
                            <div class="flex align-items-center mb-1">
                                <Tag
                                    :value="data.type === 'organization' ? 'Организация' : 'ИП'"
                                    :severity="data.type === 'organization' ? 'info' : 'warning'"
                                    class="mr-2"
                                />
                                <span class="font-semibold">{{ data.ownershipForm }}</span>
                            </div>
                            <div class="font-semibold">{{ data.name }}</div>
                            <small class="text-color-secondary">{{ data.shortName }}</small>
                        </div>
                    </template>
                </Column>

                <Column field="inn" header="ИНН" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по ИНН"
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-mono">{{ data.inn }}</span>
                    </template>
                </Column>

                <Column field="ogrn" header="ОГРН/ОГРНИП" :sortable="true" style="min-width: 150px">
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.ogrn }}</span>
                    </template>
                </Column>

                <Column field="director" header="Руководитель" :sortable="true" style="min-width: 200px">
                </Column>

                <Column field="status" header="Статус" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <Dropdown
                            v-model="filterModel.value"
                            :options="statuses"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Выберите статус"
                            class="p-column-filter"
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag
                            :value="getStatusLabel(data.status)"
                            :severity="getStatusSeverity(data.status)"
                        />
                    </template>
                </Column>

                <Column header="Синхронизация с 1С" style="min-width: 180px">
                    <template #body="{ data }">
                        <div>
                            <Tag
                                :value="getSyncStatusLabel(data.syncStatus)"
                                :severity="getSyncStatusSeverity(data.syncStatus)"
                                class="mb-1"
                            />
                            <div>
                                <small class="text-color-secondary">
                                    {{ formatDate(data.lastSyncDate) }}
                                </small>
                            </div>
                        </div>
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 150px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button
                                icon="pi pi-refresh"
                                size="small"
                                text
                                :loading="syncingId === data.id"
                                @click="syncOrganization(data)"
                                v-tooltip.top="'Синхронизировать с 1С'"
                            />
                            <Button
                                icon="pi pi-eye"
                                size="small"
                                text
                                @click="viewOrganization(data)"
                                v-tooltip.top="'Просмотр'"
                            />
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editOrganization(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="deleteOrganization(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.organization-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
