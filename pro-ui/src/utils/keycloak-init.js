// Утилита для инициализации Keycloak
let Keycloak = null;

export const initKeycloak = async () => {
    if (Keycloak) {
        return Keycloak;
    }
    
    try {
        const keycloakModule = await import('keycloak-js');
        Keycloak = keycloakModule.default || keycloakModule;
        return Keycloak;
    } catch (error) {
        console.error('Failed to import Keycloak:', error);
        throw new Error('Keycloak library could not be loaded');
    }
};

export const getKeycloakInstance = () => Keycloak; 