/**
 * Утилита для получения JWT токена из auth store
 */

let authStore = null;

/**
 * Инициализировать auth store
 */
export const initAuthStore = () => {
    if (typeof window !== 'undefined') {
        // Динамический импорт для избежания циклических зависимостей
        import('@/stores/auth').then(({ useAuthStore }) => {
            authStore = useAuthStore();
        }).catch(error => {
            console.warn('Could not import auth store:', error);
        });
    }
};

/**
 * Получить JWT токен
 * @returns {string|null} JWT токен или null
 */
export const getAuthToken = () => {
    if (authStore) {
        return authStore.getToken;
    }
    return null;
};

/**
 * Проверить, авторизован ли пользователь
 * @returns {boolean}
 */
export const isAuthenticated = () => {
    if (authStore) {
        return authStore.isAuthenticated;
    }
    return false;
};

// Инициализируем store при загрузке модуля
if (typeof window !== 'undefined') {
    // Ждем, пока DOM загрузится
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAuthStore);
    } else {
        initAuthStore();
    }
} 