/**
 * Сервис для работы с продажами
 */
import { sbolApiClient } from './ApiClient.js';

export class SalesService {
    
    /**
     * Получить список продаж
     * @param {Object} filters - Фильтры для поиска
     * @returns {Promise<Array>} Список продаж
     */
    static async getSales(filters = {}) {
        try {
            const params = new URLSearchParams();
            
            if (filters.search) params.append('search', filters.search);
            if (filters.invoiceStatus) params.append('invoiceStatus', filters.invoiceStatus);
            if (filters.orderStatus) params.append('orderStatus', filters.orderStatus);
            if (filters.regionId) params.append('regionId', filters.regionId);
            if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
            if (filters.dateTo) params.append('dateTo', filters.dateTo);
            
            params.append('page', '0');
            params.append('size', '100'); // Получаем больше данных для клиентской фильтрации
            
            const data = await sbolApiClient.get(`/api/v1/abt/sbol/sales?${params.toString()}`);
            
            console.log('Ответ от API:', data); // Добавляем логирование
            return data.content || [];
        } catch (error) {
            console.error('Ошибка получения продаж:', error);
            return [];
        }
    }
    
    /**
     * Получить продажу по ID
     * @param {string} id - ID продажи (invoiceId)
     * @returns {Promise<Object>} Продажа
     */
    static async getSale(id) {
        try {
            const data = await sbolApiClient.get(`/api/v1/abt/sbol/sales/${id}`);
            return data;
        } catch (error) {
            console.error('Ошибка получения продажи:', error);
            return null;
        }
    }
    
    /**
     * Получить статусы счетов
     * @returns {Promise<Array>} Список статусов
     */
    static async getInvoiceStatuses() {
        try {
            const data = await sbolApiClient.get('/api/sales/invoice-statuses');
            console.log('Статусы счетов:', data); // Добавляем логирование
            return data;
        } catch (error) {
            console.error('Ошибка получения статусов счетов:', error);
            return [];
        }
    }
    
    /**
     * Получить статусы заказов
     * @returns {Promise<Array>} Список статусов
     */
    static async getOrderStatuses() {
        try {
            const data = await sbolApiClient.get('/api/sales/order-statuses');
            console.log('Статусы заказов:', data); // Добавляем логирование
            return data;
        } catch (error) {
            console.error('Ошибка получения статусов заказов:', error);
            return [];
        }
    }
    
    /**
     * Получить типы карт
     * @returns {Promise<Array>} Список типов
     */
    static async getCardTypes() {
        try {
            const data = await sbolApiClient.get('/api/sales/card-types');
            return data;
        } catch (error) {
            console.error('Ошибка получения типов карт:', error);
            return [];
        }
    }
    
    /**
     * Получить платежные системы
     * @returns {Promise<Array>} Список систем
     */
    static async getPaymentSystems() {
        try {
            const data = await sbolApiClient.get('/api/sales/payment-systems');
            return data;
        } catch (error) {
            console.error('Ошибка получения платежных систем:', error);
            return [];
        }
    }
} 