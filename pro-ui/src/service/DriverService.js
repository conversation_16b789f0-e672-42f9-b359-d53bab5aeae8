export const DriverService = {
    getData: () => {
        return [
            {
                id: 1,
                personnelNumber: 'D001',
                fullName: '<PERSON><PERSON><PERSON><PERSON><PERSON> И<PERSON>а<PERSON>',
                pinCode: '1234',
                cardPan: '1234567890123456',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"'
            },
            {
                id: 2,
                personnelNumber: 'D002',
                fullName: 'Петров Петр Петрович',
                pinCode: '5678',
                cardPan: '2345678901234567',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"'
            },
            {
                id: 3,
                personnelNumber: 'D003',
                fullName: 'Сидоров Сидор Сидорович',
                pinCode: '9012',
                cardPan: '3456789012345678',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"'
            },
            {
                id: 4,
                personnelNumber: 'D004',
                fullName: 'Козлов Андрей Владими<PERSON>ович',
                pinCode: '3456',
                cardPan: '****************',
                organizationId: 3,
                organizationName: 'ООО "Автобусный парк №1"'
            },
            {
                id: 5,
                personnelNumber: 'D005',
                fullName: 'Морозов Дмитрий Александрович',
                pinCode: '7890',
                cardPan: '5678901234567890',
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"'
            },
            {
                id: 6,
                personnelNumber: 'D006',
                fullName: 'Волков Сергей Николаевич',
                pinCode: '2468',
                cardPan: '6789012345678901',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"'
            },
            {
                id: 7,
                personnelNumber: 'D007',
                fullName: 'Лебедев Михаил Игоревич',
                pinCode: '1357',
                cardPan: '7890123456789012',
                organizationId: 7,
                organizationName: 'ИП Лебедев М.И.'
            },
            {
                id: 8,
                personnelNumber: 'D008',
                fullName: 'Новиков Алексей Викторович',
                pinCode: '9753',
                cardPan: '8901234567890123',
                organizationId: 8,
                organizationName: 'ООО "Грузоперевозки Север"'
            },
            {
                id: 9,
                personnelNumber: 'D009',
                fullName: 'Федоров Владимир Сергеевич',
                pinCode: '8642',
                cardPan: '9012345678901234',
                organizationId: 6,
                organizationName: 'ООО "Электротранс"'
            },
            {
                id: 10,
                personnelNumber: 'D010',
                fullName: 'Соколов Артем Дмитриевич',
                pinCode: '1593',
                cardPan: '0123456789012345',
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"'
            },
            {
                id: 11,
                personnelNumber: 'D011',
                fullName: 'Павлов Максим Андреевич',
                pinCode: '7531',
                cardPan: '1357924680135792',
                organizationId: 5,
                organizationName: 'ООО "Такси Комфорт"'
            },
            {
                id: 12,
                personnelNumber: 'D012',
                fullName: 'Семенов Роман Олегович',
                pinCode: '9517',
                cardPan: '2468135790246813',
                organizationId: 3,
                organizationName: 'ООО "Автобусный парк №1"'
            }
        ]
    },

    getDrivers() {
        return Promise.resolve(this.getData());
    },

    getDriversByProject(projectCode) {
        // В реальном приложении здесь будет фильтрация по проекту
        return Promise.resolve(this.getData());
    },

    getDriverById(driverId) {
        const drivers = this.getData();
        const driver = drivers.find(d => d.id == driverId);
        return Promise.resolve(driver);
    },

    createDriver(projectCode, driverData) {
        // В реальном приложении здесь будет отправка POST запроса
        console.log('Creating driver for project:', projectCode, driverData);

        const newDriver = {
            ...driverData,
            id: Date.now() // Временный ID для демонстрации
        };

        return Promise.resolve(newDriver);
    },

    updateDriver(driverId, driverData) {
        // В реальном приложении здесь будет отправка PUT запроса
        console.log('Updating driver:', driverId, driverData);

        const updatedDriver = {
            ...driverData,
            id: driverId
        };

        return Promise.resolve(updatedDriver);
    },

    deleteDriver(driverId) {
        // В реальном приложении здесь будет отправка DELETE запроса
        console.log('Deleting driver:', driverId);
        return Promise.resolve({ success: true });
    }
}
