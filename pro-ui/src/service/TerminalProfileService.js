import { tmsApiClient } from './ApiClient.js';

export const TerminalProfileService = {
    // Получить все профили терминалов
    async getTerminalProfiles() {
        try {
            const response = await tmsApiClient.get('/api/v1/tms/terminal-profiles');
            return response || [];
        } catch (error) {
            console.error('Ошибка при получении профилей терминалов:', error);
            return [];
        }
    },

    // Получить активные профили терминалов
    async getActiveTerminalProfiles() {
        try {
            const response = await tmsApiClient.get('/api/v1/tms/terminal-profiles/active');
            return response || [];
        } catch (error) {
            console.error('Ошибка при получении активных профилей терминалов:', error);
            return [];
        }
    },

    // Получить профиль терминала по ID
    async getTerminalProfileById(id) {
        try {
            const profiles = await this.getTerminalProfiles();
            return profiles.find(profile => profile.id === id);
        } catch (error) {
            console.error('Ошибка при получении профиля терминала:', error);
            return null;
        }
    }
}; 