import { tmsApiClient } from './ApiClient.js';

export const TerminalTypeService = {
    // Получить все типы терминалов
    async getTerminalTypes() {
        try {
            const response = await tmsApiClient.get('/api/v1/tms/terminal-types');
            return response || [];
        } catch (error) {
            console.error('Ошибка при получении типов терминалов:', error);
            return [];
        }
    },

    // Получить тип терминала по ID
    async getTerminalTypeById(id) {
        try {
            const types = await this.getTerminalTypes();
            return types.find(type => type.id === id);
        } catch (error) {
            console.error('Ошибка при получении типа терминала:', error);
            return null;
        }
    }
}; 