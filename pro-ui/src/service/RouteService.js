import { proApiClient } from './ApiClient.js';

export const RouteService = {
    /**
     * Получить все маршруты с пагинацией и фильтрацией
     */
    async getRoutes(params = {}) {
        try {
            const response = await proApiClient.get('/api/v1/pro/routes', params);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения маршрутов:', error);
            throw error;
        }
    },

    /**
     * Получить маршрут по ID
     */
    async getRouteById(routeId) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/routes/${routeId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения маршрута:', error);
            throw error;
        }
    },

    /**
     * Получить все версии маршрута по ID
     */
    async getRouteVersions(routeId) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/routes/${routeId}/versions`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения версий маршрута:', error);
            throw error;
        }
    },

    /**
     * Получить маршруты по проекту
     */
    async getRoutesByProject(projectId) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/projects/${projectId}/routes`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения маршрутов проекта:', error);
            throw error;
        }
    },

    /**
     * Получить маршруты по статусу
     */
    async getRoutesByStatus(status) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/routes/status/${status}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения маршрутов по статусу:', error);
            throw error;
        }
    },

    /**
     * Получить маршруты по схеме
     */
    async getRoutesByScheme(scheme) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/routes/scheme/${scheme}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения маршрутов по схеме:', error);
            throw error;
        }
    },

    /**
     * Создать новый маршрут
     */
    async createRoute(routeData) {
        try {
            const response = await proApiClient.post('/api/v1/pro/routes', routeData);
            return response.data;
        } catch (error) {
            console.error('Ошибка создания маршрута:', error);
            throw error;
        }
    },

    /**
     * Обновить маршрут
     */
    async updateRoute(routeId, routeData) {
        try {
            const response = await proApiClient.put(`/api/v1/pro/routes/${routeId}`, routeData);
            return response.data;
        } catch (error) {
            console.error('Ошибка обновления маршрута:', error);
            throw error;
        }
    },

    /**
     * Удалить маршрут
     */
    async deleteRoute(routeId) {
        try {
            const response = await proApiClient.delete(`/api/v1/pro/routes/${routeId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка удаления маршрута:', error);
            throw error;
        }
    },

    /**
     * Активировать маршрут
     */
    async activateRoute(routeId) {
        try {
            const response = await proApiClient.post(`/api/v1/pro/routes/${routeId}/activate`);
            return response.data;
        } catch (error) {
            console.error('Ошибка активации маршрута:', error);
            throw error;
        }
    },

    /**
     * Деактивировать маршрут
     */
    async deactivateRoute(routeId) {
        try {
            const response = await proApiClient.post(`/api/v1/pro/routes/${routeId}/deactivate`);
            return response.data;
        } catch (error) {
            console.error('Ошибка деактивации маршрута:', error);
            throw error;
        }
    },

    /**
     * Заблокировать маршрут
     */
    async blockRoute(routeId) {
        try {
            const response = await proApiClient.post(`/api/v1/pro/routes/${routeId}/block`);
            return response.data;
        } catch (error) {
            console.error('Ошибка блокировки маршрута:', error);
            throw error;
        }
    },

    /**
     * Получить станции маршрута
     */
    async getRouteStations(routeId) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/routes/${routeId}/stations`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения станций маршрута:', error);
            throw error;
        }
    },

    /**
     * Получить информацию о станциях маршрута с данными из справочника станций
     */
    async getRouteStationInfo(routeId) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/routes/${routeId}/stations/info`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения информации о станциях маршрута:', error);
            throw error;
        }
    },

    /**
     * Добавить станцию к маршруту
     */
    async addStationToRoute(routeId, stationId) {
        try {
            const response = await proApiClient.post(`/api/v1/pro/routes/${routeId}/stations`, {
                stationId: stationId
            });
            return response.data;
        } catch (error) {
            console.error('Ошибка добавления станции к маршруту:', error);
            throw error;
        }
    },

    /**
     * Удалить станцию из маршрута
     */
    async removeStationFromRoute(stationId) {
        try {
            const response = await proApiClient.delete(`/api/v1/pro/routes/stations/${stationId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка удаления станции из маршрута:', error);
            throw error;
        }
    },

    /**
     * Обновить позиции станций в маршруте
     */
    async updateStationPositions(routeId, stationPositions) {
        try {
            const response = await proApiClient.put(`/api/v1/pro/routes/${routeId}/stations/positions`, stationPositions);
            return response.data;
        } catch (error) {
            console.error('Ошибка обновления позиций станций:', error);
            throw error;
        }
    },

    /**
     * Проверить уникальность номера маршрута
     */
    async checkRouteNumberUnique(projectId, number, excludeId = null) {
        try {
            const params = { projectId, number };
            if (excludeId) {
                params.excludeId = excludeId;
            }
            const response = await proApiClient.get('/api/v1/pro/routes/check-number', params);
            return response.data;
        } catch (error) {
            console.error('Ошибка проверки уникальности номера маршрута:', error);
            throw error;
        }
    },

    /**
     * Получить маршрут с полными данными станций
     */
    async getRouteWithStations(routeId) {
        try {
            const [route, stationInfo] = await Promise.all([
                this.getRouteById(routeId),
                this.getRouteStationInfo(routeId)
            ]);

            return {
                ...route,
                stations: stationInfo
            };
        } catch (error) {
            console.error('Ошибка получения маршрута с станциями:', error);
            throw error;
        }
    },

    /**
     * Получить маршруты проекта с полными данными станций
     */
    async getRoutesByProjectWithStations(projectId) {
        try {
            const routes = await this.getRoutesByProject(projectId);
            
            const routesWithStations = await Promise.all(
                routes.map(async (route) => {
                    try {
                        const stationInfo = await this.getRouteStationInfo(route.id);
                        return {
                            ...route,
                            stations: stationInfo
                        };
                    } catch (error) {
                        console.warn(`Не удалось получить станции для маршрута ${route.id}:`, error);
                        return {
                            ...route,
                            stations: []
                        };
                    }
                })
            );

            return routesWithStations;
        } catch (error) {
            console.error('Ошибка получения маршрутов проекта с станциями:', error);
            throw error;
        }
    }
};
