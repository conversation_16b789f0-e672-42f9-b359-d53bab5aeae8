import { proApiClient } from './ApiClient.js';

export const TariffService = {
    /**
     * Получить все тарифы с пагинацией
     */
    async getTariffs(page = 0, size = 20, projectId = null) {
        try {
            const params = { page, size };
            if (projectId) {
                params.projectId = projectId;
            }
            
            const response = await proApiClient.get('/api/v1/pro/tariffs', params);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения тарифов:', error);
            throw error;
        }
    },

    /**
     * Получить тариф по ID
     */
    async getTariffById(id) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/tariffs/${id}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения тарифа:', error);
            throw error;
        }
    },

    /**
     * Создать новый тариф
     */
    async createTariff(tariffData) {
        try {
            const response = await proApiClient.post('/api/v1/pro/tariffs', tariffData);
            return response.data;
        } catch (error) {
            console.error('Ошибка создания тарифа:', error);
            throw error;
        }
    },

    /**
     * Обновить тариф
     */
    async updateTariff(id, tariffData) {
        try {
            const response = await proApiClient.put(`/api/v1/pro/tariffs/${id}`, tariffData);
            return response.data;
        } catch (error) {
            console.error('Ошибка обновления тарифа:', error);
            throw error;
        }
    },

    /**
     * Удалить тариф
     */
    async deleteTariff(id) {
        try {
            const response = await proApiClient.delete(`/api/v1/pro/tariffs/${id}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка удаления тарифа:', error);
            throw error;
        }
    },

    /**
     * Активировать тариф
     */
    async activateTariff(id) {
        try {
            const response = await proApiClient.post(`/api/v1/pro/tariffs/${id}/activate`);
            return response.data;
        } catch (error) {
            console.error('Ошибка активации тарифа:', error);
            throw error;
        }
    },

    /**
     * Деактивировать тариф
     */
    async deactivateTariff(id) {
        try {
            const response = await proApiClient.post(`/api/v1/pro/tariffs/${id}/deactivate`);
            return response.data;
        } catch (error) {
            console.error('Ошибка деактивации тарифа:', error);
            throw error;
        }
    },

    /**
     * Заблокировать тариф
     */
    async blockTariff(id) {
        try {
            const response = await proApiClient.post(`/api/v1/pro/tariffs/${id}/block`);
            return response.data;
        } catch (error) {
            console.error('Ошибка блокировки тарифа:', error);
            throw error;
        }
    },

    /**
     * Получить тарифы по проекту
     */
    async getTariffsByProject(projectId, page = 0, size = 20) {
        return this.getTariffs(page, size, projectId);
    },

    // Методы для работы с полной информацией о тарифе

    /**
     * Получить полную информацию о тарифе
     */
    async getTariffFullInfo(id) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/tariffs/${id}/full`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения полной информации о тарифе:', error);
            throw error;
        }
    },

    // Методы для работы с продуктами тарифа

    /**
     * Получить продукты тарифа
     */
    async getTariffProducts(tariffId) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/tariffs/${tariffId}/products`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения продуктов тарифа:', error);
            throw error;
        }
    },

    /**
     * Создать продукт тарифа
     */
    async createTariffProduct(productData) {
        try {
            const response = await proApiClient.post('/api/v1/pro/tariffs/products', productData);
            return response.data;
        } catch (error) {
            console.error('Ошибка создания продукта тарифа:', error);
            throw error;
        }
    },

    /**
     * Обновить продукт тарифа
     */
    async updateTariffProduct(id, productData) {
        try {
            const response = await proApiClient.put(`/api/v1/pro/tariffs/products/${id}`, productData);
            return response.data;
        } catch (error) {
            console.error('Ошибка обновления продукта тарифа:', error);
            throw error;
        }
    },

    /**
     * Удалить продукт тарифа
     */
    async deleteTariffProduct(id) {
        try {
            const response = await proApiClient.delete(`/api/v1/pro/tariffs/products/${id}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка удаления продукта тарифа:', error);
            throw error;
        }
    },

    // Методы для работы с тарифными матрицами

    /**
     * Получить тарифные матрицы
     */
    async getTariffMatrices(productDictRowId) {
        try {
            const response = await proApiClient.get(`/api/v1/pro/tariffs/products/${productDictRowId}/matrices`);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения тарифных матриц:', error);
            throw error;
        }
    },

    /**
     * Создать тарифную матрицу
     */
    async createTariffMatrix(matrixData) {
        try {
            const response = await proApiClient.post('/api/v1/pro/tariffs/matrices', matrixData);
            return response.data;
        } catch (error) {
            console.error('Ошибка создания тарифной матрицы:', error);
            throw error;
        }
    },

    /**
     * Обновить тарифную матрицу
     */
    async updateTariffMatrix(id, matrixData) {
        try {
            const response = await proApiClient.put(`/api/v1/pro/tariffs/matrices/${id}`, matrixData);
            return response.data;
        } catch (error) {
            console.error('Ошибка обновления тарифной матрицы:', error);
            throw error;
        }
    },

    /**
     * Массовое обновление тарифной матрицы для продукта
     */
    async updateTariffMatrixForProduct(productDictRowId, matrixData) {
        try {
            const response = await proApiClient.post(`/api/v1/pro/tariffs/products/${productDictRowId}/matrix`, {
                matrixData: matrixData
            });
            return response.data;
        } catch (error) {
            console.error('Ошибка массового обновления тарифной матрицы:', error);
            throw error;
        }
    },

    /**
     * Удалить тарифную матрицу
     */
    async deleteTariffMatrix(id) {
        try {
            const response = await proApiClient.delete(`/api/v1/pro/tariffs/matrices/${id}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка удаления тарифной матрицы:', error);
            throw error;
        }
    },

    // Методы для работы с маршрутами и тарифами

    /**
     * Получить тарифы по маршрутам
     */
    async getRouteTariffs(projectId = null) {
        try {
            const params = {};
            if (projectId) {
                params.projectId = projectId;
            }
            const response = await proApiClient.get('/api/v1/pro/routes/tariffs', params);
            return response.data;
        } catch (error) {
            console.error('Ошибка получения тарифов по маршрутам:', error);
            throw error;
        }
    },

    /**
     * Назначить тариф на маршрут
     */
    async assignTariffToRoute(assignmentData) {
        try {
            const response = await proApiClient.post('/api/v1/pro/routes/tariffs/assign', assignmentData);
            return response.data;
        } catch (error) {
            console.error('Ошибка назначения тарифа на маршрут:', error);
            throw error;
        }
    },

    /**
     * Удалить тариф с маршрута
     */
    async removeTariffFromRoute(routeId, tariffId) {
        try {
            const response = await proApiClient.delete(`/api/v1/pro/routes/${routeId}/tariffs/${tariffId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка удаления тарифа с маршрута:', error);
            throw error;
        }
    }
};
