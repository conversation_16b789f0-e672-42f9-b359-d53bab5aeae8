import { proApiClient } from './ApiClient';

export const GeographicService = {

    async searchCountries(name = '') {
        try {
            const response = await proApiClient.get(`/api/v1/pro/geographic/countries?name=${encodeURIComponent(name)}`);
            console.log('Ответ от сервера для стран:', response);
            
            // Проверяем, что response - это массив
            if (Array.isArray(response)) {
                // Извлекаем данные из каждого элемента массива
                return response.map(item => item.data).filter(Boolean);
            } else if (response.success) {
                // Если это один объект с success
                return Array.isArray(response.data) ? response.data : [response.data];
            } else {
                throw new Error(response.message || 'Ошибка поиска стран');
            }
        } catch (error) {
            console.error('Ошибка поиска стран:', error);
            throw error;
        }
    },

    async searchRegions(name = '', countryId = null) {
        try {
            let url = `/api/v1/pro/geographic/regions?name=${encodeURIComponent(name)}`;
            if (countryId) {
                url += `&countryId=${countryId}`;
            }
            
            const response = await proApiClient.get(url);
            console.log('Ответ от сервера для регионов:', response);
            
            // Проверяем, что response - это массив
            if (Array.isArray(response)) {
                // Извлекаем данные из каждого элемента массива
                return response.map(item => item.data).filter(Boolean);
            } else if (response.success) {
                // Если это один объект с success
                return Array.isArray(response.data) ? response.data : [response.data];
            } else {
                throw new Error(response.message || 'Ошибка поиска регионов');
            }
        } catch (error) {
            console.error('Ошибка поиска регионов:', error);
            throw error;
        }
    },

    async searchCities(name = '', regionId = null) {
        try {
            let url = `/api/v1/pro/geographic/cities?name=${encodeURIComponent(name)}`;
            if (regionId) {
                url += `&regionId=${regionId}`;
            }
            
            const response = await proApiClient.get(url);
            console.log('Ответ от сервера для городов:', response);
            
            // Проверяем, что response - это массив
            if (Array.isArray(response)) {
                // Извлекаем данные из каждого элемента массива
                return response.map(item => item.data).filter(Boolean);
            } else if (response.success) {
                // Если это один объект с success
                return Array.isArray(response.data) ? response.data : [response.data];
            } else {
                throw new Error(response.message || 'Ошибка поиска городов');
            }
        } catch (error) {
            console.error('Ошибка поиска городов:', error);
            throw error;
        }
    },

    async searchDistricts(name = '', cityId = null) {
        try {
            let url = `/api/v1/pro/geographic/districts?name=${encodeURIComponent(name)}`;
            if (cityId) {
                url += `&cityId=${cityId}`;
            }
            
            const response = await proApiClient.get(url);
            console.log('Ответ от сервера для районов:', response);
            
            // Проверяем, что response - это массив
            if (Array.isArray(response)) {
                // Извлекаем данные из каждого элемента массива
                return response.map(item => item.data).filter(Boolean);
            } else if (response.success) {
                // Если это один объект с success
                return Array.isArray(response.data) ? response.data : [response.data];
            } else {
                throw new Error(response.message || 'Ошибка поиска районов');
            }
        } catch (error) {
            console.error('Ошибка поиска районов:', error);
            throw error;
        }
    },

    async createCountry(countryData) {
        try {
            const response = await proApiClient.post('/api/v1/pro/geographic/countries', countryData);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания страны');
            }
        } catch (error) {
            console.error('Ошибка создания страны:', error);
            throw error;
        }
    },

    async createRegion(regionData) {
        try {
            const response = await proApiClient.post('/api/v1/pro/geographic/regions', regionData);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания региона');
            }
        } catch (error) {
            console.error('Ошибка создания региона:', error);
            throw error;
        }
    },

    async createCity(cityData) {
        try {
            const response = await proApiClient.post('/api/v1/pro/geographic/cities', cityData);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания города');
            }
        } catch (error) {
            console.error('Ошибка создания города:', error);
            throw error;
        }
    },

    async createDistrict(districtData) {
        try {
            const response = await proApiClient.post('/api/v1/pro/geographic/districts', districtData);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания района');
            }
        } catch (error) {
            console.error('Ошибка создания района:', error);
            throw error;
        }
    }
}; 