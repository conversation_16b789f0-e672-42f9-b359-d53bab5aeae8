import { TmsApiService } from './TmsApiService.js';

export const TerminalService = {
    // Получить список терминалов с фильтрацией
    async getTerminals(params = {}) {
        try {
            const response = await TmsApiService.getTerminals(params);
            return response.terminals || [];
        } catch (error) {
            console.error('Ошибка при получении терминалов:', error);
            return [];
        }
    },

    // Получить терминал по ID
    async getTerminalById(id) {
        try {
            return await TmsApiService.getTerminalById(id);
        } catch (error) {
            console.error('Ошибка при получении терминала:', error);
            return null;
        }
    },

    // Получить терминалы по организации
    async getTerminalsByOrganization(organizationId) {
        try {
            const response = await TmsApiService.getTerminals({ organizationId });
            return response.terminals || [];
        } catch (error) {
            console.error('Ошибка при получении терминалов организации:', error);
            return [];
        }
    },

    // Создать терминал
    async createTerminal(terminalData) {
        try {
            return await TmsApiService.createTerminal(terminalData);
        } catch (error) {
            console.error('Ошибка при создании терминала:', error);
            throw error;
        }
    },

    // Обновить терминал
    async updateTerminal(id, terminalData) {
        try {
            return await TmsApiService.updateTerminal(id, terminalData);
        } catch (error) {
            console.error('Ошибка при обновлении терминала:', error);
            throw error;
        }
    },

    // Удалить терминал
    async deleteTerminal(id) {
        try {
            return await TmsApiService.deleteTerminal(id);
        } catch (error) {
            console.error('Ошибка при удалении терминала:', error);
            throw error;
        }
    },

    updateSoftware(terminalId, softwareVersion) {
        console.log('Updating software for terminal:', terminalId, 'to version:', softwareVersion);
        return Promise.resolve({ 
            success: true, 
            message: `ПО терминала ${terminalId} обновлено до версии ${softwareVersion}`,
            updateDate: new Date().toISOString()
        });
    },

    updateConfiguration(terminalId, configurationVersion) {
        console.log('Updating configuration for terminal:', terminalId, 'to version:', configurationVersion);
        return Promise.resolve({ 
            success: true, 
            message: `Конфигурация терминала ${terminalId} обновлена до версии ${configurationVersion}`,
            updateDate: new Date().toISOString()
        });
    },

    getTerminalTelemetry(terminalId) {
        console.log('Getting telemetry for terminal:', terminalId);
        return Promise.resolve({
            terminalId: terminalId,
            timestamp: new Date().toISOString(),
            batteryLevel: Math.floor(Math.random() * 100),
            networkSignal: ['excellent', 'good', 'poor', 'none'][Math.floor(Math.random() * 4)],
            temperature: Math.floor(Math.random() * 40) + 10,
            memoryUsage: Math.floor(Math.random() * 80) + 20,
            diskUsage: Math.floor(Math.random() * 60) + 30,
            transactionsToday: Math.floor(Math.random() * 500),
            lastTransaction: new Date(Date.now() - Math.random() * 3600000).toISOString()
        });
    },

    restartTerminal(terminalId) {
        console.log('Restarting terminal:', terminalId);
        return Promise.resolve({ 
            success: true, 
            message: `Команда перезагрузки отправлена на терминал ${terminalId}`,
            commandDate: new Date().toISOString()
        });
    }
};
