export const AgentService = {
    getData: () => {
        return [
            {
                id: 1,
                uuid: '550e8400-e29b-41d4-a716-************',
                name: 'ООО "Транс-Агент"',
                code: 'AGENT001',
                inn: '7703456789',
                type: 'legal_entity',
                status: 'active',
                contractNumber: 'АГ-2024-001',
                contractDate: '2024-01-01T00:00:00Z',
                commissionRate: 2.5,
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                contactPerson: 'Агентов Агент Агентович',
                phone: '+7 (495) 123-45-67',
                email: '<EMAIL>',
                address: 'г. Москва, ул. Агентская, д. 1',
                serviceTypes: ['card_sales', 'card_refill', 'subscription_sales'],
                totalServicePoints: 15,
                activeServicePoints: 14,
                monthlyTurnover: 2500000.00,
                createdDate: '2023-12-01T09:00:00Z'
            },
            {
                id: 2,
                uuid: '550e8400-e29b-41d4-a716-************',
                name: 'ИП Кассиров Касса Кассовна',
                code: 'AGENT002',
                inn: '************',
                type: 'individual',
                status: 'active',
                contractNumber: 'АГ-2024-002',
                contractDate: '2024-01-15T00:00:00Z',
                commissionRate: 3.0,
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                contactPerson: 'Кассиров Касса Кассовна',
                phone: '+7 (495) 234-56-78',
                email: '<EMAIL>',
                address: 'г. Москва, ул. Кассовая, д. 5',
                serviceTypes: ['card_sales', 'card_refill'],
                totalServicePoints: 3,
                activeServicePoints: 3,
                monthlyTurnover: 850000.00,
                createdDate: '2024-01-10T11:30:00Z'
            },
            {
                id: 3,
                uuid: '550e8400-e29b-41d4-a716-************',
                name: 'ООО "Мобильные платежи"',
                code: 'AGENT003',
                inn: '7704567890',
                type: 'legal_entity',
                status: 'suspended',
                contractNumber: 'АГ-2023-045',
                contractDate: '2023-06-01T00:00:00Z',
                commissionRate: 1.8,
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                contactPerson: 'Мобильный Платеж Приложенович',
                phone: '+7 (495) 345-67-89',
                email: '<EMAIL>',
                address: 'г. Москва, ул. Мобильная, д. 10',
                serviceTypes: ['mobile_app', 'online_refill'],
                totalServicePoints: 1,
                activeServicePoints: 0,
                monthlyTurnover: 0.00,
                createdDate: '2023-05-20T13:15:00Z'
            },
            {
                id: 4,
                uuid: '550e8400-e29b-41d4-a716-************',
                name: 'ООО "Автобус-Сервис"',
                code: 'AGENT004',
                inn: '7706789012',
                type: 'legal_entity',
                status: 'active',
                contractNumber: 'АГ-2024-003',
                contractDate: '2024-02-01T00:00:00Z',
                commissionRate: 2.2,
                organizationId: 3,
                organizationName: 'ООО "Автобусный парк №1"',
                contactPerson: 'Автобусов Сервис Обслуживанович',
                phone: '+7 (495) 456-78-90',
                email: '<EMAIL>',
                address: 'г. Москва, ул. Автобусная, д. 20',
                serviceTypes: ['card_sales', 'card_refill', 'terminal_maintenance'],
                totalServicePoints: 8,
                activeServicePoints: 7,
                monthlyTurnover: 1200000.00,
                createdDate: '2024-01-25T15:20:00Z'
            },
            {
                id: 5,
                uuid: '550e8400-e29b-41d4-a716-************',
                name: 'ООО "Метро-Касса"',
                code: 'AGENT005',
                inn: '7705678901',
                type: 'legal_entity',
                status: 'active',
                contractNumber: 'АГ-2024-004',
                contractDate: '2024-01-20T00:00:00Z',
                commissionRate: 2.8,
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                contactPerson: 'Метров Касса Подземная',
                phone: '+7 (495) 567-89-01',
                email: '<EMAIL>',
                address: 'г. Москва, Сокольническая площадь, д. 1',
                serviceTypes: ['card_sales', 'card_refill', 'subscription_sales', 'customer_service'],
                totalServicePoints: 25,
                activeServicePoints: 23,
                monthlyTurnover: 4500000.00,
                createdDate: '2024-01-15T10:45:00Z'
            },
            {
                id: 6,
                uuid: '550e8400-e29b-41d4-a716-************',
                name: 'ЛКП ООО "СберТройка"',
                code: 'AGENT006',
                inn: '7701234567',
                type: 'legal_entity',
                status: 'active',
                contractNumber: 'АГ-2024-005',
                contractDate: '2024-01-01T00:00:00Z',
                commissionRate: 1.5,
                organizationId: 4,
                organizationName: 'ООО "СберТройка"',
                contactPerson: 'Сберов Тройка Карточкович',
                phone: '+7 (495) 987-65-43',
                email: '<EMAIL>',
                address: 'г. Москва, ул. Сбербанковская, д. 1',
                serviceTypes: ['card_sales', 'card_refill', 'subscription_sales', 'customer_service'],
                totalServicePoints: 50,
                activeServicePoints: 48,
                monthlyTurnover: 8000000.00,
                createdDate: '2024-01-01T08:00:00Z'
            },
            {
                id: 7,
                uuid: '550e8400-e29b-41d4-a716-************',
                name: 'СБОЛ',
                code: 'AGENT007',
                inn: '7707654321',
                type: 'legal_entity',
                status: 'active',
                contractNumber: 'АГ-2024-006',
                contractDate: '2024-01-01T00:00:00Z',
                commissionRate: 1.2,
                organizationId: 5,
                organizationName: 'СБОЛ',
                contactPerson: 'СберБанк Онлайн',
                phone: '+7 (495) 111-22-33',
                email: '<EMAIL>',
                address: 'г. Москва, ул. Системная, д. 10',
                serviceTypes: ['card_sales', 'card_refill', 'subscription_sales', 'terminal_maintenance'],
                totalServicePoints: 30,
                activeServicePoints: 28,
                monthlyTurnover: 5000000.00,
                createdDate: '2024-01-01T09:00:00Z'
            }
        ];
    },

    getAgents() {
        return Promise.resolve(this.getData());
    },

    getAgentById(id) {
        const agents = this.getData();
        return Promise.resolve(agents.find(agent => agent.id === parseInt(id)));
    },

    getAgentByUuid(uuid) {
        const agents = this.getData();
        return Promise.resolve(agents.find(agent => agent.uuid === uuid));
    },

    getAgentsByOrganization(organizationId) {
        const agents = this.getData();
        return Promise.resolve(agents.filter(agent => agent.organizationId === parseInt(organizationId)));
    },

    getAgentsByStatus(status) {
        const agents = this.getData();
        return Promise.resolve(agents.filter(agent => agent.status === status));
    },

    createAgent(agentData) {
        console.log('Creating agent:', agentData);

        const newAgent = {
            ...agentData,
            id: Date.now(),
            uuid: '550e8400-e29b-41d4-a716-' + Math.random().toString(36).substr(2, 12),
            status: 'draft',
            totalServicePoints: 0,
            activeServicePoints: 0,
            monthlyTurnover: 0.00,
            createdDate: new Date().toISOString()
        };

        return Promise.resolve(newAgent);
    },

    updateAgent(id, agentData) {
        console.log('Updating agent:', id, agentData);

        const updatedAgent = {
            ...agentData,
            id: parseInt(id)
        };

        return Promise.resolve(updatedAgent);
    },

    deleteAgent(id) {
        console.log('Deleting agent:', id);
        return Promise.resolve({ success: true });
    },

    activateAgent(id) {
        console.log('Activating agent:', id);
        return Promise.resolve({
            success: true,
            message: `Агент ${id} активирован`,
            activationDate: new Date().toISOString()
        });
    },

    suspendAgent(id, reason) {
        console.log('Suspending agent:', id, 'reason:', reason);
        return Promise.resolve({
            success: true,
            message: `Агент ${id} приостановлен. Причина: ${reason}`,
            suspensionDate: new Date().toISOString()
        });
    },

    getAgentOperations(agentId, dateFrom, dateTo) {
        console.log('Getting operations for agent:', agentId, 'from:', dateFrom, 'to:', dateTo);

        // Мок данных операций агента
        const operations = [
            {
                id: 1,
                type: 'card_sale',
                amount: 100.00,
                commission: 2.50,
                timestamp: '2024-01-20T10:30:00Z',
                servicePointId: 'SP001',
                cardNumber: '****1234'
            },
            {
                id: 2,
                type: 'card_refill',
                amount: 500.00,
                commission: 12.50,
                timestamp: '2024-01-20T11:15:00Z',
                servicePointId: 'SP001',
                cardNumber: '****5678'
            },
            {
                id: 3,
                type: 'subscription_sale',
                amount: 2300.00,
                commission: 57.50,
                timestamp: '2024-01-20T12:00:00Z',
                servicePointId: 'SP002',
                cardNumber: '****9012'
            }
        ];

        return Promise.resolve(operations);
    },

    generateAgentReport(agentId, reportType, period) {
        console.log('Generating report for agent:', agentId, 'type:', reportType, 'period:', period);

        return Promise.resolve({
            agentId: agentId,
            reportType: reportType,
            period: period,
            generatedDate: new Date().toISOString(),
            totalOperations: Math.floor(Math.random() * 1000) + 100,
            totalAmount: Math.floor(Math.random() * 1000000) + 50000,
            totalCommission: Math.floor(Math.random() * 25000) + 1250,
            downloadUrl: `/reports/agent_${agentId}_${reportType}_${period}.pdf`
        });
    }
};
