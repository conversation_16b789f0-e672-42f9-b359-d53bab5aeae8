export const ContractService = {
    getData: () => {
        return [
            {
                id: '550e8400-e29b-41d4-a716-446655440001',
                // Основные атрибуты договора
                projectCode: 'MSK-001',
                projectName: 'СберТройка ПРО Москва',
                projectType: 'transport_system',
                contractType: 'system_rules',
                contractName: 'Правила системы СберТройка ПРО г. Москва',
                contractNumber: 'ПС-СТ-2024-001',
                signatureDate: '2024-01-15T00:00:00Z',
                conclusionDate: '2024-01-01T00:00:00Z',
                completionDate: '2024-12-31T23:59:59Z',
                status: 'active',
                externalId1C: 'DOC_000001_2024',

                // Поля для выпадающего списка
                number: 'ПС-СТ-2024-001',
                organizationName: 'ПАО Сбербанк',
                amount: 25000000,
                validTo: '2024-12-31T23:59:59Z',
                displayName: 'ПС-СТ-2024-001 - ПАО Сбербанк',

                // Организации в договоре с ролями
                contractOrganizations: [
                    {
                        organizationId: 1,
                        organizationName: 'ПАО Сбербанк',
                        role: 'operator',
                        roleDescription: 'Оператор - организатор ТКП СТ'
                    },
                    {
                        organizationId: 2,
                        organizationName: 'ООО "Городской транспорт"',
                        role: 'carrier',
                        roleDescription: 'Перевозчик'
                    },
                    {
                        organizationId: 3,
                        organizationName: 'ООО "СберТех"',
                        role: 'processing_center',
                        roleDescription: 'Процессинговый центр'
                    }
                ],

                // Системная информация
                syncStatus: 'synced',
                lastSyncDate: '2024-01-20T10:30:00Z',
                createdDate: '2023-12-15T09:00:00Z'
            },
            {
                id: '550e8400-e29b-41d4-a716-446655440002',
                projectCode: 'MSK-002',
                projectName: 'СберТройка ПРО Метро',
                projectType: 'metro_system',
                contractType: 'system_rules',
                contractName: 'Правила системы СберТройка ПРО Метрополитен',
                contractNumber: 'ПС-СТ-2024-002',
                signatureDate: '2024-02-10T00:00:00Z',
                conclusionDate: '2024-02-01T00:00:00Z',
                completionDate: '2025-01-31T23:59:59Z',
                status: 'active',
                externalId1C: 'DOC_000002_2024',

                // Поля для выпадающего списка
                number: 'ПС-СТ-2024-002',
                organizationName: 'АО "Метрополитен"',
                amount: 18000000,
                validTo: '2025-01-31T23:59:59Z',
                displayName: 'ПС-СТ-2024-002 - АО "Метрополитен"',

                contractOrganizations: [
                    {
                        organizationId: 1,
                        organizationName: 'ПАО Сбербанк',
                        role: 'operator',
                        roleDescription: 'Оператор - организатор ТКП СТ'
                    },
                    {
                        organizationId: 2,
                        organizationName: 'АО "Метрополитен"',
                        role: 'carrier',
                        roleDescription: 'Перевозчик'
                    },
                    {
                        organizationId: 3,
                        organizationName: 'ООО "СберТех"',
                        role: 'processing_center',
                        roleDescription: 'Процессинговый центр'
                    }
                ],

                syncStatus: 'synced',
                lastSyncDate: '2024-01-18T14:20:00Z',
                createdDate: '2024-01-10T11:30:00Z'
            },
            {
                id: '550e8400-e29b-41d4-a716-446655440003',
                projectCode: 'MSK-003',
                projectName: 'СберТройка ПРО Автобусы',
                projectType: 'bus_system',
                contractType: 'system_rules',
                contractName: 'Правила системы СберТройка ПРО Автобусы',
                contractNumber: 'ПС-СТ-2023-045',
                signatureDate: '2023-05-25T00:00:00Z',
                conclusionDate: '2023-06-01T00:00:00Z',
                completionDate: '2024-05-31T23:59:59Z',
                status: 'expiring',
                externalId1C: 'DOC_000045_2023',

                // Поля для выпадающего списка
                number: 'ПС-СТ-2023-045',
                organizationName: 'ООО "Автобусный парк №1"',
                amount: 12000000,
                validTo: '2024-05-31T23:59:59Z',
                displayName: 'ПС-СТ-2023-045 - ООО "Автобусный парк №1"',

                contractOrganizations: [
                    {
                        organizationId: 1,
                        organizationName: 'ПАО Сбербанк',
                        role: 'operator',
                        roleDescription: 'Оператор - организатор ТКП СТ'
                    },
                    {
                        organizationId: 3,
                        organizationName: 'ООО "Автобусный парк №1"',
                        role: 'carrier',
                        roleDescription: 'Перевозчик'
                    },
                    {
                        organizationId: 3,
                        organizationName: 'ООО "СберТех"',
                        role: 'processing_center',
                        roleDescription: 'Процессинговый центр'
                    }
                ],

                syncStatus: 'pending',
                lastSyncDate: '2024-01-15T16:45:00Z',
                createdDate: '2023-05-20T13:15:00Z'
            },
            {
                id: '550e8400-e29b-41d4-a716-446655440004',
                projectCode: 'MSK-004',
                projectName: 'СберТройка ПРО Инновации',
                projectType: 'innovation_system',
                contractType: 'development_agreement',
                contractName: 'Договор на внедрение инновационных решений СберТройка ПРО',
                contractNumber: 'ДОГ-СТ-2024-003',
                signatureDate: null, // Еще не подписан
                conclusionDate: '2024-03-01T00:00:00Z',
                completionDate: '2024-12-31T23:59:59Z',
                status: 'draft',
                externalId1C: null, // Еще не синхронизирован

                // Поля для выпадающего списка
                number: 'ДОГ-СТ-2024-003',
                organizationName: 'ГУП "Мосгортранс"',
                amount: 8000000,
                validTo: '2024-12-31T23:59:59Z',
                displayName: 'ДОГ-СТ-2024-003 - ГУП "Мосгортранс"',

                contractOrganizations: [
                    {
                        organizationId: 1,
                        organizationName: 'ПАО Сбербанк',
                        role: 'contractor',
                        roleDescription: 'Подрядчик'
                    },
                    {
                        organizationId: 4,
                        organizationName: 'ГУП "Мосгортранс"',
                        role: 'customer',
                        roleDescription: 'Заказчик'
                    }
                ],

                syncStatus: 'never',
                lastSyncDate: null,
                createdDate: '2024-01-25T15:20:00Z'
            }
        ]
    },

    getContracts() {
        return Promise.resolve(this.getData());
    },

    getContractsByProject(projectCode) {
        return Promise.resolve(this.getData());
    },

    getContractById(contractId) {
        const contracts = this.getData();
        const contract = contracts.find(c => c.id == contractId);
        return Promise.resolve(contract);
    },

    createContract(projectCode, contractData) {
        console.log('Creating contract for project:', projectCode, contractData);

        const newContract = {
            ...contractData,
            id: Date.now(),
            status: 'draft',
            syncStatus: 'never',
            lastSyncDate: null,
            createdDate: new Date().toISOString()
        };

        return Promise.resolve(newContract);
    },

    updateContract(contractId, contractData) {
        console.log('Updating contract:', contractId, contractData);

        const updatedContract = {
            ...contractData,
            id: contractId
        };

        return Promise.resolve(updatedContract);
    },

    deleteContract(contractId) {
        console.log('Deleting contract:', contractId);
        return Promise.resolve({ success: true });
    },

    syncContract(contractId) {
        console.log('Syncing contract with 1C:', contractId);

        return new Promise((resolve) => {
            setTimeout(() => {
                const success = Math.random() > 0.2;
                resolve({
                    success,
                    message: success
                        ? 'Синхронизация договора с 1С выполнена успешно'
                        : 'Ошибка синхронизации с 1С: договор не найден в системе',
                    syncDate: new Date().toISOString()
                });
            }, 2000);
        });
    },

    syncAllContracts() {
        console.log('Syncing all contracts with 1C');

        return new Promise((resolve) => {
            setTimeout(() => {
                const totalCount = this.getData().length;
                const successCount = Math.floor(totalCount * 0.75);
                const errorCount = totalCount - successCount;

                resolve({
                    success: true,
                    totalCount,
                    successCount,
                    errorCount,
                    message: `Синхронизация завершена. Успешно: ${successCount}, с ошибками: ${errorCount}`,
                    syncDate: new Date().toISOString()
                });
            }, 4000);
        });
    }
}
