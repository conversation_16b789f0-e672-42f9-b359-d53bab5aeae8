export const TicketService = {
    getData: () => {
        return [
            // Билеты, купленные за наличные (CASH)
            {
                id: 1,
                ticketNumber: 'CASH-2024-001234',
                paymentMethod: 'cash',
                ticketType: 'single',
                tariffName: 'Базовый тариф автобус',
                amount: 46.00,
                currency: 'RUB',
                purchaseDate: '2024-01-20T14:30:15Z',
                validFrom: '2024-01-20T14:30:15Z',
                validTo: '2024-01-20T23:59:59Z',
                status: 'active',
                terminalId: 'TRM-MSK-001',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                route: 'Автобус №101',
                passengerCategory: 'adult',
                fiscalDocumentNumber: 'ФД-001234',
                usageCount: 0,
                maxUsageCount: 1
            },
            {
                id: 2,
                ticketNumber: 'CASH-2024-001235',
                paymentMethod: 'cash',
                ticketType: 'single',
                tariffName: 'Льготный тариф студенты',
                amount: 23.00,
                currency: 'RUB',
                purchaseDate: '2024-01-20T15:15:30Z',
                validFrom: '2024-01-20T15:15:30Z',
                validTo: '2024-01-20T23:59:59Z',
                status: 'used',
                terminalId: 'TRM-MSK-001',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                route: 'Автобус №101',
                passengerCategory: 'student',
                fiscalDocumentNumber: 'ФД-001235',
                usageCount: 1,
                maxUsageCount: 1
            },
            // Билеты, купленные банковской картой (EMV)
            {
                id: 3,
                ticketNumber: 'EMV-2024-001236',
                paymentMethod: 'emv',
                ticketType: 'single',
                tariffName: 'Метро базовый',
                amount: 64.00,
                currency: 'RUB',
                purchaseDate: '2024-01-20T16:12:08Z',
                validFrom: '2024-01-20T16:12:08Z',
                validTo: '2024-01-20T23:59:59Z',
                status: 'active',
                terminalId: 'TRM-MSK-002',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                route: 'Метро',
                passengerCategory: 'adult',
                fiscalDocumentNumber: 'ФД-001236',
                cardNumber: '****1234',
                cardType: 'VISA',
                usageCount: 0,
                maxUsageCount: 1
            },
            {
                id: 4,
                ticketNumber: 'EMV-2024-001237',
                paymentMethod: 'emv',
                ticketType: 'monthly',
                tariffName: 'Месячный проездной автобус',
                amount: 2300.00,
                currency: 'RUB',
                purchaseDate: '2024-01-01T09:00:00Z',
                validFrom: '2024-01-01T00:00:00Z',
                validTo: '2024-01-31T23:59:59Z',
                status: 'active',
                terminalId: 'TRM-MSK-003',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                route: 'Все автобусные маршруты',
                passengerCategory: 'adult',
                fiscalDocumentNumber: 'ФД-001237',
                cardNumber: '****5678',
                cardType: 'MasterCard',
                usageCount: 45,
                maxUsageCount: 999
            },
            // Билеты, купленные картой Тройка (CBT)
            {
                id: 5,
                ticketNumber: 'CBT-2024-001238',
                paymentMethod: 'cbt',
                ticketType: 'single',
                tariffName: 'Базовый тариф автобус',
                amount: 46.00,
                currency: 'RUB',
                purchaseDate: '2024-01-20T17:20:33Z',
                validFrom: '2024-01-20T17:20:33Z',
                validTo: '2024-01-20T23:59:59Z',
                status: 'used',
                terminalId: 'TRM-MSK-004',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                route: 'Автобус №205',
                passengerCategory: 'adult',
                fiscalDocumentNumber: 'ФД-001238',
                troikaCardNumber: '****9012',
                balanceBefore: 150.00,
                balanceAfter: 104.00,
                usageCount: 1,
                maxUsageCount: 1
            },
            {
                id: 6,
                ticketNumber: 'CBT-2024-001239',
                paymentMethod: 'cbt',
                ticketType: 'single',
                tariffName: 'Пенсионный льготный',
                amount: 0.00,
                currency: 'RUB',
                purchaseDate: '2024-01-20T18:05:17Z',
                validFrom: '2024-01-20T18:05:17Z',
                validTo: '2024-01-20T23:59:59Z',
                status: 'used',
                terminalId: 'TRM-MSK-005',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                route: 'Автобус №101',
                passengerCategory: 'pension',
                fiscalDocumentNumber: 'ФД-001239',
                troikaCardNumber: '****3456',
                balanceBefore: 50.00,
                balanceAfter: 50.00,
                usageCount: 1,
                maxUsageCount: 1
            },
            // Билеты, купленные через мобильное приложение (ABT)
            {
                id: 7,
                ticketNumber: 'ABT-2024-001240',
                paymentMethod: 'abt',
                ticketType: 'single',
                tariffName: 'Экспресс автобус',
                amount: 92.00,
                currency: 'RUB',
                purchaseDate: '2024-01-20T19:30:45Z',
                validFrom: '2024-01-20T19:30:45Z',
                validTo: '2024-01-20T23:59:59Z',
                status: 'active',
                terminalId: null,
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"',
                route: 'Экспресс-автобус Э1',
                passengerCategory: 'adult',
                fiscalDocumentNumber: 'ФД-001240',
                phoneNumber: '+7900*****67',
                appVersion: '2.1.5',
                deviceId: 'DEVICE123456',
                usageCount: 0,
                maxUsageCount: 1
            },
            {
                id: 8,
                ticketNumber: 'ABT-2024-001241',
                paymentMethod: 'abt',
                ticketType: 'daily',
                tariffName: 'Суточный проездной',
                amount: 230.00,
                currency: 'RUB',
                purchaseDate: '2024-01-20T08:00:00Z',
                validFrom: '2024-01-20T00:00:00Z',
                validTo: '2024-01-20T23:59:59Z',
                status: 'active',
                terminalId: null,
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                route: 'Все маршруты',
                passengerCategory: 'adult',
                fiscalDocumentNumber: 'ФД-001241',
                phoneNumber: '+7901*****89',
                appVersion: '2.1.5',
                deviceId: 'DEVICE789012',
                usageCount: 8,
                maxUsageCount: 999
            }
        ];
    },

    getTickets() {
        return Promise.resolve(this.getData());
    },

    getTicketsByPaymentMethod(paymentMethod) {
        const tickets = this.getData();
        return Promise.resolve(tickets.filter(ticket => ticket.paymentMethod === paymentMethod));
    },

    getTicketById(id) {
        const tickets = this.getData();
        return Promise.resolve(tickets.find(ticket => ticket.id === parseInt(id)));
    },

    getTicketsByOrganization(organizationId) {
        const tickets = this.getData();
        return Promise.resolve(tickets.filter(ticket => ticket.organizationId === parseInt(organizationId)));
    },

    getTicketsByDateRange(dateFrom, dateTo) {
        const tickets = this.getData();
        return Promise.resolve(tickets.filter(ticket => {
            const ticketDate = new Date(ticket.purchaseDate);
            return ticketDate >= new Date(dateFrom) && ticketDate <= new Date(dateTo);
        }));
    },

    getTicketsByStatus(status) {
        const tickets = this.getData();
        return Promise.resolve(tickets.filter(ticket => ticket.status === status));
    },

    validateTicket(ticketNumber) {
        console.log('Validating ticket:', ticketNumber);
        const tickets = this.getData();
        const ticket = tickets.find(t => t.ticketNumber === ticketNumber);
        
        if (!ticket) {
            return Promise.resolve({ valid: false, message: 'Билет не найден' });
        }
        
        const now = new Date();
        const validTo = new Date(ticket.validTo);
        
        if (now > validTo) {
            return Promise.resolve({ valid: false, message: 'Билет просрочен' });
        }
        
        if (ticket.status === 'used' && ticket.usageCount >= ticket.maxUsageCount) {
            return Promise.resolve({ valid: false, message: 'Билет уже использован' });
        }
        
        return Promise.resolve({ 
            valid: true, 
            ticket: ticket,
            message: 'Билет действителен'
        });
    },

    useTicket(ticketNumber) {
        console.log('Using ticket:', ticketNumber);
        return Promise.resolve({
            success: true,
            message: `Билет ${ticketNumber} использован`,
            usageDate: new Date().toISOString()
        });
    },

    refundTicket(ticketId, reason) {
        console.log('Refunding ticket:', ticketId, 'reason:', reason);
        return Promise.resolve({
            success: true,
            message: `Билет ${ticketId} возвращен. Причина: ${reason}`,
            refundDate: new Date().toISOString()
        });
    },

    getTicketStatistics(paymentMethod, dateFrom, dateTo) {
        console.log('Getting ticket statistics:', { paymentMethod, dateFrom, dateTo });
        
        const tickets = this.getData().filter(ticket => 
            (!paymentMethod || ticket.paymentMethod === paymentMethod) &&
            new Date(ticket.purchaseDate) >= new Date(dateFrom) &&
            new Date(ticket.purchaseDate) <= new Date(dateTo)
        );
        
        const active = tickets.filter(t => t.status === 'active');
        const used = tickets.filter(t => t.status === 'used');
        const expired = tickets.filter(t => t.status === 'expired');
        
        return Promise.resolve({
            paymentMethod: paymentMethod || 'all',
            dateFrom: dateFrom,
            dateTo: dateTo,
            totalTickets: tickets.length,
            activeTickets: active.length,
            usedTickets: used.length,
            expiredTickets: expired.length,
            totalAmount: tickets.reduce((sum, ticket) => sum + ticket.amount, 0),
            averageAmount: tickets.length > 0 ? (tickets.reduce((sum, ticket) => sum + ticket.amount, 0) / tickets.length).toFixed(2) : 0
        });
    }
};
