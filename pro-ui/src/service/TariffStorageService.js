export class TariffStorageService {
    constructor() {
        this.storageKey = 'tariff_data';
        this.initializeDefaultData();
    }

    // Инициализация данных по умолчанию
    initializeDefaultData() {
        if (!this.getData()) {
            const defaultData = this.getDefaultData();
            this.saveData(defaultData);
        }
    }

    // Получение данных из localStorage
    getData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Ошибка чтения данных из localStorage:', error);
            return null;
        }
    }

    // Сохранение данных в localStorage
    saveData(data) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(data));
        } catch (error) {
            console.error('Ошибка сохранения данных в localStorage:', error);
        }
    }

    // Загрузка данных тарифов
    async loadTariffData() {
        return this.getData();
    }

    // Сохранение данных тарифов
    async saveTariffData(data) {
        this.saveData(data);
    }

    // Получение данных по умолчанию
    getDefaultData() {
        return {
            routes: [
                {
                    id: 'route-1',
                    name: 'Маршрут 1: Центр - Север',
                    description: 'Центральная → Северная → Парковая → Университетская',
                    stations: ['Центральная', 'Северная', 'Парковая', 'Университетская']
                },
                {
                    id: 'route-2',
                    name: 'Маршрут 2: Восток - Запад',
                    description: 'Восточная → Центральная → Западная → Аэропорт',
                    stations: ['Восточная', 'Центральная', 'Западная', 'Аэропорт']
                },
                {
                    id: 'route-3',
                    name: 'Маршрут 3: Юг - Центр',
                    description: 'Южная → Торговая → Центральная → Театральная',
                    stations: ['Южная', 'Торговая', 'Центральная', 'Театральная']
                },
                {
                    id: 'route-4',
                    name: 'Маршрут 4: Кольцевой',
                    description: 'Центральная → Парковая → Университетская → Торговая',
                    stations: ['Центральная', 'Парковая', 'Университетская', 'Торговая']
                }
            ],
            availableTariffs: [
                {
                    id: 'tariff-1',
                    name: 'Базовый',
                    status: 'ACTIVE',
                    tags: 'basic,default'
                },
                {
                    id: 'tariff-2',
                    name: 'Премиум',
                    status: 'ACTIVE',
                    tags: 'premium,comfort'
                },
                {
                    id: 'tariff-3',
                    name: 'Экономный',
                    status: 'ACTIVE',
                    tags: 'economy,student'
                },
                {
                    id: 'tariff-4',
                    name: 'Ночной',
                    status: 'DISABLED',
                    tags: 'night,special'
                }
            ],
            paymentMethods: [
                {
                    id: 'pm-1',
                    type: 'CASH',
                    name: 'Наличные',
                    description: 'Оплата наличными деньгами'
                },
                {
                    id: 'pm-2',
                    type: 'EMV',
                    name: 'Банковская карта',
                    description: 'Оплата банковской картой'
                },
                {
                    id: 'pm-3',
                    type: 'TROIKA_TICKET',
                    name: 'Тройка (билет)',
                    description: 'Транспортная карта Тройка - билет'
                },
                {
                    id: 'pm-4',
                    type: 'TROIKA_WALLET',
                    name: 'Тройка (кошелек)',
                    description: 'Транспортная карта Тройка - кошелек'
                },
                {
                    id: 'pm-5',
                    type: 'ABT_TICKET',
                    name: 'Транспортная карта (билет)',
                    description: 'Транспортная карта - билет'
                },
                {
                    id: 'pm-6',
                    type: 'ABT_WALLET',
                    name: 'Транспортная карта (кошелек)',
                    description: 'Транспортная карта - кошелек'
                },
                {
                    id: 'pm-7',
                    type: 'PROSTOR_TICKET',
                    name: 'Простор (билет)',
                    description: 'Транспортная карта Простор - билет'
                },
                {
                    id: 'pm-8',
                    type: 'QR_TICKET',
                    name: 'QR (билет)',
                    description: 'QR-код - билет'
                }
            ],
            products: [
                {
                    id: 'prod-1',
                    name: 'Разовый проезд',
                    description: 'Проезд в одну сторону',
                    category: 'transport'
                },
                {
                    id: 'prod-2',
                    name: 'Проезд туда-обратно',
                    description: 'Проезд в обе стороны',
                    category: 'transport'
                },
                {
                    id: 'prod-3',
                    name: 'Суточный проезд',
                    description: 'Неограниченное количество поездок в течение дня',
                    category: 'subscription'
                },
                {
                    id: 'prod-4',
                    name: 'Недельный проезд',
                    description: 'Неограниченное количество поездок в течение недели',
                    category: 'subscription'
                },
                {
                    id: 'prod-5',
                    name: 'Месячный проезд',
                    description: 'Неограниченное количество поездок в течение месяца',
                    category: 'subscription'
                }
            ],
            tariffData: [
                {
                    routeId: 'route-1',
                    routeName: 'Маршрут 1: Центр - Север',
                    routeDescription: 'Центральная → Северная → Парковая → Университетская',
                    tariffs: {
                        'tariff-1': {
                            id: 'tariff-1',
                            name: 'Базовый',
                            status: 'ACTIVE',
                            tags: 'basic,default',
                            paymentMethods: [
                                {
                                    id: 'pm-1',
                                    type: 'CASH',
                                    name: 'Наличные',
                                    products: [
                                        {
                                            id: 'prod-1',
                                            name: 'Разовый проезд',
                                            fixedPrice: true,
                                            price: 5000,
                                            tariffMatrix: null
                                        }
                                    ]
                                },
                                {
                                    id: 'pm-2',
                                    type: 'EMV',
                                    name: 'Банковская карта',
                                    products: [
                                        {
                                            id: 'prod-1',
                                            name: 'Разовый проезд',
                                            fixedPrice: false,
                                            price: null,
                                            tariffMatrix: {
                                                'Центральная-Северная': 2800,
                                                'Центральная-Парковая': 4200,
                                                'Центральная-Университетская': 5600,
                                                'Северная-Парковая': 2300,
                                                'Северная-Университетская': 3700,
                                                'Парковая-Университетская': 1800
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        'tariff-2': {
                            id: 'tariff-2',
                            name: 'Премиум',
                            status: 'ACTIVE',
                            tags: 'premium,comfort',
                            paymentMethods: [
                                {
                                    id: 'pm-3',
                                    type: 'TROIKA_TICKET',
                                    name: 'Тройка (билет)',
                                    products: [
                                        {
                                            id: 'prod-1',
                                            name: 'Разовый проезд',
                                            fixedPrice: true,
                                            price: 7500,
                                            tariffMatrix: null
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                },
                {
                    routeId: 'route-2',
                    routeName: 'Маршрут 2: Восток - Запад',
                    routeDescription: 'Восточная → Центральная → Западная → Аэропорт',
                    tariffs: {
                        'tariff-1': {
                            id: 'tariff-1',
                            name: 'Базовый',
                            status: 'ACTIVE',
                            tags: 'basic,default',
                            paymentMethods: [
                                {
                                    id: 'pm-1',
                                    type: 'CASH',
                                    name: 'Наличные',
                                    products: [
                                        {
                                            id: 'prod-1',
                                            name: 'Разовый проезд',
                                            fixedPrice: true,
                                            price: 6000,
                                            tariffMatrix: null
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                },
                {
                    routeId: 'route-3',
                    routeName: 'Маршрут 3: Юг - Центр',
                    routeDescription: 'Южная → Торговая → Центральная → Театральная',
                    tariffs: {}
                },
                {
                    routeId: 'route-4',
                    routeName: 'Маршрут 4: Кольцевой',
                    routeDescription: 'Центральная → Парковая → Университетская → Торговая',
                    tariffs: {
                        'tariff-3': {
                            id: 'tariff-3',
                            name: 'Экономный',
                            status: 'ACTIVE',
                            tags: 'economy,student',
                            paymentMethods: [
                                {
                                    id: 'pm-5',
                                    type: 'ABT_TICKET',
                                    name: 'Транспортная карта (билет)',
                                    products: [
                                        {
                                            id: 'prod-1',
                                            name: 'Разовый проезд',
                                            fixedPrice: true,
                                            price: 3500,
                                            tariffMatrix: null
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }
            ]
        };
    }

    // Добавление тарифа к маршруту
    async addTariffToRoute(routeId, tariffId) {
        const data = this.getData();
        const routeData = data.tariffData.find(item => item.routeId === routeId);
        
        if (routeData) {
            const tariff = data.availableTariffs.find(t => t.id === tariffId);
            if (tariff) {
                routeData.tariffs[tariffId] = {
                    ...tariff,
                    paymentMethods: []
                };
                this.saveData(data);
            }
        }
    }

    // Обновление настроек тарифа
    async updateTariffSettings(routeId, tariffId, settings) {
        const data = this.getData();
        const routeData = data.tariffData.find(item => item.routeId === routeId);
        
        if (routeData && routeData.tariffs[tariffId]) {
            routeData.tariffs[tariffId] = {
                ...routeData.tariffs[tariffId],
                ...settings
            };
            this.saveData(data);
        }
    }

    // Удаление тарифа с маршрута
    async removeTariffFromRoute(routeId, tariffId) {
        const data = this.getData();
        const routeData = data.tariffData.find(item => item.routeId === routeId);
        
        if (routeData && routeData.tariffs[tariffId]) {
            delete routeData.tariffs[tariffId];
            this.saveData(data);
        }
    }

    // Добавление нового тарифа
    async addTariff(tariff) {
        const data = this.getData();
        data.availableTariffs.push(tariff);
        this.saveData(data);
    }

    // Получение маршрута по ID
    getRouteById(routeId) {
        const data = this.getData();
        return data.routes.find(route => route.id === routeId);
    }

    // Получение тарифа по ID
    getTariffById(tariffId) {
        const data = this.getData();
        return data.availableTariffs.find(tariff => tariff.id === tariffId);
    }

    // Получение способа оплаты по ID
    getPaymentMethodById(paymentMethodId) {
        const data = this.getData();
        return data.paymentMethods.find(method => method.id === paymentMethodId);
    }

    // Получение продукта по ID
    getProductById(productId) {
        const data = this.getData();
        return data.products.find(product => product.id === productId);
    }

    // Очистка всех данных
    clearData() {
        localStorage.removeItem(this.storageKey);
    }
} 