// Конфигурация API для разных окружений
const getApiConfig = () => {
    const isDevelopment = import.meta.env.DEV;

    // В режиме разработки проверяем наличие переменных окружения для переопределения URL
    if (isDevelopment) {
        const hasCustomUrls = import.meta.env.VITE_API_SBOL_URL ||
                             import.meta.env.VITE_API_ABT_URL ||
                             import.meta.env.VITE_API_PRO_URL ||
                             import.meta.env.VITE_API_TMS_URL ||
                             import.meta.env.VITE_API_PRO_GATE_PRIVATE_URL;

        // Если есть кастомные URL, используем их
        if (hasCustomUrls) {
            return {
                sbol: import.meta.env.VITE_API_SBOL_URL || 'http://localhost:8083',
                abt: import.meta.env.VITE_API_ABT_URL || 'http://localhost:8086',
                pro: import.meta.env.VITE_API_PRO_URL || 'http://localhost:8085',
                tms: import.meta.env.VITE_API_TMS_URL || 'http://localhost:8088',
                proGatePrivate: import.meta.env.VITE_API_PRO_GATE_PRIVATE_URL || 'http://localhost:5015'
            };
        }

        // В режиме разработки используем localhost с разными портами
        return {
            sbol: 'http://localhost:8083',
            abt: 'http://localhost:8086',
            pro: 'http://localhost:8085',
            tms: 'http://localhost:8088',
            proGatePrivate: 'http://localhost:5015'
        };
    }

    // В продакшене всегда используем пустой базовый URL (текущий домен)
    return {
        sbol: '',
        abt: '',
        pro: '',
        tms: '',
        proGatePrivate: ''
    };
};

export default getApiConfig;
