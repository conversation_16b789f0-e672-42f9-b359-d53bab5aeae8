// Конфигурация Keycloak
const getKeycloakConfig = () => {
    // Получаем значения из переменных окружения или используем значения по умолчанию
    const keycloakUrl = import.meta.env.VITE_KEYCLOAK_URL || 'https://dev-auth.sbertroika.tech/';
    const realm = import.meta.env.VITE_KEYCLOAK_REALM || 'test-asop';
    const clientId = import.meta.env.VITE_KEYCLOAK_CLIENT_ID || 'crm-ui-local';
    
    return {
        url: keycloakUrl,
        realm: realm,
        clientId: clientId,
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
        checkLoginIframe: false,
        enableLogging: import.meta.env.DEV
    };
};

export default getKeycloakConfig; 