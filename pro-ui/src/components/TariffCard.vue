<template>
    <div class="card">
        <div class="flex justify-between items-start mb-6">
            <div>
                <h2 class="text-2xl font-bold mb-2">{{ tariff.name }}</h2>
                <Tag 
                    :value="getStatusLabel(tariff.status)" 
                    :severity="getStatusSeverity(tariff.status)"
                    class="text-sm"
                />
            </div>
            <div class="flex gap-2">
                <Button 
                    icon="pi pi-pencil" 
                    label="Редактировать" 
                    class="p-button-outlined p-button-success"
                    @click="$emit('edit', tariff)"
                />
                <Button 
                    v-if="tariff.status === 'DISABLED'"
                    icon="pi pi-check" 
                    label="Активировать" 
                    class="p-button-success"
                    @click="$emit('activate', tariff.id)"
                />
                <Button 
                    v-if="tariff.status === 'ACTIVE'"
                    icon="pi pi-times" 
                    label="Деактивировать" 
                    class="p-button-warning"
                    @click="$emit('deactivate', tariff.id)"
                />
                <Button 
                    v-if="tariff.status !== 'IS_DELETED'"
                    icon="pi pi-trash" 
                    label="Удалить" 
                    class="p-button-danger"
                    @click="$emit('delete', tariff)"
                />
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Основная информация -->
            <div class="space-y-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800">Основная информация</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">ID:</span>
                            <span class="font-mono text-sm">{{ tariff.id }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Версия:</span>
                            <span class="font-medium">{{ tariff.version }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Проект:</span>
                            <span class="font-medium">{{ tariff.projectId }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Дата создания:</span>
                            <span class="font-medium">{{ formatDate(tariff.versionCreatedAt) }}</span>
                        </div>
                        <div v-if="tariff.versionCreatedBy" class="flex justify-between">
                            <span class="text-gray-600">Создан пользователем:</span>
                            <span class="font-medium">{{ tariff.versionCreatedBy }}</span>
                        </div>
                    </div>
                </div>

                <!-- Теги -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800">Теги</h3>
                    <div v-if="tariff.tags" class="flex flex-wrap gap-2">
                        <Tag 
                            v-for="tag in tariff.tags.split(',')" 
                            :key="tag" 
                            :value="tag.trim()" 
                            severity="info"
                            class="text-sm"
                        />
                    </div>
                    <span v-else class="text-gray-400">Теги не указаны</span>
                </div>
            </div>

            <!-- Статус и действия -->
            <div class="space-y-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800">Статус</h3>
                    <div class="space-y-3">
                        <div class="flex items-center gap-3">
                            <Tag 
                                :value="getStatusLabel(tariff.status)" 
                                :severity="getStatusSeverity(tariff.status)"
                                class="text-base"
                            />
                        </div>
                        <div class="text-sm text-gray-600">
                            {{ getStatusDescription(tariff.status) }}
                        </div>
                    </div>
                </div>

                <!-- История изменений -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800">История изменений</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Текущая версия:</span>
                            <span class="font-medium">{{ tariff.version }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Последнее изменение:</span>
                            <span class="font-medium">{{ formatDate(tariff.versionCreatedAt) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Дополнительные действия -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <h3 class="text-lg font-semibold mb-3 text-gray-800">Дополнительные действия</h3>
            <div class="flex flex-wrap gap-3">
                <Button 
                    icon="pi pi-copy" 
                    label="Создать копию" 
                    class="p-button-outlined p-button-info"
                    @click="$emit('copy', tariff)"
                />
                <Button 
                    icon="pi pi-history" 
                    label="История версий" 
                    class="p-button-outlined p-button-secondary"
                    @click="$emit('history', tariff.id)"
                />
                <Button 
                    icon="pi pi-download" 
                    label="Экспорт" 
                    class="p-button-outlined"
                    @click="$emit('export', tariff)"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import Tag from 'primevue/tag';
import Button from 'primevue/button';

// Props
const props = defineProps({
    tariff: {
        type: Object,
        required: true
    }
});

// Emits
const emit = defineEmits(['edit', 'activate', 'deactivate', 'delete', 'copy', 'history', 'export']);

// Методы
const getStatusLabel = (status) => {
    const statusMap = {
        'ACTIVE': 'Активный',
        'DISABLED': 'Отключен',
        'BLOCKED': 'Заблокирован',
        'IS_DELETED': 'Удален'
    };
    return statusMap[status] || status;
};

const getStatusSeverity = (status) => {
    const severityMap = {
        'ACTIVE': 'success',
        'DISABLED': 'warning',
        'BLOCKED': 'danger',
        'IS_DELETED': 'secondary'
    };
    return severityMap[status] || 'info';
};

const getStatusDescription = (status) => {
    const descriptions = {
        'ACTIVE': 'Тариф активен и доступен для использования',
        'DISABLED': 'Тариф временно отключен',
        'BLOCKED': 'Тариф заблокирован и недоступен',
        'IS_DELETED': 'Тариф удален из системы'
    };
    return descriptions[status] || 'Статус не определен';
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('ru-RU');
};
</script>

<style scoped>
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.space-y-4 > * + * {
    margin-top: 1rem;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.space-y-2 > * + * {
    margin-top: 0.5rem;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

.gap-6 {
    gap: 1.5rem;
}

.gap-3 {
    gap: 0.75rem;
}

.gap-2 {
    gap: 0.5rem;
}

.flex {
    display: flex;
}

.flex-wrap {
    flex-wrap: wrap;
}

.justify-between {
    justify-content: space-between;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.text-gray-600 {
    color: #4b5563;
}

.text-gray-800 {
    color: #1f2937;
}

.text-gray-400 {
    color: #9ca3af;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.font-mono {
    font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mt-6 {
    margin-top: 1.5rem;
}

.pt-6 {
    padding-top: 1.5rem;
}

.p-4 {
    padding: 1rem;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.border-t {
    border-top-width: 1px;
}

.border-gray-200 {
    border-color: #e5e7eb;
}
</style> 