<template>
    <div class="tariff-settings-editor">
        <Dialog 
            :visible="visible" 
            header="Редактирование настроек тарифа" 
            modal 
            class="p-fluid"
            :style="{ width: '90rem', maxWidth: '95vw' }"
            :closable="true"
            @update:visible="onVisibleChange"
            @hide="onClose"
        >
            <div class="grid grid-cols-1 gap-6">
                <!-- Информация о тарифе -->
                <div class="card p-4 bg-blue-50 border-round">
                    <h3 class="text-lg font-semibold mb-3 text-blue-900">
                        <i class="pi pi-info-circle mr-2"></i>
                        Информация о тарифе
                    </h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div>
                            <label class="font-medium text-gray-700">Название:</label>
                            <p class="text-lg">{{ tariffInfo.name }}</p>
                        </div>
                        <div>
                            <label class="font-medium text-gray-700">Статус:</label>
                            <Tag 
                                :value="getStatusLabel(tariffInfo.status)" 
                                :severity="getStatusSeverity(tariffInfo.status)"
                            />
                        </div>
                        <div>
                            <label class="font-medium text-gray-700">Теги:</label>
                            <div class="flex flex-wrap gap-1 mt-1">
                                <Tag 
                                    v-for="tag in tariffInfo.tags?.split(',') || []" 
                                    :key="tag" 
                                    :value="tag.trim()" 
                                    severity="info"
                                    class="text-xs"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ограничения тарифа -->
                <div class="card p-4">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="pi pi-shield mr-2"></i>
                        Ограничения действия тарифа
                    </h3>
                    
                    <div class="grid grid-cols-1 gap-4">
                        <!-- Ограничения по маршрутам -->
                        <div class="border-1 border-gray-200 border-round p-4">
                            <h4 class="font-medium mb-3 text-blue-800">
                                <i class="pi pi-map mr-2"></i>
                                Ограничения по маршрутам
                            </h4>
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="font-medium text-gray-700 mb-2">Базовое правило:</label>
                                    <Dropdown 
                                        v-model="tariffRouteConstraints.baseRule" 
                                        :options="baseRuleOptions" 
                                        optionLabel="label" 
                                        optionValue="value" 
                                        placeholder="Выберите правило"
                                        class="w-full"
                                    />
                                </div>
                                <div class="flex items-end">
                                    <Button 
                                        label="Добавить исключение" 
                                        icon="pi pi-plus" 
                                        class="p-button-outlined p-button-sm"
                                        @click="addRouteException"
                                    />
                                </div>
                            </div>
                            
                            <!-- Список исключений -->
                            <div v-if="tariffRouteConstraints.exceptions.length > 0">
                                <h5 class="font-medium text-gray-700 mb-2">Исключения:</h5>
                                <div class="space-y-2">
                                    <div 
                                        v-for="(exception, index) in tariffRouteConstraints.exceptions" 
                                        :key="index"
                                        class="flex items-center gap-4 p-3 bg-gray-50 border-round"
                                    >
                                        <div class="flex-1">
                                            <Dropdown 
                                                v-model="exception.routeId" 
                                                :options="routeOptions" 
                                                optionLabel="name" 
                                                optionValue="id" 
                                                placeholder="Выберите маршрут"
                                                class="w-full"
                                            />
                                        </div>
                                        <div class="w-32">
                                            <Dropdown 
                                                v-model="exception.action" 
                                                :options="actionOptions" 
                                                optionLabel="label" 
                                                optionValue="value" 
                                                placeholder="Действие"
                                                class="w-full"
                                            />
                                        </div>
                                        <Button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-danger p-button-text" 
                                            @click="removeRouteException(index)"
                                            v-tooltip.top="'Удалить'"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Ограничения по транспорту -->
                        <div class="border-1 border-gray-200 border-round p-4">
                            <h4 class="font-medium mb-3 text-green-800">
                                <i class="pi pi-car mr-2"></i>
                                Ограничения по транспорту
                            </h4>
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="font-medium text-gray-700 mb-2">Базовое правило:</label>
                                    <Dropdown 
                                        v-model="transportConstraints.baseRule" 
                                        :options="baseRuleOptions" 
                                        optionLabel="label" 
                                        optionValue="value" 
                                        placeholder="Выберите правило"
                                        class="w-full"
                                    />
                                </div>
                                <div class="flex items-end">
                                    <Button 
                                        label="Добавить исключение" 
                                        icon="pi pi-plus" 
                                        class="p-button-outlined p-button-sm"
                                        @click="addTransportException"
                                    />
                                </div>
                            </div>
                            
                            <!-- Список исключений -->
                            <div v-if="transportConstraints.exceptions.length > 0">
                                <h5 class="font-medium text-gray-700 mb-2">Исключения:</h5>
                                <div class="space-y-2">
                                    <div 
                                        v-for="(exception, index) in transportConstraints.exceptions" 
                                        :key="index"
                                        class="flex items-center gap-4 p-3 bg-gray-50 border-round"
                                    >
                                        <div class="flex-1">
                                            <Dropdown 
                                                v-model="exception.vehicleId" 
                                                :options="vehicleOptions" 
                                                optionLabel="name" 
                                                optionValue="id" 
                                                placeholder="Выберите транспорт"
                                                class="w-full"
                                            />
                                        </div>
                                        <div class="w-32">
                                            <Dropdown 
                                                v-model="exception.action" 
                                                :options="actionOptions" 
                                                optionLabel="label" 
                                                optionValue="value" 
                                                placeholder="Действие"
                                                class="w-full"
                                            />
                                        </div>
                                        <Button 
                                            icon="pi pi-trash" 
                                            class="p-button-rounded p-button-danger p-button-text" 
                                            @click="removeTransportException(index)"
                                            v-tooltip.top="'Удалить'"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Стоимость тарифа -->
                <div class="card p-4">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="pi pi-dollar mr-2"></i>
                        Стоимость тарифа по способам оплаты
                    </h3>
                    
                    <div class="grid grid-cols-1 gap-4">
                        <!-- Тарифы по способам оплаты -->
                        <div class="border-1 border-gray-200 border-round p-4">
                            <h4 class="font-medium mb-3 text-purple-800">
                                <i class="pi pi-credit-card mr-2"></i>
                                Тарифы по способам оплаты
                            </h4>
                            <div class="grid grid-cols-1 gap-4">
                                <div 
                                    v-for="price in paymentMethods" 
                                    :key="price.method"
                                    class="p-4 bg-gray-50 border-round"
                                >
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="font-medium text-gray-700">
                                            {{ getPaymentMethodLabel(price.method) }}
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="text-sm text-gray-600">Фиксированный тариф</span>
                                            <InputSwitch 
                                                v-model="price.isFixed" 
                                            />
                                        </div>
                                    </div>
                                    
                                    <div v-if="price.isFixed" class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="font-medium text-gray-700 mb-2">Стоимость (в копейках):</label>
                                            <InputNumber 
                                                v-model="price.amount" 
                                                :min="0" 
                                                :max="1000000"
                                                class="w-full"
                                                placeholder="Введите стоимость"
                                            />
                                        </div>
                                        <div class="flex items-end">
                                            <div class="text-lg font-bold text-green-600">
                                                {{ formatPrice(price.amount) }} ₽
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else class="text-center p-4 bg-blue-50 border-round">
                                        <div class="text-sm text-blue-600 mb-2">
                                            <i class="pi pi-table mr-1"></i>
                                            Тарифная матрица
                                        </div>
                                        <div class="text-sm text-gray-500 mb-3">
                                            Стоимость зависит от станций. Каждый способ оплаты имеет свою матрицу.
                                        </div>
                                        <Button 
                                            label="Настроить матрицу" 
                                            icon="pi pi-cog" 
                                            class="p-button-outlined p-button-sm"
                                            @click="configureMatrix(price.method)"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        <i class="pi pi-info-circle mr-1"></i>
                        Редактирование настроек тарифа "{{ tariffInfo.name }}"
                    </div>
                    <div class="flex gap-2">
                        <Button 
                            label="Отмена" 
                            icon="pi pi-times" 
                            @click="onClose" 
                            class="p-button-secondary"
                        />
                        <Button 
                            label="Сохранить" 
                            icon="pi pi-check" 
                            @click="saveSettings"
                            :loading="saving"
                            class="p-button-primary"
                        />
                    </div>
                </div>
            </template>
        </Dialog>

        <!-- Диалог настройки тарифной матрицы -->
        <Dialog 
            :visible="matrixDialogVisible" 
            :header="`Настройка тарифной матрицы - ${getPaymentMethodLabel(currentMatrixMethod)}`"
            modal 
            class="p-fluid"
            :style="{ width: '90rem', maxWidth: '95vw' }"
            :closable="true"
            @update:visible="matrixDialogVisible = $event"
        >
            <div class="grid grid-cols-1 gap-4">
                <!-- Выбор маршрута -->
                <div class="p-4 bg-blue-50 border-round">
                    <h4 class="font-medium mb-2 text-blue-800">
                        <i class="pi pi-route mr-2"></i>
                        Выбор маршрута
                    </h4>
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Выберите маршрут для настройки матрицы:
                        </label>
                        <Dropdown
                            v-model="selectedRouteForMethod[currentMatrixMethod]"
                            :options="getAvailableRoutesForMethod(currentMatrixMethod)"
                            optionLabel="name"
                            optionValue="id"
                            placeholder="Выберите маршрут"
                            class="w-full md:w-20rem"
                            @change="onRouteChange(currentMatrixMethod)"
                        />
                    </div>
                    <p class="text-sm text-gray-600">
                        Матрица настраивается отдельно для каждого маршрута. 
                        Доступны только маршруты, разрешенные для данного способа оплаты.
                    </p>
                </div>

                <!-- Матрица для выбранного маршрута -->
                <div v-if="selectedRouteForMethod[currentMatrixMethod]" class="border-1 border-gray-200 border-round p-4">
                    <div class="mb-3">
                        <h4 class="font-medium text-gray-800">
                            <i class="pi pi-table mr-2"></i>
                            Матрица для маршрута: {{ getRouteName(selectedRouteForMethod[currentMatrixMethod]) }}
                        </h4>
                        <p class="text-sm text-gray-600 mt-1">
                            Настройте стоимость для каждой пары станций
                        </p>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="border border-gray-300 p-2 text-left">От станции</th>
                                    <th class="border border-gray-300 p-2 text-left">До станции</th>
                                    <th class="border border-gray-300 p-2 text-center">Стоимость (₽)</th>
                                    <th class="border border-gray-300 p-2 text-center">Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr 
                                    v-for="stationPair in getStationPairsForRoute(selectedRouteForMethod[currentMatrixMethod])" 
                                    :key="`${stationPair.from}-${stationPair.to}`"
                                    class="hover:bg-gray-50"
                                >
                                    <td class="border border-gray-300 p-2">
                                        {{ stationPair.from }}
                                    </td>
                                    <td class="border border-gray-300 p-2">
                                        {{ stationPair.to }}
                                    </td>
                                    <td class="border border-gray-300 p-2 text-center">
                                        <InputNumber 
                                            :model-value="getMatrixPriceForEdit(currentMatrixMethod, selectedRouteForMethod[currentMatrixMethod], stationPair.from, stationPair.to)" 
                                            mode="decimal" 
                                            :minFractionDigits="2" 
                                            :maxFractionDigits="2"
                                            class="w-24"
                                            :disabled="true"
                                            placeholder="0.00"
                                        />
                                    </td>
                                    <td class="border border-gray-300 p-2 text-center">
                                        <Button 
                                            icon="pi pi-pencil" 
                                            class="p-button-rounded p-button-text p-button-sm" 
                                            :disabled="true"
                                            v-tooltip.top="'Редактировать'"
                                        />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 border-round">
                        <div class="flex items-center">
                            <i class="pi pi-info-circle text-yellow-600 mr-2"></i>
                            <span class="text-sm text-yellow-800">
                                Функция редактирования матрицы будет доступна в следующих версиях
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Сообщение если маршрут не выбран -->
                <div v-else class="text-center p-8 bg-gray-50 border-round">
                    <div class="text-gray-500">
                        <i class="pi pi-route mr-2 text-2xl"></i>
                        <p class="mt-2">Выберите маршрут для настройки тарифной матрицы</p>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        <i class="pi pi-wrench mr-1"></i>
                        В разработке
                    </div>
                    <div class="flex gap-2">
                        <Button 
                            label="Отмена" 
                            icon="pi pi-times" 
                            @click="matrixDialogVisible = false" 
                            class="p-button-secondary"
                        />
                        <Button 
                            label="Сохранить" 
                            icon="pi pi-check" 
                            :disabled="true"
                            class="p-button-primary"
                        />
                    </div>
                </div>
            </template>
        </Dialog>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import Dialog from 'primevue/dialog'
import Button from 'primevue/button'
import Tag from 'primevue/tag'
import InputSwitch from 'primevue/inputswitch'
import InputNumber from 'primevue/inputnumber'
import Dropdown from 'primevue/dropdown'

// Props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    tariffId: {
        type: String,
        default: null
    }
})

// Emits
const emit = defineEmits(['update:visible', 'save'])

// Состояние
const saving = ref(false)
const matrixDialogVisible = ref(false)
const currentMatrixMethod = ref(null)
const samplePrice = ref(45.50)
const samplePrice2 = ref(67.20)

// Захардкоженные данные для прототипа
const tariffInfo = ref({
    name: 'Базовый тариф',
    status: 'ACTIVE',
    tags: 'basic,default,premium'
})

const tariffRouteConstraints = ref({
    baseRule: 'ALLOW',
    exceptions: []
})

// Захардкоженные маршруты
const routes = ref([
    { id: 'route-1', name: 'Маршрут 1: Центр - Север', stations: ['Центральная', 'Северная', 'Парковая', 'Университетская'] },
    { id: 'route-2', name: 'Маршрут 2: Восток - Запад', stations: ['Восточная', 'Центральная', 'Западная', 'Аэропорт'] },
    { id: 'route-3', name: 'Маршрут 3: Юг - Центр', stations: ['Южная', 'Торговая', 'Центральная', 'Театральная'] }
])

// Ограничения по маршрутам для способов оплаты
const routeConstraints = ref({
    'EMV': ['route-1', 'route-2'], // EMV доступен только на маршрутах 1 и 2
    'ABT_WALLET': ['route-1', 'route-3'] // ABT_WALLET доступен только на маршрутах 1 и 3
})

// Выбранные маршруты для каждого способа оплаты
const selectedRouteForMethod = ref({})

// Тарифные матрицы по способам оплаты и маршрутам
const tariffMatrices = ref({
    'EMV': {
        'route-1': {
            'Центральная-Северная': 2800,
            'Центральная-Парковая': 4200,
            'Центральная-Университетская': 5600,
            'Северная-Парковая': 2300,
            'Северная-Университетская': 3700,
            'Парковая-Университетская': 1800
        },
        'route-2': {
            'Восточная-Центральная': 3200,
            'Восточная-Западная': 4800,
            'Восточная-Аэропорт': 6400,
            'Центральная-Западная': 2500,
            'Центральная-Аэропорт': 4100,
            'Западная-Аэропорт': 2000
        }
    },
    'ABT_WALLET': {
        'route-1': {
            'Центральная-Северная': 3200,
            'Центральная-Парковая': 4800,
            'Центральная-Университетская': 6400,
            'Северная-Парковая': 2700,
            'Северная-Университетская': 4300,
            'Парковая-Университетская': 2200
        },
        'route-3': {
            'Южная-Торговая': 3500,
            'Южная-Центральная': 5200,
            'Южная-Театральная': 6800,
            'Торговая-Центральная': 2800,
            'Торговая-Театральная': 4500,
            'Центральная-Театральная': 2400
        }
    }
})

const transportConstraints = ref({
    baseRule: 'ALLOW',
    exceptions: []
})

const paymentMethods = ref([
    { method: 'CASH', amount: 5000, isFixed: true },
    { method: 'EMV', amount: 4800, isFixed: false },
    { method: 'TROIKA_TICKET', amount: 4500, isFixed: true },
    { method: 'TROIKA_WALLET', amount: 4300, isFixed: true },
    { method: 'ABT_TICKET', amount: 4600, isFixed: true },
    { method: 'ABT_WALLET', amount: 4400, isFixed: false },
    { method: 'PROSTOR_TICKET', amount: 4700, isFixed: true },
    { method: 'QR_TICKET', amount: 4900, isFixed: true }
])

// Опции для выпадающих списков
const baseRuleOptions = [
    { label: 'Разрешено', value: 'ALLOW' },
    { label: 'Запрещено', value: 'DENY' }
]

const actionOptions = [
    { label: 'Разрешить', value: 'ALLOW' },
    { label: 'Запретить', value: 'DENY' }
]

const routeOptions = [
    { id: '1', name: 'Маршрут №1 (001)' },
    { id: '2', name: 'Маршрут №2 (002)' },
    { id: '3', name: 'Маршрут №3 (003)' },
    { id: '4', name: 'Маршрут №4 (004)' },
    { id: '5', name: 'Маршрут №5 (005)' }
]

const vehicleOptions = [
    { id: '1', name: 'Автобус №123' },
    { id: '2', name: 'Автобус №124' },
    { id: '3', name: 'Троллейбус №45' },
    { id: '4', name: 'Троллейбус №46' },
    { id: '5', name: 'Трамвай №12' }
]

// Методы
const getStatusLabel = (status) => {
    const labels = {
        'ACTIVE': 'Активен',
        'DISABLED': 'Отключен',
        'BLOCKED': 'Заблокирован',
        'IS_DELETED': 'Удален'
    }
    return labels[status] || status
}

const getStatusSeverity = (status) => {
    const severities = {
        'ACTIVE': 'success',
        'DISABLED': 'warning',
        'BLOCKED': 'danger',
        'IS_DELETED': 'secondary'
    }
    return severities[status] || 'info'
}

const getPaymentMethodLabel = (method) => {
    const labels = {
        'CASH': 'Наличные',
        'EMV': 'Банковская карта',
        'TROIKA_TICKET': 'Тройка (билет)',
        'TROIKA_WALLET': 'Тройка (кошелек)',
        'ABT_TICKET': 'Транспортная карта (билет)',
        'ABT_WALLET': 'Транспортная карта (кошелек)',
        'PROSTOR_TICKET': 'Простор (билет)',
        'QR_TICKET': 'QR (билет)',
        'QR_WALLET': 'QR (кошелек)'
    }
    return labels[method] || method
}

const formatPrice = (amount) => {
    return (amount / 100).toFixed(2)
}

const onClose = () => {
    emit('update:visible', false)
}

const onVisibleChange = (value) => {
    emit('update:visible', value)
}

const addRouteException = () => {
    tariffRouteConstraints.value.exceptions.push({
        routeId: null,
        action: 'ALLOW'
    })
}

const removeRouteException = (index) => {
    tariffRouteConstraints.value.exceptions.splice(index, 1)
}

const addTransportException = () => {
    transportConstraints.value.exceptions.push({
        vehicleId: null,
        action: 'ALLOW'
    })
}

const removeTransportException = (index) => {
    transportConstraints.value.exceptions.splice(index, 1)
}

const configureMatrix = (method) => {
    currentMatrixMethod.value = method
    matrixDialogVisible.value = true
}

const getAvailableRoutesForMethod = (method) => {
    const availableRouteIds = routeConstraints.value[method] || []
    return routes.value.filter(route => availableRouteIds.includes(route.id))
}

const getRouteName = (routeId) => {
    const route = routes.value.find(r => r.id === routeId)
    return route ? route.name : 'Неизвестный маршрут'
}

const getStationPairsForRoute = (routeId) => {
    const route = routes.value.find(r => r.id === routeId)
    if (!route) return []
    
    const pairs = []
    const stations = route.stations
    
    for (let i = 0; i < stations.length; i++) {
        for (let j = i + 1; j < stations.length; j++) {
            pairs.push({
                from: stations[i],
                to: stations[j]
            })
        }
    }
    
    return pairs
}

const getMatrixPriceForEdit = (method, routeId, stationFrom, stationTo) => {
    const methodMatrices = tariffMatrices.value[method]
    if (!methodMatrices) return 0
    
    const routeMatrix = methodMatrices[routeId]
    if (!routeMatrix) return 0
    
    const key = `${stationFrom}-${stationTo}`
    const priceInKopecks = routeMatrix[key] || 0
    return priceInKopecks / 100 // Конвертируем копейки в рубли для отображения
}

const onRouteChange = (method) => {
    console.log(`Маршрут изменен для метода ${method}:`, selectedRouteForMethod.value[method])
}

const saveSettings = async () => {
    try {
        saving.value = true
        
        // TODO: Реализовать сохранение настроек
        console.log('Сохранение настроек:', {
            tariffId: props.tariffId,
            tariffRouteConstraints: tariffRouteConstraints.value,
            transportConstraints: transportConstraints.value,
            paymentMethods: paymentMethods.value
        })
        
        // Имитация задержки
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        emit('save', {
            tariffId: props.tariffId,
            tariffRouteConstraints: tariffRouteConstraints.value,
            transportConstraints: transportConstraints.value,
            paymentMethods: paymentMethods.value
        })
        
        onClose()
    } catch (error) {
        console.error('Ошибка сохранения настроек:', error)
    } finally {
        saving.value = false
    }
}
</script>

<style scoped>
.tariff-settings-editor {
    /* Дополнительные стили при необходимости */
}
</style> 