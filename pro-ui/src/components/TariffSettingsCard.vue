<template>
    <div class="tariff-settings-card">
        <Dialog 
            :visible="visible" 
            header="Настройки тарифа" 
            modal 
            class="p-fluid"
            :style="{ width: '90rem', maxWidth: '95vw' }"
            :closable="true"
            @update:visible="onVisibleChange"
            @hide="onClose"
        >
            <div class="grid grid-cols-1 gap-6">
                <!-- Информация о тарифе -->
                <div class="card p-4 bg-blue-50 border-round">
                    <h3 class="text-lg font-semibold mb-3 text-blue-900">
                        <i class="pi pi-info-circle mr-2"></i>
                        Информация о тарифе
                    </h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div>
                            <label class="font-medium text-gray-700">Название:</label>
                            <p class="text-lg">{{ tariffInfo.name }}</p>
                        </div>
                        <div>
                            <label class="font-medium text-gray-700">Статус:</label>
                            <Tag 
                                :value="getStatusLabel(tariffInfo.status)" 
                                :severity="getStatusSeverity(tariffInfo.status)"
                            />
                        </div>
                        <div>
                            <label class="font-medium text-gray-700">Теги:</label>
                            <div class="flex flex-wrap gap-1 mt-1">
                                <Tag 
                                    v-for="tag in tariffInfo.tags?.split(',') || []" 
                                    :key="tag" 
                                    :value="tag.trim()" 
                                    severity="info"
                                    class="text-xs"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ограничения тарифа -->
                <div class="card p-4">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="pi pi-shield mr-2"></i>
                        Ограничения действия тарифа
                    </h3>
                    
                    <div class="grid grid-cols-1 gap-4">
                        <!-- Ограничения по маршрутам -->
                        <div class="border-1 border-gray-200 border-round p-4">
                            <h4 class="font-medium mb-3 text-blue-800">
                                <i class="pi pi-map mr-2"></i>
                                Ограничения по маршрутам
                            </h4>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label class="font-medium text-gray-700">Базовое правило:</label>
                                    <div v-if="tariffRouteConstraints.exceptions.length === 0" class="mt-1">
                                        <Tag 
                                            :value="tariffRouteConstraints.baseRule === 'ALLOW' ? 'Разрешено на всех маршрутах' : 'Запрещено на всех маршрутах'" 
                                            :severity="tariffRouteConstraints.baseRule === 'ALLOW' ? 'success' : 'danger'"
                                        />
                                    </div>
                                    <div v-else class="mt-1">
                                        <Tag 
                                            :value="tariffRouteConstraints.baseRule === 'ALLOW' ? 'Разрешено' : 'Запрещено'" 
                                            :severity="tariffRouteConstraints.baseRule === 'ALLOW' ? 'success' : 'danger'"
                                        />
                                        <p class="text-sm text-gray-600 mt-1">
                                            {{ tariffRouteConstraints.exceptions.length }} исключений
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Список исключений -->
                            <div v-if="tariffRouteConstraints.exceptions.length > 0" class="mt-3">
                                <h5 class="font-medium text-gray-700 mb-2">Исключения:</h5>
                                <div class="space-y-2">
                                    <div 
                                        v-for="exception in tariffRouteConstraints.exceptions" 
                                        :key="exception.id"
                                        class="flex items-center justify-between p-2 bg-gray-50 border-round"
                                    >
                                        <div>
                                            <span class="font-medium">{{ exception.name }}</span>
                                            <span class="text-sm text-gray-500 ml-2">({{ exception.number }})</span>
                                        </div>
                                        <Tag 
                                            :value="exception.action === 'ALLOW' ? 'Разрешено' : 'Запрещено'" 
                                            :severity="exception.action === 'ALLOW' ? 'success' : 'danger'"
                                            class="text-xs"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Ограничения по транспорту -->
                        <div class="border-1 border-gray-200 border-round p-4">
                            <h4 class="font-medium mb-3 text-green-800">
                                <i class="pi pi-car mr-2"></i>
                                Ограничения по транспорту
                            </h4>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label class="font-medium text-gray-700">Базовое правило:</label>
                                    <div v-if="transportConstraints.exceptions.length === 0" class="mt-1">
                                        <Tag 
                                            :value="transportConstraints.baseRule === 'ALLOW' ? 'Разрешено на всех транспортных средствах' : 'Запрещено на всех транспортных средствах'" 
                                            :severity="transportConstraints.baseRule === 'ALLOW' ? 'success' : 'danger'"
                                        />
                                    </div>
                                    <div v-else class="mt-1">
                                        <Tag 
                                            :value="transportConstraints.baseRule === 'ALLOW' ? 'Разрешено' : 'Запрещено'" 
                                            :severity="transportConstraints.baseRule === 'ALLOW' ? 'success' : 'danger'"
                                        />
                                        <p class="text-sm text-gray-600 mt-1">
                                            {{ transportConstraints.exceptions.length }} исключений
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Список исключений -->
                            <div v-if="transportConstraints.exceptions.length > 0" class="mt-3">
                                <h5 class="font-medium text-gray-700 mb-2">Исключения:</h5>
                                <div class="space-y-2">
                                    <div 
                                        v-for="exception in transportConstraints.exceptions" 
                                        :key="exception.id"
                                        class="flex items-center justify-between p-2 bg-gray-50 border-round"
                                    >
                                        <div>
                                            <span class="font-medium">{{ exception.name }}</span>
                                            <span class="text-sm text-gray-500 ml-2">({{ exception.number }})</span>
                                        </div>
                                        <Tag 
                                            :value="exception.action === 'ALLOW' ? 'Разрешено' : 'Запрещено'" 
                                            :severity="exception.action === 'ALLOW' ? 'success' : 'danger'"
                                            class="text-xs"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Стоимость тарифа -->
                <div class="card p-4">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="pi pi-dollar mr-2"></i>
                        Стоимость тарифа по способам оплаты
                    </h3>
                    
                    <div class="grid grid-cols-1 gap-4">
                        <!-- Тарифы по способам оплаты -->
                        <div class="border-1 border-gray-200 border-round p-4">
                            <h4 class="font-medium mb-3 text-purple-800">
                                <i class="pi pi-credit-card mr-2"></i>
                                Тарифы по способам оплаты
                            </h4>
                            <div class="grid grid-cols-1 gap-4">
                                <div 
                                    v-for="price in paymentMethods" 
                                    :key="price.method"
                                    class="p-4 bg-gray-50 border-round"
                                >
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="font-medium text-gray-700">
                                            {{ getPaymentMethodLabel(price.method) }}
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="text-sm text-gray-600">Фиксированный тариф</span>
                                            <InputSwitch 
                                                v-model="price.isFixed" 
                                                :disabled="true"
                                            />
                                        </div>
                                    </div>
                                    
                                    <div v-if="price.isFixed" class="text-center">
                                        <div class="text-lg font-bold text-green-600">
                                            {{ formatPrice(price.amount) }} ₽
                                        </div>
                                        <div class="text-sm text-gray-500 mt-1">
                                            Фиксированная стоимость
                                        </div>
                                    </div>
                                    <div v-else class="text-center">
                                        <div class="text-sm text-blue-600">
                                            <i class="pi pi-table mr-1"></i>
                                            Тарифная матрица
                                        </div>
                                        <div class="text-sm text-gray-500 mt-1">
                                            Стоимость зависит от станций
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Тарифные матрицы по способам оплаты -->
                        <div v-for="method in paymentMethods.filter(m => !m.isFixed)" :key="method.method" class="border-1 border-gray-200 border-round p-4">
                            <h4 class="font-medium mb-3 text-orange-800">
                                <i class="pi pi-table mr-2"></i>
                                Тарифная матрица - {{ getPaymentMethodLabel(method.method) }}
                            </h4>
                            <div class="mb-3">
                                <p class="text-sm text-gray-600">
                                    Стоимость зависит от маршрута и станций для способа оплаты "{{ getPaymentMethodLabel(method.method) }}"
                                </p>
                            </div>
                            
                            <!-- Выбор маршрута -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Выберите маршрут для просмотра матрицы:
                                </label>
                                <Dropdown
                                    v-model="selectedRouteForMethod[method.method]"
                                    :options="getAvailableRoutesForMethod(method.method)"
                                    optionLabel="name"
                                    optionValue="id"
                                    placeholder="Выберите маршрут"
                                    class="w-full md:w-20rem"
                                    @change="onRouteChange(method.method)"
                                />
                            </div>

                            <!-- Матрица для выбранного маршрута -->
                            <div v-if="selectedRouteForMethod[method.method]" class="overflow-x-auto">
                                <div class="mb-2">
                                    <span class="text-sm font-medium text-gray-600">
                                        Маршрут: {{ getRouteName(selectedRouteForMethod[method.method]) }}
                                    </span>
                                </div>
                                <table class="w-full border-collapse">
                                    <thead>
                                        <tr class="bg-gray-100">
                                            <th class="border border-gray-300 p-2 text-left">От станции</th>
                                            <th class="border border-gray-300 p-2 text-left">До станции</th>
                                            <th class="border border-gray-300 p-2 text-center">Стоимость</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr 
                                            v-for="stationPair in getStationPairsForRoute(selectedRouteForMethod[method.method])" 
                                            :key="`${stationPair.from}-${stationPair.to}`"
                                            class="hover:bg-gray-50"
                                        >
                                            <td class="border border-gray-300 p-2">
                                                {{ stationPair.from }}
                                            </td>
                                            <td class="border border-gray-300 p-2">
                                                {{ stationPair.to }}
                                            </td>
                                            <td class="border border-gray-300 p-2 text-center">
                                                <span v-if="getMatrixPrice(method.method, selectedRouteForMethod[method.method], stationPair.from, stationPair.to)" 
                                                      class="font-bold text-green-600">
                                                    {{ formatPrice(getMatrixPrice(method.method, selectedRouteForMethod[method.method], stationPair.from, stationPair.to)) }} ₽
                                                </span>
                                                <span v-else class="text-gray-400 italic">
                                                    не задано
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Сообщение если маршрут не выбран -->
                            <div v-else class="text-center p-4 bg-gray-50 border-round">
                                <div class="text-gray-500">
                                    <i class="pi pi-info-circle mr-2"></i>
                                    Выберите маршрут для просмотра тарифной матрицы
                                </div>
                            </div>
                        </div>
                        
                        <!-- Сообщение если нет матричных тарифов -->
                        <div v-if="paymentMethods.filter(m => !m.isFixed).length === 0" class="border-1 border-gray-200 border-round p-4 text-center">
                            <div class="text-gray-500">
                                <i class="pi pi-info-circle mr-2"></i>
                                Все способы оплаты используют фиксированные тарифы
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        <i class="pi pi-info-circle mr-1"></i>
                        Настройки тарифа "{{ tariffInfo.name }}"
                    </div>
                    <div class="flex gap-2">
                        <Button 
                            label="Редактировать" 
                            icon="pi pi-pencil" 
                            @click="editSettings"
                            class="p-button-primary"
                        />
                        <Button 
                            label="Закрыть" 
                            icon="pi pi-times" 
                            @click="onClose" 
                            class="p-button-secondary"
                        />
                    </div>
                </div>
            </template>
        </Dialog>

        <!-- Редактор настроек тарифа -->
        <TariffSettingsEditor 
            v-model:visible="editorVisible"
            :tariff-id="tariffId"
            @save="onSettingsSave"
        />
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import Dialog from 'primevue/dialog'
import Button from 'primevue/button'
import Tag from 'primevue/tag'
import InputSwitch from 'primevue/inputswitch'
import TariffSettingsEditor from './TariffSettingsEditor.vue'

// Props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    tariffId: {
        type: String,
        default: null
    }
})

// Состояние редактора
const editorVisible = ref(false)

// Emits
const emit = defineEmits(['update:visible', 'edit'])

// Захардкоженные данные для прототипа
const tariffInfo = ref({
    name: 'Базовый тариф',
    status: 'ACTIVE',
    tags: 'basic,default,premium'
})

const tariffRouteConstraints = ref({
    baseRule: 'ALLOW',
    exceptions: []
})

const transportConstraints = ref({
    baseRule: 'ALLOW',
    exceptions: [
        { id: '1', name: 'Автобус №123', number: '123', action: 'DENY' },
        { id: '2', name: 'Троллейбус №45', number: '045', action: 'DENY' }
    ]
})

const paymentMethods = ref([
    { method: 'CASH', amount: 5000, isFixed: true },
    { method: 'EMV', amount: 4800, isFixed: false },
    { method: 'TROIKA_TICKET', amount: 4500, isFixed: true },
    { method: 'TROIKA_WALLET', amount: 4300, isFixed: true },
    { method: 'ABT_TICKET', amount: 4600, isFixed: true },
    { method: 'ABT_WALLET', amount: 4400, isFixed: false },
    { method: 'PROSTOR_TICKET', amount: 4700, isFixed: true },
    { method: 'QR_TICKET', amount: 4900, isFixed: true }
])

// Выбранные маршруты для каждого способа оплаты
const selectedRouteForMethod = ref({})

// Захардкоженные маршруты
const routes = ref([
    { id: 'route-1', name: 'Маршрут 1: Центр - Север', stations: ['Центральная', 'Северная', 'Парковая', 'Университетская'] },
    { id: 'route-2', name: 'Маршрут 2: Восток - Запад', stations: ['Восточная', 'Центральная', 'Западная', 'Аэропорт'] },
    { id: 'route-3', name: 'Маршрут 3: Юг - Центр', stations: ['Южная', 'Торговая', 'Центральная', 'Театральная'] }
])

// Ограничения по маршрутам для способов оплаты
const routeConstraints = ref({
    'EMV': ['route-1', 'route-2'], // EMV доступен только на маршрутах 1 и 2
    'ABT_WALLET': ['route-1', 'route-3'] // ABT_WALLET доступен только на маршрутах 1 и 3
})

// Тарифные матрицы по способам оплаты и маршрутам
const tariffMatrices = ref({
    'EMV': {
        'route-1': {
            'Центральная-Северная': 2800,
            'Центральная-Парковая': 4200,
            'Центральная-Университетская': 5600,
            'Северная-Парковая': 2300,
            'Северная-Университетская': 3700,
            'Парковая-Университетская': 1800
        },
        'route-2': {
            'Восточная-Центральная': 3200,
            'Восточная-Западная': 4800,
            'Восточная-Аэропорт': 6400,
            'Центральная-Западная': 2500,
            'Центральная-Аэропорт': 4100,
            'Западная-Аэропорт': 2000
        }
    },
    'ABT_WALLET': {
        'route-1': {
            'Центральная-Северная': 3200,
            'Центральная-Парковая': 4800,
            'Центральная-Университетская': 6400,
            'Северная-Парковая': 2700,
            'Северная-Университетская': 4300,
            'Парковая-Университетская': 2200
        },
        'route-3': {
            'Южная-Торговая': 3500,
            'Южная-Центральная': 5200,
            'Южная-Театральная': 6800,
            'Торговая-Центральная': 2800,
            'Торговая-Театральная': 4500,
            'Центральная-Театральная': 2400
        }
    }
})

// Методы
const getStatusLabel = (status) => {
    const labels = {
        'ACTIVE': 'Активен',
        'DISABLED': 'Отключен',
        'BLOCKED': 'Заблокирован',
        'IS_DELETED': 'Удален'
    }
    return labels[status] || status
}

const getStatusSeverity = (status) => {
    const severities = {
        'ACTIVE': 'success',
        'DISABLED': 'warning',
        'BLOCKED': 'danger',
        'IS_DELETED': 'secondary'
    }
    return severities[status] || 'info'
}

const getPaymentMethodLabel = (method) => {
    const labels = {
        'CASH': 'Наличные',
        'EMV': 'Банковская карта',
        'TROIKA_TICKET': 'Тройка (билет)',
        'TROIKA_WALLET': 'Тройка (кошелек)',
        'ABT_TICKET': 'Транспортная карта (билет)',
        'ABT_WALLET': 'Транспортная карта (кошелек)',
        'PROSTOR_TICKET': 'Простор (билет)',
        'QR_TICKET': 'QR (билет)',
        'QR_WALLET': 'QR (кошелек)'
    }
    return labels[method] || method
}

const formatPrice = (amount) => {
    return (amount / 100).toFixed(2)
}

const getMatrixForMethod = (method) => {
    return tariffMatrices.value[method] || []
}

const getAvailableRoutesForMethod = (method) => {
    const availableRouteIds = routeConstraints.value[method] || []
    return routes.value.filter(route => availableRouteIds.includes(route.id))
}

const getRouteName = (routeId) => {
    const route = routes.value.find(r => r.id === routeId)
    return route ? route.name : 'Неизвестный маршрут'
}

const getStationPairsForRoute = (routeId) => {
    const route = routes.value.find(r => r.id === routeId)
    if (!route) return []
    
    const pairs = []
    const stations = route.stations
    
    for (let i = 0; i < stations.length; i++) {
        for (let j = i + 1; j < stations.length; j++) {
            pairs.push({
                from: stations[i],
                to: stations[j]
            })
        }
    }
    
    return pairs
}

const getMatrixPrice = (method, routeId, stationFrom, stationTo) => {
    const methodMatrices = tariffMatrices.value[method]
    if (!methodMatrices) return null
    
    const routeMatrix = methodMatrices[routeId]
    if (!routeMatrix) return null
    
    const key = `${stationFrom}-${stationTo}`
    return routeMatrix[key] || null
}

const onRouteChange = (method) => {
    // Можно добавить дополнительную логику при смене маршрута
    console.log(`Маршрут изменен для метода ${method}:`, selectedRouteForMethod.value[method])
}

const onClose = () => {
    emit('update:visible', false)
}

const onVisibleChange = (value) => {
    emit('update:visible', value)
}

const editSettings = () => {
    editorVisible.value = true
}

const onSettingsSave = (settings) => {
    console.log('Настройки сохранены:', settings)
    // TODO: Обновить данные в родительском компоненте
    editorVisible.value = false
}
</script>

<style scoped>
.tariff-settings-card {
    /* Дополнительные стили при необходимости */
}
</style> 