<template>
    <span>{{ text }}</span>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps({
    asyncFn: {
        type: Function,
        required: true
    }
});

const text = ref('Загрузка...');

onMounted(async () => {
    try {
        text.value = await props.asyncFn();
    } catch (error) {
        console.error('Ошибка загрузки:', error);
        text.value = 'Ошибка загрузки';
    }
});
</script> 