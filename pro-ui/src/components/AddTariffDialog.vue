<template>
    <Dialog
        v-model:visible="visible"
        header="Добавить тариф к маршруту"
        modal
        :style="{ width: '40rem' }"
    >
        <div class="p-fluid">
            <div class="mb-4">
                <label class="block text-900 font-medium mb-2">Выберите тариф</label>
                <Dropdown
                    v-model="selectedTariff"
                    :options="availableTariffs"
                    option-label="name"
                    option-value="id"
                    placeholder="Выберите тариф для добавления"
                    class="w-full"
                />
            </div>

            <div v-if="selectedTariff" class="selected-tariff-info p-3 border-round surface-50">
                <h4 class="text-lg font-medium mb-2">Информация о тарифе</h4>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <label class="block text-900 font-medium mb-1">Название:</label>
                        <span class="text-color-secondary">{{ getSelectedTariffInfo.name }}</span>
                    </div>
                    <div class="col-12 md:col-6">
                        <label class="block text-900 font-medium mb-1">Статус:</label>
                        <Tag
                            :value="getStatusLabel(getSelectedTariffInfo.status)"
                            :severity="getStatusSeverity(getSelectedTariffInfo.status)"
                        />
                    </div>
                    <div class="col-12">
                        <label class="block text-900 font-medium mb-1">Тэги:</label>
                        <span class="text-color-secondary">{{ getSelectedTariffInfo.tags || 'Нет тэгов' }}</span>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <Button
                label="Отмена"
                icon="pi pi-times"
                @click="closeDialog"
                class="p-button-text"
            />
            <Button
                label="Добавить"
                icon="pi pi-check"
                @click="addTariff"
                :disabled="!selectedTariff"
                :loading="saving"
            />
        </template>
    </Dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import { TariffStorageService } from '@/service/TariffStorageService';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import Dropdown from 'primevue/dropdown';
import Tag from 'primevue/tag';

const toast = useToast();
const storageService = new TariffStorageService();

// Props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    routeId: {
        type: String,
        default: null
    }
});

// Emits
const emit = defineEmits(['update:visible', 'saved']);

// Реактивные данные
const saving = ref(false);
const selectedTariff = ref(null);
const availableTariffs = ref([]);

// Computed
const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

const getSelectedTariffInfo = computed(() => {
    if (!selectedTariff.value) return {};
    return availableTariffs.value.find(tariff => tariff.id === selectedTariff.value) || {};
});

// Методы
const loadAvailableTariffs = async () => {
    try {
        const data = await storageService.loadTariffData();
        availableTariffs.value = data.availableTariffs || [];
    } catch (error) {
        console.error('Ошибка загрузки тарифов:', error);
    }
};

const addTariff = async () => {
    if (!selectedTariff.value || !props.routeId) return;

    try {
        saving.value = true;
        await storageService.addTariffToRoute(props.routeId, selectedTariff.value);

        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Тариф добавлен к маршруту',
            life: 3000
        });

        emit('saved');
        closeDialog();
    } catch (error) {
        console.error('Ошибка добавления тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось добавить тариф',
            life: 3000
        });
    } finally {
        saving.value = false;
    }
};

const closeDialog = () => {
    visible.value = false;
    selectedTariff.value = null;
    emit('update:visible', false);
};

const getStatusLabel = (status) => {
    const labels = {
        'ACTIVE': 'Активен',
        'DISABLED': 'Отключен',
        'BLOCKED': 'Заблокирован',
        'IS_DELETED': 'Удален'
    };
    return labels[status] || status;
};

const getStatusSeverity = (status) => {
    const severities = {
        'ACTIVE': 'success',
        'DISABLED': 'warning',
        'BLOCKED': 'danger',
        'IS_DELETED': 'secondary'
    };
    return severities[status] || 'info';
};

// Watchers
watch(() => props.visible, (newValue) => {
    if (newValue) {
        loadAvailableTariffs();
        selectedTariff.value = null;
    }
});
</script>

<style scoped>
.selected-tariff-info {
    background-color: var(--surface-50);
    border: 1px solid var(--surface-200);
}
</style> 