<template>
    <div class="card">
        <Toast />
        <ConfirmDialog />
        
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-bold">Тарифы</h2>
            <Button 
                label="Добавить тариф" 
                icon="pi pi-plus" 
                @click="openCreateDialog"
                class="p-button-success"
            />
        </div>

        <DataTable 
            :value="tariffs" 
            :loading="loading"
            :paginator="true" 
            :rows="pagination.size"
            :totalRecords="pagination.totalElements"
            :lazy="true"
            @page="onPageChange"
            v-model:filters="filters"
            filterDisplay="menu"
            :globalFilterFields="['name', 'status']"
            dataKey="id"
            :resizableColumns="true"
            columnResizeMode="expand"
            showGridlines
            stripedRows
            class="p-datatable-lg"
        >
            <template #header>
                <div class="flex justify-between items-center">
                    <span class="p-input-icon-left">
                        <i class="pi pi-search" />
                        <InputText 
                            v-model="filters.global.value" 
                            placeholder="Поиск..." 
                            class="p-inputtext-sm"
                        />
                    </span>
                </div>
            </template>

            <Column field="name" header="Название" sortable>
                <template #body="{ data }">
                    <span class="font-medium">{{ data.name }}</span>
                </template>
                <template #filter="{ filterModel, filterCallback }">
                    <InputText 
                        v-model="filterModel.value" 
                        type="text" 
                        @input="filterCallback()" 
                        class="p-column-filter" 
                        placeholder="Поиск по названию"
                    />
                </template>
            </Column>

            <Column field="status" header="Статус" sortable>
                <template #body="{ data }">
                    <Tag 
                        :value="getStatusLabel(data.status)" 
                        :severity="getStatusSeverity(data.status)"
                    />
                </template>
                <template #filter="{ filterModel, filterCallback }">
                    <Dropdown 
                        v-model="filterModel.value" 
                        @change="filterCallback()" 
                        :options="statusOptions" 
                        optionLabel="label" 
                        optionValue="value" 
                        placeholder="Выберите статус" 
                        class="p-column-filter" 
                        :showClear="true"
                    />
                </template>
            </Column>

            <Column field="tags" header="Теги">
                <template #body="{ data }">
                    <div v-if="data.tags" class="flex flex-wrap gap-1">
                        <Tag 
                            v-for="tag in data.tags.split(',')" 
                            :key="tag" 
                            :value="tag.trim()" 
                            severity="info"
                            class="text-xs"
                        />
                    </div>
                    <span v-else class="text-gray-400">-</span>
                </template>
            </Column>

            <Column field="versionCreatedAt" header="Дата создания" sortable>
                <template #body="{ data }">
                    {{ formatDate(data.versionCreatedAt) }}
                </template>
            </Column>

            <Column header="Действия" :exportable="false" style="min-width:10rem">
                <template #body="{ data }">
                    <div class="flex gap-2">
                        <Button 
                            icon="pi pi-cog" 
                            class="p-button-rounded p-button-info p-button-text" 
                            @click="openSettingsDialog(data)"
                            v-tooltip.top="'Настройки'"
                        />
                        <Button 
                            icon="pi pi-pencil" 
                            class="p-button-rounded p-button-success p-button-text" 
                            @click="openEditDialog(data)"
                            v-tooltip.top="'Редактировать'"
                        />
                        <Button 
                            v-if="data.status === 'DISABLED'"
                            icon="pi pi-check" 
                            class="p-button-rounded p-button-success p-button-text" 
                            @click="activateTariff(data.id)"
                            v-tooltip.top="'Активировать'"
                        />
                        <Button 
                            v-if="data.status === 'ACTIVE'"
                            icon="pi pi-times" 
                            class="p-button-rounded p-button-warning p-button-text" 
                            @click="deactivateTariff(data.id)"
                            v-tooltip.top="'Деактивировать'"
                        />
                        <Button 
                            v-if="data.status !== 'IS_DELETED'"
                            icon="pi pi-trash" 
                            class="p-button-rounded p-button-danger p-button-text" 
                            @click="confirmDelete(data)"
                            v-tooltip.top="'Удалить'"
                        />
                    </div>
                </template>
            </Column>
        </DataTable>

        <!-- Диалог создания/редактирования тарифа -->
        <Dialog 
            v-model:visible="dialogVisible" 
            :header="isEditMode ? 'Редактировать тариф' : 'Создать тариф'"
            modal 
            class="p-fluid"
            :style="{ width: '50rem' }"
        >
            <div class="grid grid-cols-2 gap-4">
                <div class="field">
                    <label for="name">Название *</label>
                    <InputText 
                        id="name" 
                        v-model="tariffForm.name" 
                        required 
                        autofocus 
                        :class="{ 'p-invalid': submitted && !tariffForm.name }"
                    />
                    <small class="p-error" v-if="submitted && !tariffForm.name">
                        Название обязательно для заполнения.
                    </small>
                </div>

                <div class="field">
                    <label for="status">Статус</label>
                    <Dropdown 
                        id="status" 
                        v-model="tariffForm.status" 
                        :options="statusOptions" 
                        optionLabel="label" 
                        optionValue="value" 
                        placeholder="Выберите статус"
                    />
                </div>

                <div class="field col-span-2">
                    <label for="tags">Теги</label>
                    <InputText 
                        id="tags" 
                        v-model="tariffForm.tags" 
                        placeholder="Введите теги через запятую"
                    />
                    <small class="text-gray-500">Например: basic, default, premium</small>
                </div>
            </div>

            <template #footer>
                <Button 
                    label="Отмена" 
                    icon="pi pi-times" 
                    @click="closeDialog" 
                    class="p-button-text"
                />
                <Button 
                    :label="isEditMode ? 'Обновить' : 'Создать'" 
                    icon="pi pi-check" 
                    @click="saveTariff" 
                    :loading="saving"
                />
            </template>
        </Dialog>

        <!-- Диалог настроек тарифа -->
        <TariffSettingsCard 
            v-model:visible="settingsDialogVisible"
            :tariff-id="selectedTariffId"
            @edit="editSettings"
        />
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { TariffService } from '@/service/TariffService';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { FilterMatchMode } from '@primevue/core/api';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';
import Tag from 'primevue/tag';
import Dialog from 'primevue/dialog';
import ConfirmDialog from 'primevue/confirmdialog';
import Toast from 'primevue/toast';
import TariffSettingsCard from './TariffSettingsCard.vue';

const toast = useToast();
const confirm = useConfirm();

// Реактивные данные
const tariffs = ref([]);
const loading = ref(false);
const saving = ref(false);
const dialogVisible = ref(false);
const isEditMode = ref(false);
const submitted = ref(false);
const currentPage = ref(0);
const pageSize = ref(20);
const settingsDialogVisible = ref(false);
const selectedTariffId = ref(null);

// Форма тарифа
const tariffForm = reactive({
    name: '',
    status: 'ACTIVE',
    tags: ''
});

// Фильтры
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
});

// Пагинация
const pagination = ref({
    page: 0,
    size: 20,
    totalElements: 0,
    totalPages: 0,
    hasNext: false,
    hasPrevious: false
});

// Опции статусов
const statusOptions = [
    { label: 'Активный', value: 'ACTIVE' },
    { label: 'Отключен', value: 'DISABLED' },
    { label: 'Заблокирован', value: 'BLOCKED' },
    { label: 'Удален', value: 'IS_DELETED' }
];

// Props
const props = defineProps({
    projectId: {
        type: String,
        default: null
    }
});

// Методы
const loadTariffs = async () => {
    try {
        loading.value = true;
        const response = await TariffService.getTariffs(
            currentPage.value, 
            pageSize.value, 
            props.projectId
        );
        
        tariffs.value = response.content || [];
        pagination.value = response.pagination || {
            page: currentPage.value,
            size: pageSize.value,
            totalElements: 0,
            totalPages: 0,
            hasNext: false,
            hasPrevious: false
        };
    } catch (error) {
        console.error('Ошибка загрузки тарифов:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить тарифы',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

const onPageChange = (event) => {
    currentPage.value = event.page;
    loadTariffs();
};

const openCreateDialog = () => {
    isEditMode.value = false;
    resetForm();
    dialogVisible.value = true;
};

const openEditDialog = (tariff) => {
    isEditMode.value = true;
    tariffForm.name = tariff.name || '';
    tariffForm.status = tariff.status || 'ACTIVE';
    tariffForm.tags = tariff.tags || '';
    dialogVisible.value = true;
};

const openSettingsDialog = (tariff) => {
    selectedTariffId.value = tariff.id;
    settingsDialogVisible.value = true;
};

const editSettings = (tariffId) => {
    // TODO: Реализовать редактирование настроек
    console.log('Редактирование настроек для тарифа:', tariffId);
    toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Функция редактирования настроек будет реализована позже',
        life: 3000
    });
};

const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

const resetForm = () => {
    tariffForm.name = '';
    tariffForm.status = 'ACTIVE';
    tariffForm.tags = '';
    submitted.value = false;
};

const saveTariff = async () => {
    submitted.value = true;
    
    if (!tariffForm.name) {
        return;
    }

    try {
        saving.value = true;
        
        if (isEditMode.value) {
            // Обновление тарифа
            await TariffService.updateTariff(editingTariffId.value, {
                name: tariffForm.name,
                status: tariffForm.status,
                tags: tariffForm.tags
            });
            toast.add({
                severity: 'success',
                summary: 'Успешно',
                detail: 'Тариф обновлен',
                life: 3000
            });
        } else {
            // Создание тарифа
            await TariffService.createTariff({
                projectId: props.projectId,
                name: tariffForm.name,
                status: tariffForm.status,
                tags: tariffForm.tags
            });
            toast.add({
                severity: 'success',
                summary: 'Успешно',
                detail: 'Тариф создан',
                life: 3000
            });
        }
        
        closeDialog();
        loadTariffs();
    } catch (error) {
        console.error('Ошибка сохранения тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось сохранить тариф',
            life: 3000
        });
    } finally {
        saving.value = false;
    }
};

const confirmDelete = (tariff) => {
    confirm.require({
        message: `Вы уверены, что хотите удалить тариф "${tariff.name}"?`,
        header: 'Подтверждение удаления',
        icon: 'pi pi-exclamation-triangle',
        accept: () => deleteTariff(tariff.id)
    });
};

const deleteTariff = async (id) => {
    try {
        await TariffService.deleteTariff(id);
        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Тариф удален',
            life: 3000
        });
        loadTariffs();
    } catch (error) {
        console.error('Ошибка удаления тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось удалить тариф',
            life: 3000
        });
    }
};

const activateTariff = async (id) => {
    try {
        await TariffService.activateTariff(id);
        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Тариф активирован',
            life: 3000
        });
        loadTariffs();
    } catch (error) {
        console.error('Ошибка активации тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось активировать тариф',
            life: 3000
        });
    }
};

const deactivateTariff = async (id) => {
    try {
        await TariffService.deactivateTariff(id);
        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Тариф деактивирован',
            life: 3000
        });
        loadTariffs();
    } catch (error) {
        console.error('Ошибка деактивации тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось деактивировать тариф',
            life: 3000
        });
    }
};

// Вспомогательные методы
const getStatusLabel = (status) => {
    const option = statusOptions.find(opt => opt.value === status);
    return option ? option.label : status;
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'ACTIVE': return 'success';
        case 'DISABLED': return 'warning';
        case 'BLOCKED': return 'danger';
        case 'IS_DELETED': return 'secondary';
        default: return 'info';
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('ru-RU');
};

// ID редактируемого тарифа
const editingTariffId = ref(null);

// Загрузка данных при монтировании
onMounted(() => {
    loadTariffs();
});
</script>

<style scoped>
.p-datatable .p-datatable-thead > tr > th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.p-datatable .p-datatable-tbody > tr:hover {
    background-color: #f8f9fa;
}

.p-button-text {
    min-width: 2rem;
    height: 2rem;
}

.field label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}
</style> 