import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import { useAuthStore } from './stores/auth';
import { initAuthStore } from './utils/auth-token';

import Material from '@primeuix/themes/material';
import PrimeVue from 'primevue/config';
import ConfirmationService from 'primevue/confirmationservice';
import ToastService from 'primevue/toastservice';

import '@/assets/styles.scss';

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(router);
app.use(PrimeVue, {
    theme: {
        preset: Material,
        options: {
            darkModeSelector: '.app-dark'
        }
    }
});
app.use(ToastService);
app.use(ConfirmationService);

// Инициализация Keycloak перед монтированием приложения
const authStore = useAuthStore();
authStore.initKeycloak().then(() => {
    // Инициализируем auth store для API клиентов
    initAuthStore();
    app.mount('#app');
}).catch((error) => {
    console.error('Failed to initialize Keycloak:', error);
    // Инициализируем auth store для API клиентов даже при ошибке
    initAuthStore();
    app.mount('#app');
});
