# Build stage
FROM node:18-alpine as builder
# Build the application with environment variables
ARG VITE_KEYCLOAK_URL
ARG VITE_KEYCLOAK_REALM
ARG VITE_KEYCLOAK_CLIENT_ID
ARG VITE_API_SBOL_URL=''
ARG VITE_API_ABT_URL=''
ARG VITE_API_PRO_URL=''
ARG VITE_API_TMS_URL=''
ARG VITE_API_PRO_GATE_PRIVATE_URL=''

ENV VITE_KEYCLOAK_URL=$VITE_KEYCLOAK_URL
ENV VITE_KEYCLOAK_REALM=$VITE_KEYCLOAK_REALM
ENV VITE_KEYCLOAK_CLIENT_ID=$VITE_KEYCLOAK_CLIENT_ID
ENV VITE_API_SBOL_URL=$VITE_API_SBOL_URL
ENV VITE_API_ABT_URL=$VITE_API_ABT_URL
ENV VITE_API_PRO_URL=$VITE_API_PRO_URL
ENV VITE_API_TMS_URL=$VITE_API_TMS_URL
ENV VITE_API_PRO_GATE_PRIVATE_URL=$VITE_API_PRO_GATE_PRIVATE_URL
RUN env

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (including devDependencies for build)
RUN npm install --include=dev

# Copy source code
COPY . .
RUN rm -rf /app/dist

# Verify Vite installation and build with retry logic
RUN ls -la node_modules/.bin/vite || echo "Vite not found in node_modules/.bin"
RUN NODE_OPTIONS="--max-old-space-size=4096" npm run build || \
    (echo "Build failed, retrying..." && sleep 5 && NODE_OPTIONS="--max-old-space-size=4096" npm run build)

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html
# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create script to replace environment variables at runtime
RUN echo '#!/bin/sh' > /docker-entrypoint.sh && \
    echo 'set -e' >> /docker-entrypoint.sh && \
    echo '' >> /docker-entrypoint.sh && \
    echo '# Replace environment variables in built files' >> /docker-entrypoint.sh && \
    echo 'if [ -n "$KEYCLOAK_URL" ]; then' >> /docker-entrypoint.sh && \
    echo '  find /usr/share/nginx/html -name "*.js" -exec sed -i "s|https://dev-auth.sbertroika.tech/|$KEYCLOAK_URL|g" {} \;' >> /docker-entrypoint.sh && \
    echo 'fi' >> /docker-entrypoint.sh && \
    echo 'if [ -n "$KEYCLOAK_REALM" ]; then' >> /docker-entrypoint.sh && \
    echo '  find /usr/share/nginx/html -name "*.js" -exec sed -i "s|test-asop|$KEYCLOAK_REALM|g" {} \;' >> /docker-entrypoint.sh && \
    echo 'fi' >> /docker-entrypoint.sh && \
    echo 'if [ -n "$KEYCLOAK_CLIENT_ID" ]; then' >> /docker-entrypoint.sh && \
    echo '  find /usr/share/nginx/html -name "*.js" -exec sed -i "s|crm-ui-local|$KEYCLOAK_CLIENT_ID|g" {} \;' >> /docker-entrypoint.sh && \
    echo 'fi' >> /docker-entrypoint.sh && \
    echo '' >> /docker-entrypoint.sh && \
    echo '# Start nginx' >> /docker-entrypoint.sh && \
    echo 'exec nginx -g "daemon off;"' >> /docker-entrypoint.sh && \
    chmod +x /docker-entrypoint.sh

EXPOSE 80


CMD ["/docker-entrypoint.sh"]
