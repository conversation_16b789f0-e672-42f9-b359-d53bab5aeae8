version: '3.8'

services:
  pro-ui:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VITE_KEYCLOAK_URL: ${KEYCLOAK_URL:-https://dev-auth.sbertroika.tech/}
        VITE_KEYCLOAK_REALM: ${KEYCLOAK_REALM:-test-asop}
        VITE_KEYCLOAK_CLIENT_ID: ${KEYCLOAK_CLIENT_ID:-crm-ui-local}
        VITE_API_SBOL_URL: ${API_SBOL_URL}
        VITE_API_ABT_URL: ${API_ABT_URL}
        VITE_API_PRO_URL: ${API_PRO_URL}
        VITE_API_TMS_URL: ${API_TMS_URL}
        VITE_API_PRO_GATE_PRIVATE_URL: ${API_PRO_GATE_PRIVATE_URL}
    ports:
      - "80:80"
    environment:
      # Переменные для переопределения конфигураци<PERSON> в runtime
      KEYCLOAK_URL: ${KEYCLOAK_URL:-https://dev-auth.sbertroika.tech/}
      KEYCLOAK_REALM: ${KEYCLOAK_REALM:-test-asop}
      KEYCLOAK_CLIENT_ID: ${KEYCLOAK_CLIENT_ID:-crm-ui-local}
      # Переменные для переопределения API URL в runtime
      API_SBOL_URL: ${API_SBOL_URL}
      API_ABT_URL: ${API_ABT_URL}
      API_PRO_URL: ${API_PRO_URL}
      API_TMS_URL: ${API_TMS_URL}
      API_PRO_GATE_PRIVATE_URL: ${API_PRO_GATE_PRIVATE_URL}
    restart: unless-stopped 