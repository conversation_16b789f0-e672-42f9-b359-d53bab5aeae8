include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# PRO-UI: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
pro_ui_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "pro-ui"
    TAG: "$CI_COMMIT_SHORT_SHA"
    BUILD_ARG_VITE_KEYCLOAK_URL: https://dev-auth.sbertroika.tech/
    BUILD_ARG_VITE_KEYCLOAK_REALM: test-asop
    BUILD_ARG_VITE_KEYCLOAK_CLIENT_ID: crm-ui
  extends:
    - .docker_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pro-ui/**


pro_ui_helm_kubeval_testing_develop:
  stage: test
  needs:
    - job: pro_ui_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    SERVICE_NAME: "pro-ui"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pro-ui/**
        - charts/pro-ui/**


pro_ui_deploy_chart_develop:
  stage: deploy
  needs:
    - pro_ui_helm_kubeval_testing_develop
    - job: pro_ui_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "pro-ui"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pro-ui/**
        - charts/pro-ui/**

# --- TAG & release---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - pro-ui/**

pro_ui_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pro-ui"
    BUILD_ARG_VITE_KEYCLOAK_URL: https://dev-auth.sbertroika.tech/
    BUILD_ARG_VITE_KEYCLOAK_REALM: test-asop
    BUILD_ARG_VITE_KEYCLOAK_CLIENT_ID: crm-ui
  extends:
    - .docker_build_and_push
  <<: *tag_rules

pro_ui_helm_kubeval_testing_tag:
  stage: test
  needs:
    - pro_ui_build_tag
  variables:
    SERVICE_NAME: "pro-ui"
  extends:
    - .validate_helm_template
  <<: *tag_rules

pro_ui_deploy_chart_tag:
  stage: deploy
  needs:
    - pro_ui_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pro-ui"
  extends:
    - .deploy_helm_template
  <<: *tag_rules

pro_ui_build_release:
    stage: build
    variables:
        TAG: "$CI_COMMIT_TAG-prod"
        SERVICE_NAME: "pro-ui"
        BUILD_ARG_VITE_KEYCLOAK_URL: https://auth.sbertroika.ru/
        BUILD_ARG_VITE_KEYCLOAK_REALM: release_asop
        BUILD_ARG_VITE_KEYCLOAK_CLIENT_ID: crm-ui
    extends:
        - .docker_build_and_push
    <<: *tag_rules

pro_ui_deploy_prod:
  stage: deploy
  needs:
    - pro_ui_build_release
  variables:
    STAGE: "prod"
    TAG:  "$CI_COMMIT_TAG-prod"
    SERVICE_NAME: "pro-ui"
    NAMESPACE: "$KUBE_NAMESPACE-prod"
    KUBECONFIG_FILE: $KUBECONFIG_CCE_PROD
  extends:
    - .deploy_helm_template
  when: manual
  <<: *tag_rules

