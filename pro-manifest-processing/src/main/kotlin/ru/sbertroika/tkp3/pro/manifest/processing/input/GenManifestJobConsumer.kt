package ru.sbertroika.tkp3.pro.manifest.processing.input

import com.fasterxml.jackson.module.kotlin.readValue
import com.rabbitmq.client.Channel
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.amqp.support.AmqpHeaders
import org.springframework.messaging.handler.annotation.Header
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.manifest.model.pro.JournalOperationType
import ru.sbertroika.tkp3.manifest.model.pro.ManifestJournal
import ru.sbertroika.tkp3.manifest.model.pro.OperationStatus
import ru.sbertroika.tkp3.manifest.starter.ManifestService
import ru.sbertroika.tkp3.manifest.starter.mapper
import ru.sbertroika.tkp3.pro.manifest.processing.model.GenManifestJob
import ru.sbertroika.tkp3.pro.manifest.processing.output.repository.ProjectRepository
import ru.sbertroika.tkp3.pro.manifest.processing.output.service.JournalService
import ru.sbertroika.tkp3.pro.manifest.processing.output.service.ManifestGeneratorService
import ru.sbertroika.tkp3.pro.manifest.processing.output.service.MqService

@Component
class GenManifestJobConsumer(
    private val mqService: MqService,
    private val manifestGeneratorService: ManifestGeneratorService,
    private val manifestService: ManifestService,
    private val journalService: JournalService,
    private val projectRepository: ProjectRepository
) {
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val mapper = mapper()

    @RabbitListener(queues = ["\${spring.rabbitmq.processing_queue_name}"])
    fun process(message: Message, channel: Channel, @Header(AmqpHeaders.DELIVERY_TAG) tag: Long) = runBlocking {
        try {
            val job = mapper.readValue<GenManifestJob>(message.body)
            logger.info("Start job $job")
            journalService.logOperation(
                ManifestJournal(
                    jobId = job.jobId,
                    type = JournalOperationType.GEN_DAY_MANIFEST_JOB,
                    projectId = job.projectId,
                    projectVersion = job.projectVersion,
                    status = OperationStatus.RETRY_JOB,
                    manifestDate = job.date,
                    manifestVersion = job.version
                )
            )

            val project = if (job.projectVersion != null) {
                projectRepository.findAllByIdAndVersion(job.projectId, job.projectVersion)
            } else {
                projectRepository.findAllByIdAndEndDateIsNull(job.projectId)
            }
            if (project == null) {
                journalService.logOperation(
                    ManifestJournal(
                        jobId = job.jobId,
                        type = JournalOperationType.GEN_DAY_MANIFEST_JOB,
                        projectId = job.projectId,
                        projectVersion = job.projectVersion,
                        status = OperationStatus.FAIL,
                        errors = listOf("Project ${job.projectId} not found"),
                        manifestDate = job.date
                    )
                )
                channel.basicAck(tag, false)
                return@runBlocking
            }

            val version = if (job.isUpdateVersion) {
                manifestService.getManifestByProject(project.id!!.toString()).fold(
                    { 1 },
                    { it?.version?.plus(1) ?: 1 }
                )
            } else 1

            manifestGeneratorService.generateManifest(project, job.date, version, job.jobId).fold(
                {
                    logger.error("Error generateManifest", it)
                    journalService.logOperation(
                        ManifestJournal(
                            jobId = job.jobId,
                            type = JournalOperationType.GEN_DAY_MANIFEST_JOB,
                            projectId = job.projectId,
                            projectVersion = job.projectVersion,
                            status = OperationStatus.FAIL,
                            errors = listOf(it.message)
                        )
                    )
                    if (job.attemptNumber < 10) {
                        mqService.extracted(job)
                    }
                },
                {}
            )
            channel.basicAck(tag, false)
        } catch (e: Exception) {
            logger.error("Process job error: $message", e)
            channel.basicAck(tag, false)
        }
    }
}