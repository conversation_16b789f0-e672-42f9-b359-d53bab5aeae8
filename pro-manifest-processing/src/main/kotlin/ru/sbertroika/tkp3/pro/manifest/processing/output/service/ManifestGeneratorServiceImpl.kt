package ru.sbertroika.tkp3.pro.manifest.processing.output.service

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.toList
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.manifest.model.Manifest
import ru.sbertroika.tkp3.manifest.model.ManifestPasiv
import ru.sbertroika.tkp3.manifest.model.ManifestPasivDict
import ru.sbertroika.tkp3.manifest.model.ManifestProComposite
import ru.sbertroika.tkp3.manifest.model.pasiv.Organization
import ru.sbertroika.tkp3.manifest.model.pro.JournalOperationType
import ru.sbertroika.tkp3.manifest.model.pro.ManifestJournal
import ru.sbertroika.tkp3.manifest.model.pro.OperationStatus
import ru.sbertroika.tkp3.manifest.starter.ManifestService
import ru.sbertroika.tkp3.pro.manifest.processing.output.repository.ManifestRepository
import ru.sbertroika.tkp3.pro.manifest.processing.output.repository.ProjectFunctionRepository
import ru.sbertroika.tkp3.pro.model.Project
import ru.sbertroika.tkp3.pro.model.ProjectFunctionType
import java.sql.Timestamp
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@Service
class ManifestGeneratorServiceImpl(
    private val manifestService: ManifestService,
    private val journalService: JournalService,
    private val projectFunctionRepository: ProjectFunctionRepository,
    private val manifestRepository: ManifestRepository,
    private val proService: ProService,
    private val pasivService: PasivService,

    @Value("\${system_user_id}")
    private val systemUserId: String
) : ManifestGeneratorService {
    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    override suspend fun generateManifest(project: Project, date: LocalDate, version: Int, jobId: UUID): Either<Throwable, Unit> = coroutineScope {
        val res = mutableMapOf<ProjectFunctionType, Any>()

        // Не забываем что сборка манифеста может идти асинхронно так как опрашивается много подсистем
        projectFunctionRepository.findAllByProjectIdAndVersion(project.id!!, project.version!!).toList().map { function ->
            when (ProjectFunctionType.valueOf(function.type!!)) {
                ProjectFunctionType.PRO -> async(Dispatchers.IO) {
                    withTimeoutOrNull(60000) {
                        kotlin.runCatching {
                            proService.getManifest(project.id.toString(), jobId=jobId).fold(
                                {
                                    logger.error("Error load pro manifest, jobId=$jobId", it)
                                    res[ProjectFunctionType.PRO] = Error(it)
                                },
                                { manifest ->
                                    res[ProjectFunctionType.PRO] = manifest
                                }
                            )
                        }
                    }
                }

                //TODO:Klabukov Сюдв нужно добавить запрос организаций по типу перевозчик и проекту
//                ProjectFunctionType.PASIV -> async(Dispatchers.IO) {
//                    withTimeoutOrNull(60000) {
//                        kotlin.runCatching {
//                            pasivService.getManifest(project.id.toString()).fold(
//                                {
//                                    res[ProjectFunctionType.PASIV] = Error(it)
//                                },
//                                { manifest ->
//                                    res[ProjectFunctionType.PASIV] = manifest
//                                }
//                            )
//                        }
//                    }
//                }
                //TODO Убрать заглушку когда будет подключен ПАСиВ
                ProjectFunctionType.PASIV -> async(Dispatchers.IO) {
                    withTimeoutOrNull(60000) {
                        kotlin.runCatching {
                            res[ProjectFunctionType.PASIV] = ManifestPasiv(
                                features = emptyList(),
                                dict = ManifestPasivDict(
                                    organization = listOf(
                                        Organization(
                                            id = UUID.fromString("65bab8b6-5728-4823-b5b6-b5e1efaf1fbf"),
                                            name = "ООО \"СберТройка\"",
                                            shortName = "SberTroika",
                                            inn = "9702027017",
                                            kpp = "770201001",
                                            address = "129110, г Москва, ул Щепкина, 51/4 / строение 2, этаж 5",
                                            paymentPlace = "Разъездная торговля"
                                        )
                                    )
                                )
                            )
                        }
                    }
                }
            }
        }.awaitAll()

        val findErrors = res.filter { it.value is Error }
        if (findErrors.isNotEmpty()) {
            journalService.logOperation(
                ManifestJournal(
                    jobId = jobId,
                    type = JournalOperationType.GEN_DAY_MANIFEST_RESULT,
                    status = OperationStatus.FAIL,
                    projectId = project.id,
                    projectVersion = project.version,
                    errors = findErrors.mapNotNull { (it.value as Error).message }.toList(),
                    manifestDate = date,
                    manifestVersion = version
                )
            )
            return@coroutineScope Error("Error generate manifest").left()
        }

        val validFrom = ZonedDateTime.now()
        val validTill = ZonedDateTime.now().plusDays(3)
        val manifest = Manifest(
            id = "${project.id}-${date.format(fmt)}",
            version = version,
            projectId = project.id!!,
            createdAt = validFrom,
            projectIndex = project.index!!,
            validFrom = validFrom,
            validTill = validTill,
            service = ru.sbertroika.tkp3.manifest.model.Service(
                pro = if (res.containsKey(ProjectFunctionType.PRO)) (res[ProjectFunctionType.PRO] as ManifestProComposite).pro else null,
                proAbt = if (res.containsKey(ProjectFunctionType.PRO)) (res[ProjectFunctionType.PRO] as ManifestProComposite).abt else null,
                proTroika = if (res.containsKey(ProjectFunctionType.PRO)) (res[ProjectFunctionType.PRO] as ManifestProComposite).troika else null,
                pasiv = if (res.containsKey(ProjectFunctionType.PASIV)) res[ProjectFunctionType.PASIV] as ManifestPasiv else null
            )
        )

        manifestService.saveManifest(manifest).fold(
            {
                logger.error("Error save manifest, jobId=$jobId", it)
                journalService.logOperation(
                    ManifestJournal(
                        jobId = jobId,
                        type = JournalOperationType.GEN_DAY_MANIFEST_RESULT,
                        status = OperationStatus.FAIL,
                        projectId = project.id,
                        projectVersion = project.version,
                        errors = listOf("Error save manifest to storage: ${it.message}"),
                        manifestDate = date,
                        manifestVersion = version
                    )
                )
                return@coroutineScope it.left()
            },
            { fileName ->
                manifestRepository.save(
                    ru.sbertroika.tkp3.pro.model.Manifest(
                        version = 1,
                        projectId = project.id,
                        projectVersion = project.version,
                        createdBy = UUID.fromString(systemUserId),
                        activeFrom = Timestamp.from(validFrom.toInstant()),
                        activeTill = Timestamp.from(validTill.toInstant()),
                        path = fileName
                    )
                )

                manifestService.setManifestPath(fileName, project.id.toString())
                journalService.logOperation(
                    ManifestJournal(
                        jobId = jobId,
                        type = JournalOperationType.GEN_DAY_MANIFEST_RESULT,
                        status = OperationStatus.SUCCESS,
                        projectId = project.id,
                        projectVersion = project.version,
                        manifestDate = date,
                        manifestVersion = version
                    )
                )
            }
        )

        Unit.right()
    }

    companion object {
        private val fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    }
}