package ru.sbertroika.tkp3.pro.manifest.processing

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.context.annotation.ComponentScan
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication
@EnableR2dbcRepositories
@EnableScheduling
@ComponentScan("ru.sbertroika.tkp3")
open class ManifestProcessingApplication

fun main(args: Array<String>) {
    runApplication<ManifestProcessingApplication>(*args)
}
