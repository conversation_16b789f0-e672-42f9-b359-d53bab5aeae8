package ru.sbertroika.tkp3.pro.manifest.processing.output.service

import arrow.core.Either
import arrow.core.getOrElse
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.manifest.model.*
import ru.sbertroika.tkp3.manifest.model.pro.*
import ru.sbertroika.tkp3.pro.manifest.processing.output.repository.*
import java.util.*

@Service
class ProServiceImpl(
    private val routeCrudRepository: RouteCrudRepository,
    private val routeStationRepository: RouteStationRepository,
    private val routeConstraintRepository: RouteConstraintRepository,
    private val routeConstraintExceptionRepository: RouteConstraintExceptionRepository,

    private val stationRepository: StationCrudRepository,

    private val productRepository: ProductCrudRepository,
    private val productDictRowRepository: ProductDictRowRepository,
    private val productDictRowTariffMatrixPriceRepository: ProductDictRowTariffMatrixPriceRepository,

    private val tariffRepository: TariffCrudRepository,
    private val tariffConstraintRepository: TariffConstraintRepository,
    private val tariffConstraintExceptionRepository: TariffConstraintExceptionRepository,

    private val vehicleRepository: VehicleCrudRepository,
    private val vehicleConstraintRepository: VehicleConstraintRepository,
    private val vehicleConstraintExceptionRepository: VehicleConstraintExceptionRepository,

    private val abtService: AbtService
) : ProService {
    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    override suspend fun getManifest(projectId: String, jobId: UUID): Either<Throwable, ManifestProComposite> = Either.catch {
        val projectUuid = UUID.fromString(projectId)

        try {
            ManifestProComposite(
                pro = ManifestPro(
                    features = emptyList(), // TODO: Добавить получение features из базы
                    dict = ManifestProDict(
                        route = routeCrudRepository.findAllByProjectId(projectUuid)
                            .map { rItem ->
                                Route(
                                    id = rItem.id!!,
                                    version = rItem.version!!,
                                    name = rItem.name!!,
                                    scheme = RouteScheme.valueOf(rItem.scheme!!.name),
                                    routeIndex = rItem.index!!,
                                    number = rItem.number ?: "",
                                    station = routeStationRepository.findAllByRouteId(rItem.id!!)
                                        .map { rsItem ->
                                            RouteStation(rsItem.stationId!!, rsItem.position!!, rsItem.version!!)
                                        }.toList(),
                                    constraint = routeConstraintRepository.findAllByRouteId(rItem.id!!)
                                        .map { rcItem ->
                                            Constraint(
                                                type = ConstraintType.valueOf(rcItem.type!!.name),
                                                baseRule = ConstraintBaseRule.valueOf(rcItem.baseRule!!.name),
                                                exception = routeConstraintExceptionRepository.findAllByRouteConstraintId(rcItem.id!!)
                                                    .map { rceItem ->
                                                        ConstraintException(rceItem.exceptionId!!)
                                                    }.toList()
                                            )
                                        }.toList()
                                )
                            }.toList(),

                        station = stationRepository.findAllByProjectId(projectUuid)
                            .map { item ->
                                Station(
                                    id = item.id!!,
                                    version = item.version!!,
                                    name = item.name!!,
                                    lat = item.latitude ?: 0.0,
                                    lon = item.longitude ?: 0.0
                                )
                            }.toList(),

                        tariff = tariffRepository.findAllByProjectId(projectUuid)
                            .map { tItem ->
                                Tariff(
                                    id = tItem.id!!,
                                    version = tItem.version!!,
                                    name = tItem.name!!,
                                    constraint = tariffConstraintRepository.findAllByTariffId(tItem.id!!)
                                        .map { tcItem ->
                                            Constraint(
                                                type = ConstraintType.valueOf(tcItem.type!!.name),
                                                baseRule = ConstraintBaseRule.valueOf(tcItem.baseRule!!.name),
                                                exception = tariffConstraintExceptionRepository.findAllByTariffConstraintId(tcItem.id!!)
                                                    .map { tceItem ->
                                                        ConstraintException(tceItem.exceptionId!!)
                                                    }.toList()
                                            )
                                        }.toList()
                                )
                            }.toList(),

                        product = productRepository.findAll()
                            .map { pItem ->
                                Product(pItem.id!!, pItem.name!!, pItem.version!!)
                            }.toList(),

                        transport = vehicleRepository.findAllByProjectId(projectUuid)
                            .map { vItem ->
                                Transport(
                                    id = vItem.id!!,
                                    version = vItem.version!!,
                                    number = vItem.number ?: "",
                                    type = TransportType.valueOf(vItem.type!!.name),
                                    constraint = vehicleConstraintRepository.findAllByVehicleId(vItem.id!!)
                                        .map { vcItem ->
                                            Constraint(
                                                type = ConstraintType.valueOf(vcItem.type!!.name),
                                                baseRule = ConstraintBaseRule.valueOf(vcItem.baseRule!!.name),
                                                exception = vehicleConstraintExceptionRepository.findAllByVehicleConstraintId(vcItem.id!!)
                                                    .map { vceItem ->
                                                        ConstraintException(vceItem.exceptionId!!)
                                                    }.toList()
                                            )
                                        }.toList()
                                )
                            }.toList(),

                        menu = productDictRowRepository.findAllByProjectId(projectUuid).toList()
                            .groupByTo(mutableMapOf()) { "${it.productId}-${it.tariffId}" }
                            .map { group ->
                                val first = group.value.first()
                                ProductMenu(
                                    productId = first.productId!!,
                                    tariffId = first.tariffId!!,
                                    version = first.version!!,
                                    priceRules = group.value.map { row ->
                                        PriceRule(
                                            paymentType = TPaymentType.valueOf(row.paymentMethodType!!.name),
                                            price = if (row.isFixPrice == true) row.price!!.toInt() else 0,
                                            version = row.version!!,
                                            matrix = if (row.isFixPrice != true) {
                                                productDictRowTariffMatrixPriceRepository.findAllByProductDicRowId(row.id!!)
                                                    .map { m ->
                                                        PriceRuleMatrixItem(
                                                            stationFrom = m.stationFromId!!,
                                                            stationTo = m.stationToId!!,
                                                            price = m.amount!!.toInt(),
                                                            version = m.version!!
                                                        )
                                                    }.toList()
                                            } else emptyList()
                                        )
                                    }.toList()
                                )
                            }.toList(),

                        employee = emptyList() // TODO: Добавить получение employee из базы
                    )
                ),

                abt = abtService.getManifest(projectId, jobId=jobId).getOrElse {
                    throw RuntimeException("Error request ABT Manifest")
                },

                troika = ManifestProTroika(
                    dict = ManifestProTroikaDict(
                        templates = emptyList() // TODO: Добавить получение troika templates из базы
                    )
                )
            )
        } catch (ex: Exception) {
            logger.error("Error make pro Manifest, jobId=$jobId", ex)
            throw RuntimeException(ex)
        }
    }
}