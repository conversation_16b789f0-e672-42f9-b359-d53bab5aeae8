package ru.sbertroika.tkp3.pro.manifest.processing.output.service

import arrow.core.Either
import org.springframework.amqp.core.AmqpTemplate
import org.springframework.amqp.core.Message
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.pro.manifest.processing.model.GenManifestJob
import ru.sbertroika.tkp3.pro.manifest.processing.util.mapper

@Service
class MqServiceImpl(
    private val template: AmqpTemplate,

    @Value("\${spring.rabbitmq.processing_queue_name}")
    private val processingQueueName: String,

    @Value("\${spring.rabbitmq.processing_delayed_execution_queue_name}")
    private val processingDelayedExecutionQueueName: String

) : MqService {
    private val mapper = mapper()

    override suspend fun addJob(value: GenManifestJob): Either<Throwable, Unit> = Either.catch {
        val message = Message(mapper.writeValueAsBytes(value))
        template.send(processingQueueName, message)
    }

    override suspend fun extracted(value: GenManifestJob): Either<Throwable, Unit> = Either.catch {
        val message = Message(mapper.writeValueAsBytes(value.copy(attemptNumber = value.attemptNumber + 1)))
        message.messageProperties.expiration = "60000"
        template.send(processingDelayedExecutionQueueName, message)
    }
}