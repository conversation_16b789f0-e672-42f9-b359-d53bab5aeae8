package ru.sbertroika.tkp3.pro.manifest.processing.output.service

import arrow.core.Either
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.manifest.model.ManifestPasiv
import ru.sbertroika.tkp3.manifest.model.ManifestPasivDict

@Service
class PasivServiceImpl : PasivService {

    override suspend fun getManifest(projectId: String): Either<Throwable, ManifestPasiv> = Either.catch {
        // TODO: Реализовать получение PASIV данных
        // Пока возвращаем пустой результат
        ManifestPasiv(
            features = emptyList(),
            dict = ManifestPasivDict(organization = emptyList())
        )
    }
}