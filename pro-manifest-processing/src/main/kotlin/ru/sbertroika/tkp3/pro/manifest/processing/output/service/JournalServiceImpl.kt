package ru.sbertroika.tkp3.pro.manifest.processing.output.service

import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.manifest.model.pro.ManifestJournal
import ru.sbertroika.tkp3.manifest.starter.mapper
import java.util.*

@Service
class JournalServiceImpl(
    kafkaProducerFactory: ProducerFactory<String, Any>,
    @Value("\${spring.kafka.operation_journal_topic}")
    private val journalTopic: String
) : JournalService {

    private val mapper = mapper()
    private val producer = kafkaProducerFactory.createProducer()

    override suspend fun logOperation(result: ManifestJournal) {
        val out = ProducerRecord<String, Any>(journalTopic, UUID.randomUUID().toString(), mapper.writeValueAsString(result))
        producer.send(out)
    }
}