package ru.sbertroika.tkp3.pro.manifest.processing.output.repository

import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.pro.model.Manifest
import ru.sbertroika.tkp3.pro.model.ManifestPK
import java.sql.Timestamp
import java.util.*

interface ManifestRepository : CoroutineCrudRepository<Manifest, ManifestPK> {

    suspend fun findByProjectIdAndProjectVersionAndActiveFrom(projectId: UUID, projectVersion: Int, activeFrom: Timestamp): Manifest?
}