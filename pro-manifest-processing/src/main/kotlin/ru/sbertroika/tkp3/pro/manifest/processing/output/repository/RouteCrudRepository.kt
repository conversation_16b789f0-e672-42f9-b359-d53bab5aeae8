package ru.sbertroika.tkp3.pro.manifest.processing.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.pro.common.repository.AbstractSettingsRepository
import ru.sbertroika.tkp3.pro.manifest.processing.util.timestampNow
import ru.sbertroika.tkp3.pro.model.*
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

interface RouteCrudRepository : CoroutineCrudRepository<Route, RoutePK> {

    suspend fun findAllByProjectId(projectId: UUID): Flow<Route>
}

interface RouteConstraintRepository : CoroutineCrudRepository<RouteConstraint, RouteConstraintPK> {

    suspend fun findAllByRouteId(routeId: UUID): Flow<RouteConstraint>
}

interface RouteConstraintExceptionRepository : CoroutineCrudRepository<RouteConstraintException, RouteConstraintExceptionPK> {

    suspend fun findAllByRouteConstraintId(routeConstraintId: UUID): Flow<RouteConstraintException>
}

interface RouteStationRepository : CoroutineCrudRepository<RouteStation, RouteStationPK> {

    suspend fun findAllByRouteId(routeId: UUID): Flow<RouteStation>
}

interface DispatchingRouteOrganizationRepository : CoroutineCrudRepository<DispatchingRouteOrganization, DispatchingRouteOrganizationPK> {
    suspend fun findAllByRouteId(routeId: UUID): Flow<DispatchingRouteOrganization>
}


@Component
class RouteRepository(
    override val dbClient: DatabaseClient,
    override val repository: RouteCrudRepository
): AbstractSettingsRepository<Route, RoutePK>(dbClient, repository){
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) \n"
    else "SELECT * \n") +
            "FROM route o\n" +
            "INNER JOIN (\n" +
            "    SELECT r_id, MAX(r_version) vers\n" +
            "    FROM route\n" +
            "    GROUP BY r_id\n" +
            ") o2 ON o.r_id = o2.r_id AND o.r_version = o2.vers AND o.r_status != 'IS_DELETED'"

    override fun toEntity(t: Readable) = Route(
        id = t.get("r_id") as UUID,
        version = t.get("r_version") as Int,
        name = t.get("r_name") as String,
        tags = t.get("tags") as String?,
        status = t.get("r_status") as RouteStatus,
        activeFrom = if (t.get("r_active_from") == null) null else Timestamp.valueOf(t.get("r_active_from") as LocalDateTime),
        activeTill = if (t.get("r_active_till") == null) null else Timestamp.valueOf(t.get("r_active_till") as LocalDateTime),
        index = t.get("r_index") as Int?,
        number = t.get("r_number") as String?,
        scheme = if (t.get("r_scheme") == null) null else t.get("r_scheme") as RouteScheme
    )

    override suspend fun findById(id: String): Route? {
        return dbClient.sql("${getQuery()} AND o.r_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Route> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Route> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    fun addNumberFilter(query: String, number : String) = "$query AND o.r_number = '$number'"
    fun addOrgIdFilter(query: String, orgId : String) = "$query AND o.r_org_id = '%$orgId%'"
    fun addNameFilter(query: String, name : String) = "$query AND o.r_name LIKE '%$name%'"


    fun findAllByRequest(query: String): Flow<Route> {
        return dbClient.sql(query)
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && entity.status != RouteStatus.IS_DELETED) {
            repository.save(entity.copy(
                version = entity.version!! + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                status = RouteStatus.IS_DELETED
            ))
        }
    }

} 