package ru.sbertroika.tkp3.pro.manifest.processing.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.pro.model.Project
import ru.sbertroika.tkp3.pro.model.ProjectFunction
import ru.sbertroika.tkp3.pro.model.ProjectFunctionPK
import ru.sbertroika.tkp3.pro.model.ProjectPK
import java.util.*

interface ProjectRepository : CoroutineCrudRepository<Project, ProjectPK> {

    suspend fun findAllByIdAndEndDateIsNull(id: UUID): Project?

    suspend fun findAllByIdAndVersion(id: UUID, version: Int): Project?
}

interface ProjectFunctionRepository : CoroutineCrudRepository<ProjectFunction, ProjectFunctionPK> {

    suspend fun findAllByProjectIdAndVersion(projectId: UUID, version: Int): Flow<ProjectFunction>
}