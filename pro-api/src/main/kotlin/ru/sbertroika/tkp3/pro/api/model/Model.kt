package ru.sbertroika.tkp3.pro.api.model

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.ZonedDateTime
import java.util.*

/**
 * Операция
 */
data class Operation(

    /**
     * Тип операции
     * @see OperationType
     */
    val type: OperationType,

    /**
     * Заводской номер терминала
     */
    val terminalSerial: String,

    /**
     * Идентификатор терминала
     */
    @Column("terminal_id")
    val terminalId: UUID,

    /**
     * Банковский идентификатор терминала
     */
    val tid: String? = null,

    /**
     * Единый регистрационный номер (ERN)
     */
    val ern: Int,

    /**
     * Данные обработки операции
     */
    val raw: String? = null,

    /**
     * Формат кодирования результата обработки операции
     * @see OperationRawFormat
     */
    val rawType: OperationRawFormat? = null,

    /**
     * Билеты
     */
    val tickets: List<Ticket>,

    /**
     * Идентификатор носителя (UID)
     */
    val cardUid: String? = null,

    /**
     * Идентификатор носителя (Hash(PAN))
     */
    val cardHash: String? = null,

    /**
     * Идентификатор носителя (HashType)
     */
    val cardHashType: String? = null,

    /**
     * Идентификатор шаблона списания
     */
    val templateId: UUID? = null,

    /**
     * Идентификатор абонемента
     */
    val abonementId: Long? = null
)

/**
 * Билет
 */
data class Ticket(

    /**
     * Серия билета
     */
    val ticketSeries: String,

    /**
     * Номер билета
     */
    val ticketNumber: String,

    /**
     * Дата формирования билета в UTC+0
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val createdAt: ZonedDateTime,

    /**
     * Манифест
     */
    val manifest: String,

    /**
     * Версия манифеста
     */
    val manifestVersion: Long,

    /**
     * Номер смены
     */
    val shiftNumber: Long,

    /**
     * Идентификатор услуги
     */
    val serviceId: String,

    /**
     * Идентификатор тарифа
     */
    val tariffId: String,

    /**
     * Идентификатор продукта
     */
    val productId: String,

    /**
     * Стоимость билета
     */
    val amount: Long,

    /**
     * Станция входа
     */
    val stationFromId: String?,

    /**
     * Станция выхода
     */
    val stationToId: String?
)

/**
 * Типы операции
 */
enum class OperationType {
    /**
     * Проход за наличные
     */
    PASS_CASH,

    /**
     * Проход по БК
     */
    PASS_EMV,

    /**
     * Проход по кошельку Тройки
     */
    PASS_TROIKA_WALLET,

    /**
     * Проход по билету Тройки
     */
    PASS_TROIKA_TICKET,

    /**
     * Идентификационный процессинг (кошелек)
     */
    PASS_ABT_WALLET,

    /**
     * Идентификационный процессинг (билет)
     */
    PASS_ABT_TICKET
}

enum class OperationRawFormat {
    /**
     * Сырые данные упакованные в Base64
     */
    CB64
}

enum class ShiftOperationType {
    OPEN, CLOSE
}

@Table("shift_operation")
data class ShiftOperation(

    @Column("created_at")
    val createdAt: ZonedDateTime,

    @Column("record_at")
    val recordAt: ZonedDateTime,

    /**
     * Тип операции
     * @see ShiftOperationType
     */
    @Column("type")
    val type: ShiftOperationType,

    /**
     * Заводской номер терминала
     */
    @Column("terminal_serial")
    val terminalSerial: String,

    /**
     * Банковский идентификатор терминала
     */
    @Column("tid")
    val tid: String? = null,

    /**
     * Идентификатор терминала
     */
    @Column("terminal_id")
    val terminalId: UUID,

    /**
     * Идентификатор проекта
     */
    @Column("project_id")
    val projectId: UUID,

    /**
     * Идентификатор организации
     */
    @Column("org_id")
    val orgId: UUID,

    /**
     * Единый регистрационный номер (ERN)
     */
    @Column("ern")
    val ern: Int,

    /**
     * Идентификатор пользователя открывшего/закрывшего смену
     */
    @Column("user_id")
    val userId: String,

    /**
     * Манифест
     */
    @Column("manifest")
    val manifest: String? = null,

    /**
     * Версия манифеста
     */
    @Column("manifest_ver")
    val manifestVersion: Int? = null,

    /**
     * Номер смены
     */
    @Column("shift_num")
    val shiftNumber: Long,

    /**
     * Идентификатор маршрута
     */
    @Column("r_id")
    val routeId: String? = null,

    /**
     * Версия маршрута
     */
    @Column("r_ver")
    val routeVersion: Int? = null,

    /**
     * Идентификатор Т/С
     */
    @Column("v_id")
    val vehicleId: String? = null,

    /**
     * Версия Т/С
     */
    @Column("v_ver")
    val vehicleVersion: Int? = null
)