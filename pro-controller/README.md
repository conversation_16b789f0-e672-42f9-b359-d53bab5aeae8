# Pro Controller API

Микросервис для управления проектами СберТройка ПРО.

## Запуск локально

```bash
./gradlew :pro-controller:bootRun --args='--spring.profiles.active=local'
```

Сервис будет доступен на `http://localhost:8085`

## API Endpoints

### Проекты

#### Получить список проектов с пагинацией и фильтрацией
```
GET /api/v1/pro/projects
```

**Параметры:**
- `status` (опционально) - фильтр по статусу проекта
- `region` (опционально) - фильтр по региону
- `projectType` (опционально) - фильтр по типу проекта
- `page` (по умолчанию 0) - номер страницы
- `size` (по умолчанию 20) - размер страницы

**Ответ:**
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": "uuid",
        "name": "Название проекта",
        "code": "ST-PRO-001",
        "status": "ACTIVE",
        "contractNumber": "СТ-ПРО-2024-001",
        "contractName": "Договор",
        "operatorOrganization": {
          "id": "uuid",
          "name": "ООО Оператор",
          "role": "operator"
        }
      }
    ],
    "pagination": {
      "page": 0,
      "size": 20,
      "totalElements": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrevious": false
    }
  }
}
```

#### Получить проект по ID
```
GET /api/v1/pro/projects/{id}
```

#### Создать проект
```
POST /api/v1/pro/projects
```

#### Обновить проект
```
PUT /api/v1/pro/projects/{id}
```

#### Удалить проект (логическое удаление)
```
DELETE /api/v1/pro/projects/{id}
```

#### Синхронизировать проект с договором
```
POST /api/v1/pro/projects/{id}/sync
```

#### Активировать проект
```
POST /api/v1/pro/projects/{id}/activate
```

#### Завершить проект
```
POST /api/v1/pro/projects/{id}/complete
```

### Географические справочники

#### Поиск стран (autocomplete)
```
GET /api/v1/pro/geographic/countries?name=рос
```

#### Поиск регионов (autocomplete)
```
GET /api/v1/pro/geographic/regions?name=моск&countryId=uuid
```

#### Поиск городов (autocomplete)
```
GET /api/v1/pro/geographic/cities?name=моск&regionId=uuid
```

#### Поиск районов (autocomplete)
```
GET /api/v1/pro/geographic/districts?name=центр&cityId=uuid
```

#### Создать или найти страну
```
POST /api/v1/pro/geographic/countries
```

**Тело запроса:**
```json
{
  "name": "Россия"
}
```

#### Создать или найти регион
```
POST /api/v1/pro/geographic/regions
```

**Тело запроса:**
```json
{
  "name": "Московская область",
  "countryId": "uuid"
}
```

#### Создать или найти город
```
POST /api/v1/pro/geographic/cities
```

**Тело запроса:**
```json
{
  "name": "Москва",
  "regionId": "uuid"
}
```

#### Создать или найти район
```
POST /api/v1/pro/geographic/districts
```

**Тело запроса:**
```json
{
  "name": "Центральный",
  "cityId": "uuid"
}
```

## Структура ответов

Все API endpoints возвращают ответы в формате:

```json
{
  "success": true|false,
  "message": "Опциональное сообщение",
  "data": "Данные ответа",
  "timestamp": "2024-01-01T12:00:00"
}
```

## Фильтрация и пагинация

Для списков поддерживается:
- Фильтрация по различным полям
- Пагинация с параметрами `page` и `size`
- Сортировка по полям
- Глобальный поиск

## Обработка ошибок

При возникновении ошибок API возвращает:
```json
{
  "success": false,
  "message": "Описание ошибки",
  "timestamp": "2024-01-01T12:00:00"
}
``` 