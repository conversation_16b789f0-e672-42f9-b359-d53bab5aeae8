package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.CountryRepository
import ru.sbertroika.tkp3.pro.controller.output.RegionRepository
import ru.sbertroika.tkp3.pro.controller.output.CityRepository
import ru.sbertroika.tkp3.pro.controller.output.DistrictRepository
import ru.sbertroika.tkp3.pro.model.Country
import ru.sbertroika.tkp3.pro.model.Region
import ru.sbertroika.tkp3.pro.model.City
import ru.sbertroika.tkp3.pro.model.District
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Service
class GeographicService(
    private val countryRepository: CountryRepository,
    private val regionRepository: RegionRepository,
    private val cityRepository: CityRepository,
    private val districtRepository: DistrictRepository
) {

    /**
     * Поиск стран по названию (autocomplete)
     */
    fun searchCountries(name: String): Flux<Country> {
        return if (name.isBlank()) {
            countryRepository.findAllOrderByName()
        } else {
            countryRepository.findByNameStartingWithIgnoreCase(name)
        }
    }

    /**
     * Поиск регионов по названию и стране (autocomplete)
     */
    fun searchRegions(name: String, countryId: UUID? = null): Flux<Region> {
        return when {
            countryId == null -> {
                if (name.isBlank()) {
                    Flux.empty()
                } else {
                    regionRepository.findByNameStartingWithIgnoreCase(name)
                }
            }
            name.isBlank() -> {
                regionRepository.findByCountryId(countryId)
            }
            else -> {
                regionRepository.findByCountryIdAndNameStartingWithIgnoreCase(countryId, name)
            }
        }
    }

    /**
     * Поиск городов по названию и региону (autocomplete)
     */
    fun searchCities(name: String, regionId: UUID? = null): Flux<City> {
        return when {
            regionId == null -> {
                if (name.isBlank()) {
                    Flux.empty()
                } else {
                    cityRepository.findByNameStartingWithIgnoreCase(name)
                }
            }
            name.isBlank() -> {
                cityRepository.findByRegionId(regionId)
            }
            else -> {
                cityRepository.findByRegionIdAndNameStartingWithIgnoreCase(regionId, name)
            }
        }
    }

    /**
     * Поиск районов по названию и городу (autocomplete)
     */
    fun searchDistricts(name: String, cityId: UUID? = null): Flux<District> {
        return when {
            cityId == null -> {
                if (name.isBlank()) {
                    Flux.empty()
                } else {
                    districtRepository.findByNameStartingWithIgnoreCase(name)
                }
            }
            name.isBlank() -> {
                districtRepository.findByCityId(cityId)
            }
            else -> {
                districtRepository.findByCityIdAndNameStartingWithIgnoreCase(cityId, name)
            }
        }
    }

    /**
     * Создание или поиск страны
     */
    fun findOrCreateCountry(name: String): Mono<Country> {
        return countryRepository.findByNameStartingWithIgnoreCase(name)
            .filter { it.name.equals(name, ignoreCase = true) }
            .next()
            .switchIfEmpty(
                Mono.defer {
                    val country = Country(
                        id = UUID.randomUUID(),
                        name = name,
                        code = null,
                        createdAt = Timestamp.valueOf(LocalDateTime.now()),
                        updatedAt = Timestamp.valueOf(LocalDateTime.now())
                    )
                    countryRepository.save(country)
                }
            )
    }

    /**
     * Создание или поиск региона
     */
    fun findOrCreateRegion(name: String, countryId: UUID): Mono<Region> {
        return regionRepository.findByCountryIdAndNameStartingWithIgnoreCase(countryId, name)
            .filter { it.name.equals(name, ignoreCase = true) }
            .next()
            .switchIfEmpty(
                Mono.defer {
                    val region = Region(
                        id = UUID.randomUUID(),
                        name = name,
                        countryId = countryId,
                        code = null,
                        createdAt = Timestamp.valueOf(LocalDateTime.now()),
                        updatedAt = Timestamp.valueOf(LocalDateTime.now())
                    )
                    regionRepository.save(region)
                }
            )
    }

    /**
     * Создание или поиск города
     */
    fun findOrCreateCity(name: String, regionId: UUID): Mono<City> {
        return cityRepository.findByRegionIdAndNameStartingWithIgnoreCase(regionId, name)
            .filter { it.name.equals(name, ignoreCase = true) }
            .next()
            .switchIfEmpty(
                Mono.defer {
                    val city = City(
                        id = UUID.randomUUID(),
                        name = name,
                        regionId = regionId,
                        code = null,
                        createdAt = Timestamp.valueOf(LocalDateTime.now()),
                        updatedAt = Timestamp.valueOf(LocalDateTime.now())
                    )
                    cityRepository.save(city)
                }
            )
    }

    /**
     * Создание или поиск района
     */
    fun findOrCreateDistrict(name: String, cityId: UUID): Mono<District> {
        return districtRepository.findByCityIdAndNameStartingWithIgnoreCase(cityId, name)
            .filter { it.name.equals(name, ignoreCase = true) }
            .next()
            .switchIfEmpty(
                Mono.defer {
                    val district = District(
                        id = UUID.randomUUID(),
                        name = name,
                        cityId = cityId,
                        code = null,
                        createdAt = Timestamp.valueOf(LocalDateTime.now()),
                        updatedAt = Timestamp.valueOf(LocalDateTime.now())
                    )
                    districtRepository.save(district)
                }
            )
    }
} 