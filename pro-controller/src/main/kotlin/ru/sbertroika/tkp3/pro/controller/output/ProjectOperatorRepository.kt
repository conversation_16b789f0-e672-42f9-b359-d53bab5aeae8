package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.ProjectOperator
import reactor.core.publisher.Flux
import java.util.*

@Repository
interface ProjectOperatorRepository : ReactiveCrudRepository<ProjectOperator, UUID> {

    @Query("SELECT * FROM project_operator WHERE po_project_id = :projectId AND po_project_version = :projectVersion")
    fun findByProjectIdAndVersion(projectId: UUID, projectVersion: Int): Flux<ProjectOperator>

    @Query("SELECT * FROM project_operator WHERE po_project_id = :projectId ORDER BY po_project_version DESC LIMIT 1")
    fun findLatestByProjectId(projectId: UUID): Flux<ProjectOperator>
} 