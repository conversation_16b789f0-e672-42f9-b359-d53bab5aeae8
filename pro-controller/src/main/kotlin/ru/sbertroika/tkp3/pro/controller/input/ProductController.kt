package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.ProductService
import ru.sbertroika.tkp3.pro.controller.model.ProductDto
import ru.sbertroika.tkp3.pro.model.Product
import java.util.*

@RestController
@RequestMapping("/api/v1/pro")
class ProductController(
    private val productService: ProductService
) {

    @GetMapping("/products")
    fun getAllProducts(): Flux<ApiResponse<ProductDto>> {
        return productService.getAllProducts()
            .map { product ->
                ApiResponse.success(product)
            }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<ProductDto>("Ошибка получения продуктов: ${error.message}"))
            }
    }

    @GetMapping("/products/{id}")
    fun getProductById(@PathVariable id: String): Mono<ApiResponse<ProductDto>> {
        return productService.getProductById(UUID.fromString(id))
            .map { product ->
                ApiResponse.success(product)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<ProductDto>("Ошибка получения продукта: ${error.message}"))
            }
    }

    @GetMapping("/projects/{projectId}/products")
    fun getProductsByProject(
        @PathVariable projectId: String,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(required = false) name: String?,
        @RequestParam(required = false) status: String?
    ): Mono<ApiResponse<Map<String, Any>>> {
        return productService.getProductsByProject(
            UUID.fromString(projectId), page, size, name, status
        )
            .map { result ->
                ApiResponse.success(result)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения продуктов проекта: ${error.message}"))
            }
    }

    @PostMapping("/products/batch")
    fun getProductsByIds(@RequestBody request: Map<String, List<String>>): Flux<ApiResponse<ProductDto>> {
        val productIds = request["productIds"]?.map { UUID.fromString(it) } ?: emptyList()
        return productService.getProductsByIds(productIds)
            .map { product ->
                ApiResponse.success(product)
            }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<ProductDto>("Ошибка получения продуктов по ID: ${error.message}"))
            }
    }

    @PostMapping("/projects/{projectId}/products")
    fun createProduct(@PathVariable projectId: String, @RequestBody product: Product): Mono<ApiResponse<Product>> {
        return productService.createProduct(UUID.fromString(projectId), product)
            .map { createdProduct ->
                ApiResponse.success(createdProduct, "Продукт успешно создан")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Product>("Ошибка создания продукта: ${error.message}"))
            }
    }

    @PutMapping("/products/{id}")
    fun updateProduct(@PathVariable id: String, @RequestBody product: Product): Mono<ApiResponse<Product>> {
        return productService.updateProduct(UUID.fromString(id), product)
            .map { updatedProduct ->
                ApiResponse.success(updatedProduct, "Продукт успешно обновлен")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Product>("Ошибка обновления продукта: ${error.message}"))
            }
    }

    @DeleteMapping("/products/{id}")
    fun deleteProduct(@PathVariable id: String): Mono<ApiResponse<String>> {
        return productService.deleteProduct(UUID.fromString(id))
            .map {
                ApiResponse.success("Продукт успешно удален")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка удаления продукта: ${error.message}"))
            }
    }
} 