package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.Region
import reactor.core.publisher.Flux
import java.util.*

@Repository
interface RegionRepository : ReactiveCrudRepository<Region, UUID> {

    @Query("SELECT * FROM region WHERE r_name ILIKE :name || '%' ORDER BY r_name LIMIT 10")
    fun findByNameStartingWithIgnoreCase(name: String): Flux<Region>

    @Query("SELECT * FROM region WHERE r_country_id = :countryId ORDER BY r_name")
    fun findByCountryId(countryId: UUID): Flux<Region>

    @Query("SELECT * FROM region WHERE r_country_id = :countryId AND r_name ILIKE :name || '%' ORDER BY r_name LIMIT 10")
    fun findByCountryIdAndNameStartingWithIgnoreCase(countryId: UUID, name: String): Flux<Region>
} 