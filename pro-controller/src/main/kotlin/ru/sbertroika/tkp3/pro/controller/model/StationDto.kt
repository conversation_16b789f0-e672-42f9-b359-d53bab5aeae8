package ru.sbertroika.tkp3.pro.controller.model

import org.springframework.data.relational.core.mapping.Column
import java.sql.Timestamp
import java.util.*

/**
 * DTO для отображения станций в UI с названиями географических объектов
 */
data class StationDto(
    /** Уникальный идентификатор станции */
    @Column("id")
    val id: UUID? = null,

    /** Версия записи станции */
    @Column("version")
    val version: Int? = null,

    /** Дата и время создания версии */
    @Column("versionCreatedAt")
    val versionCreatedAt: Timestamp? = null,

    /** Идентификатор пользователя, создавшего версию */
    @Column("versionCreatedBy")
    val versionCreatedBy: UUID? = null,

    /** Идентификатор проекта */
    @Column("projectId")
    val projectId: UUID? = null,

    /** Идентификатор тарифной зоны */
    @Column("tariffZoneId")
    val tariffZoneId: UUID? = null,

    /** Наименование станции */
    @Column("name")
    val name: String? = null,

    /** Латинское наименование станции */
    @Column("latinName")
    val latinName: String? = null,

    /** Статус станции */
    @Column("status")
    val status: String? = null,

    /** Широта координат станции */
    @Column("latitude")
    val latitude: Double? = null,

    /** Долгота координат станции */
    @Column("longitude")
    val longitude: Double? = null,

    /** Идентификатор города */
    @Column("cityId")
    val cityId: UUID? = null,

    /** Идентификатор района */
    @Column("districtId")
    val districtId: UUID? = null,

    /** Идентификатор региона */
    @Column("regionId")
    val regionId: UUID? = null,

    /** Идентификатор страны */
    @Column("countryId")
    val countryId: UUID? = null,

    /** Теги станции */
    @Column("tags")
    val tags: String = "",

    /** Дата последней синхронизации */
    @Column("lastSyncDate")
    val lastSyncDate: Timestamp? = null,

    /** Статус синхронизации */
    @Column("syncStatus")
    val syncStatus: String? = null,

    // Поля для отображения названий географических объектов
    /** Название страны */
    @Column("country")
    val country: String? = null,

    /** Название региона */
    @Column("region")
    val region: String? = null,

    /** Название города */
    @Column("city")
    val city: String? = null,

    /** Название района */
    @Column("district")
    val district: String? = null,

    /** Есть ли координаты */
    @Column("hasCoordinates")
    val hasCoordinates: Boolean? = null,

    /** Координаты в строковом формате */
    @Column("coordinates")
    val coordinates: String? = null
) 