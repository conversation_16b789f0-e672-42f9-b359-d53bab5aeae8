package ru.sbertroika.tkp3.pro.controller.model

import ru.sbertroika.tkp3.pro.model.TariffStatus
import ru.sbertroika.tkp3.pro.model.PayMethodType
import java.sql.Timestamp
import java.util.*

data class TariffDto(
    val id: UUID? = null,
    val version: Int? = null,
    val versionCreatedAt: Timestamp? = null,
    val versionCreatedBy: UUID? = null,
    val projectId: UUID? = null,
    val name: String? = null,
    val status: TariffStatus? = null,
    val tags: String? = null
)

data class TariffCreateRequest(
    val projectId: UUID,
    val name: String,
    val status: TariffStatus = TariffStatus.ACTIVE,
    val tags: String? = null
)

data class TariffUpdateRequest(
    val name: String? = null,
    val status: TariffStatus? = null,
    val tags: String? = null
)

data class TariffListResponse(
    val content: List<TariffDto>,
    val pagination: PaginationInfo
)

data class PaginationInfo(
    val page: Int,
    val size: Int,
    val totalElements: Long,
    val totalPages: Int,
    val hasNext: Boolean,
    val hasPrevious: Boolean
)

// DTO для работы с тарифными матрицами
data class TariffMatrixDto(
    val id: UUID? = null,
    val productDictRowId: UUID? = null,
    val stationFromId: UUID? = null,
    val stationToId: UUID? = null,
    val amount: Long? = null,
    val stationFromName: String? = null,
    val stationToName: String? = null,
    val tags: String? = null
)

data class TariffMatrixCreateRequest(
    val productDictRowId: UUID,
    val stationFromId: UUID,
    val stationToId: UUID,
    val amount: Long
)

data class TariffMatrixUpdateRequest(
    val amount: Long
)

data class TariffMatrixBulkUpdateRequest(
    val matrixData: Map<String, Long> // Ключ в формате "stationFrom-stationTo", значение - цена
)

// DTO для работы с продуктами в тарифах
data class ProductDictRowDto(
    val id: UUID? = null,
    val version: Int? = null,
    val versionCreatedAt: Timestamp? = null,
    val versionCreatedBy: UUID? = null,
    val projectId: UUID? = null,
    val productId: UUID? = null,
    val tariffId: UUID? = null,
    val paymentMethodType: PayMethodType? = null,
    val isFixPrice: Boolean? = null,
    val price: Long? = null,
    val tags: String? = null,
    val productName: String? = null,
    val tariffName: String? = null
)

data class ProductDictRowCreateRequest(
    val projectId: UUID,
    val productId: UUID,
    val tariffId: UUID,
    val paymentMethodType: PayMethodType,
    val isFixPrice: Boolean,
    val price: Long? = null,
    val tags: String? = null
)

data class ProductDictRowUpdateRequest(
    val paymentMethodType: PayMethodType? = null,
    val isFixPrice: Boolean? = null,
    val price: Long? = null,
    val tags: String? = null
)

// DTO для работы с тарифными ограничениями
data class TariffConstraintDto(
    val id: UUID? = null,
    val version: Int? = null,
    val versionCreatedAt: Timestamp? = null,
    val versionCreatedBy: UUID? = null,
    val tariffId: UUID? = null,
    val type: String? = null,
    val baseRule: String? = null,
    val tags: String? = null
)

data class TariffConstraintCreateRequest(
    val tariffId: UUID,
    val type: String,
    val baseRule: String? = null,
    val tags: String? = null
)

data class TariffConstraintUpdateRequest(
    val type: String? = null,
    val baseRule: String? = null,
    val tags: String? = null
)

// DTO для работы с исключениями тарифных ограничений
data class TariffConstraintExceptionDto(
    val id: UUID? = null,
    val version: Int? = null,
    val versionCreatedAt: Timestamp? = null,
    val versionCreatedBy: UUID? = null,
    val tariffConstraintId: UUID? = null,
    val exceptionId: UUID? = null,
    val tags: String? = null
)

data class TariffConstraintExceptionCreateRequest(
    val tariffConstraintId: UUID,
    val exceptionId: UUID,
    val tags: String? = null
)

// DTO для комплексной информации о тарифе
data class TariffFullInfoDto(
    val tariff: TariffDto,
    val productDictRows: List<ProductDictRowDto>,
    val tariffMatrices: List<TariffMatrixDto>,
    val constraints: List<TariffConstraintDto>,
    val constraintExceptions: List<TariffConstraintExceptionDto>
)

// DTO для назначения тарифов на маршруты
data class RouteTariffAssignmentDto(
    val routeId: UUID,
    val routeName: String? = null,
    val tariffId: UUID,
    val tariffName: String? = null,
    val assignedAt: Timestamp? = null
)

data class RouteTariffAssignmentCreateRequest(
    val routeId: UUID,
    val tariffId: UUID
)

// DTO для работы с тарифами по маршрутам
data class RouteTariffsResponse(
    val routeId: UUID,
    val routeName: String? = null,
    val routeDescription: String? = null,
    val tariffs: Map<String, TariffWithPaymentMethodsDto>
)

// DTO для тарифа со способами оплаты
data class TariffWithPaymentMethodsDto(
    val id: UUID? = null,
    val name: String? = null,
    val description: String? = null,
    val status: String? = null,
    val tags: String? = null,
    val paymentMethods: List<TariffPaymentMethodDto> = emptyList()
)

// DTO для способа оплаты в тарифе
data class TariffPaymentMethodDto(
    val id: String,
    val type: String,
    val name: String,
    val products: List<ProductDictRowDto> = emptyList()
) 