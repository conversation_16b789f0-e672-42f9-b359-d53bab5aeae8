package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.District
import reactor.core.publisher.Flux
import java.util.*

@Repository
interface DistrictRepository : ReactiveCrudRepository<District, UUID> {

    @Query("SELECT * FROM district WHERE d_name ILIKE :name || '%' ORDER BY d_name LIMIT 10")
    fun findByNameStartingWithIgnoreCase(name: String): Flux<District>

    @Query("SELECT * FROM district WHERE d_city_id = :cityId ORDER BY d_name")
    fun findByCityId(cityId: UUID): Flux<District>

    @Query("SELECT * FROM district WHERE d_city_id = :cityId AND d_name ILIKE :name || '%' ORDER BY d_name LIMIT 10")
    fun findByCityIdAndNameStartingWithIgnoreCase(cityId: UUID, name: String): Flux<District>
} 