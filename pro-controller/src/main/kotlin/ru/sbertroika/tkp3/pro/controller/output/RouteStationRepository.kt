package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.RouteStation
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.*

@Repository
interface RouteStationRepository : ReactiveCrudRepository<RouteStation, ru.sbertroika.tkp3.pro.model.RouteStationPK> {

    /**
     * Получить все станции маршрута (последние версии)
     */
    @Query("""
        SELECT rs.* FROM route_station rs
        INNER JOIN (
            SELECT rs_id, MAX(rs_version) as max_version
            FROM route_station
            WHERE r_id = :routeId AND rs_is_del = false
            GROUP BY rs_id
        ) latest ON rs.rs_id = latest.rs_id AND rs.rs_version = latest.max_version
        ORDER BY rs.rs_pos
    """)
    fun findStationsByRouteId(routeId: UUID): Flux<RouteStation>

    /**
     * Получить все версии станций маршрута
     */
    @Query("""
        SELECT * FROM route_station 
        WHERE r_id = :routeId 
        ORDER BY rs_pos, rs_version DESC
    """)
    fun findAllVersionsByRouteId(routeId: UUID): Flux<RouteStation>

    /**
     * Получить станцию маршрута по ID (последняя версия)
     */
    @Query("""
        SELECT * FROM route_station 
        WHERE rs_id = :id 
        ORDER BY rs_version DESC 
        LIMIT 1
    """)
    fun findRouteStationById(id: UUID): Mono<RouteStation>

    /**
     * Получить максимальную позицию станции в маршруте
     */
    @Query("""
        SELECT COALESCE(MAX(rs_pos), 0) FROM route_station 
        WHERE r_id = :routeId AND rs_is_del = false
    """)
    fun findMaxPositionByRouteId(routeId: UUID): Flux<Int>

    /**
     * Проверить существование станции в маршруте
     */
    @Query("""
        SELECT COUNT(*) > 0 FROM route_station 
        WHERE r_id = :routeId 
        AND st_id = :stationId 
        AND rs_is_del = false
        AND rs_id != :excludeId
    """)
    fun existsByRouteIdAndStationId(
        routeId: UUID,
        stationId: UUID,
        excludeId: UUID? = null
    ): Flux<Boolean>

    /**
     * Получить станции по списку ID маршрутов
     */
    @Query("""
        SELECT rs.* FROM route_station rs
        INNER JOIN (
            SELECT rs_id, MAX(rs_version) as max_version
            FROM route_station
            WHERE r_id = ANY(:routeIds) AND rs_is_del = false
            GROUP BY rs_id
        ) latest ON rs.rs_id = latest.rs_id AND rs.rs_version = latest.max_version
        ORDER BY rs.r_id, rs.rs_pos
    """)
    fun findStationsByRouteIds(routeIds: List<UUID>): Flux<RouteStation>
} 