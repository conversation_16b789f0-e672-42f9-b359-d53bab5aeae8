package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.controller.model.RouteStationInfo
import reactor.core.publisher.Flux
import java.util.*

@Repository
interface RouteStationInfoRepository : ReactiveCrudRepository<RouteStationInfo, UUID> {

    /**
     * Получить информацию о станциях маршрута с данными из справочника станций
     */
    @Query("""
        SELECT 
            rs.rs_id as route_station_id,
            rs.r_id as route_id,
            rs.st_id as station_id,
            rs.rs_pos as position,
            s.st_name as station_name,
            rs.rs_is_del as is_deleted,
            rs.rs_version as version,
            rs.rs_version_created_at as version_created_at,
            rs.rs_version_created_by as version_created_by,
            rs.tags
        FROM route_station rs
        INNER JOIN (
            SELECT r_id, st_id, MAX(rs_version) as max_version
            FROM route_station
            WHERE r_id = :routeId AND rs_is_del = false
            GROUP BY r_id, st_id
        ) latest ON rs.r_id = latest.r_id AND rs.st_id = latest.st_id AND rs.rs_version = latest.max_version
        LEFT JOIN station s ON rs.st_id = s.st_id
        WHERE rs.rs_is_del = false
        ORDER BY rs.rs_pos
    """)
    fun findRouteStationInfoByRouteId(routeId: UUID): Flux<RouteStationInfo>

    /**
     * Получить информацию о станциях для нескольких маршрутов
     */
    @Query("""
        SELECT 
            rs.rs_id as route_station_id,
            rs.r_id as route_id,
            rs.st_id as station_id,
            rs.rs_pos as position,
            s.st_name as station_name,
            rs.rs_is_del as is_deleted,
            rs.rs_version as version,
            rs.rs_version_created_at as version_created_at,
            rs.rs_version_created_by as version_created_by,
            rs.tags
        FROM route_station rs
        INNER JOIN (
            SELECT r_id, st_id, MAX(rs_version) as max_version
            FROM route_station
            WHERE r_id IN (:routeIds) AND rs_is_del = false
            GROUP BY r_id, st_id
        ) latest ON rs.r_id = latest.r_id AND rs.st_id = latest.st_id AND rs.rs_version = latest.max_version
        LEFT JOIN station s ON rs.st_id = s.st_id
        WHERE rs.rs_is_del = false
        ORDER BY rs.r_id, rs.rs_pos
    """)
    fun findRouteStationInfoByRouteIds(routeIds: List<UUID>): Flux<RouteStationInfo>

    /**
     * Получить информацию о конкретной станции маршрута (последняя версия)
     */
    @Query("""
        SELECT 
            rs.rs_id as route_station_id,
            rs.r_id as route_id,
            rs.st_id as station_id,
            rs.rs_pos as position,
            s.st_name as station_name,
            rs.rs_is_del as is_deleted,
            rs.rs_version as version,
            rs.rs_version_created_at as version_created_at,
            rs.rs_version_created_by as version_created_by,
            rs.tags
        FROM route_station rs
        LEFT JOIN station s ON rs.st_id = s.st_id
        WHERE rs.rs_id = :routeStationId
        ORDER BY rs.rs_version DESC
        LIMIT 1
    """)
    fun findRouteStationInfoById(routeStationId: UUID): Flux<RouteStationInfo>
} 