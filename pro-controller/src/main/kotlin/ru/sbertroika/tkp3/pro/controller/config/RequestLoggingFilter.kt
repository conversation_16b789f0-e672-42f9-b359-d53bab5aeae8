package ru.sbertroika.tkp3.pro.controller.config

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.web.server.ServerWebExchange
import org.springframework.web.server.WebFilter
import org.springframework.web.server.WebFilterChain
import reactor.core.publisher.Mono

@Component
class RequestLoggingFilter : WebFilter {
    
    private val logger = LoggerFactory.getLogger(RequestLoggingFilter::class.java)
    
    override fun filter(exchange: ServerWebExchange, chain: WebFilterChain): Mono<Void> {
        val request = exchange.request
        val path = request.path.toString()
        val method = request.method?.toString() ?: "UNKNOWN"
        
        // Логируем основные детали запроса
        logger.info("=== Входящий запрос ===")
        logger.info("Метод: $method")
        logger.info("Путь: $path")
        logger.info("Query параметры: ${request.queryParams}")
        
        // Логируем заголовки
        logger.info("Заголовки:")
        request.headers.forEach { (name, values) ->
            if (name.equals("authorization", ignoreCase = true)) {
                // Маскируем JWT токен
                values.forEach { value ->
                    val maskedValue = if (value.startsWith("Bearer ")) {
                        "Bearer ${value.substring(7).take(10)}..."
                    } else {
                        "***"
                    }
                    logger.info("  $name: $maskedValue")
                }
            } else {
                logger.info("  $name: $values")
            }
        }
        
        // Проверяем наличие Authorization заголовка
        val authHeader = request.headers.getFirst("Authorization")
        if (authHeader == null) {
            if (method == "OPTIONS") {
                logger.info("ℹ️  OPTIONS запрос (CORS preflight) - Authorization не требуется")
            } else {
                logger.warn("⚠️  Отсутствует заголовок Authorization для запроса $method $path")
            }
        } else {
            logger.info("✅ Заголовок Authorization присутствует")
        }
        
        logger.info("=====================")
        
        return chain.filter(exchange)
    }
} 