package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.RouteService
import ru.sbertroika.tkp3.pro.model.Route
import ru.sbertroika.tkp3.pro.model.RouteStation
import ru.sbertroika.tkp3.pro.controller.model.RouteStationInfo
import java.util.*

@RestController
@RequestMapping("/api/v1/pro")
class RouteController(
    private val routeService: RouteService
) {

    @GetMapping("/routes")
    fun getRoutes(
        @RequestParam(required = false) projectId: String?,
        @RequestParam(required = false) status: String?,
        @RequestParam(required = false) scheme: String?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<Map<String, Any>>> {
        val projectUuid = projectId?.let { UUID.fromString(it) }
        
        return Mono.zip(
            routeService.getAllRoutes(projectUuid, status, scheme, page, size).collectList(),
            routeService.getRoutesCount(projectUuid, status, scheme)
        ).map { result ->
            val routes = result.t1
            val totalCount = result.t2
            val totalPages = (totalCount + size - 1) / size
            val response = mapOf(
                "content" to routes,
                "pagination" to mapOf(
                    "page" to page,
                    "size" to size,
                    "totalElements" to totalCount,
                    "totalPages" to totalPages,
                    "hasNext" to (page < totalPages - 1),
                    "hasPrevious" to (page > 0)
                )
            )
            ApiResponse.success(response)
        }.onErrorResume { error ->
            Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения маршрутов: ${error.message}"))
        }
    }

    @GetMapping("/routes/{id}")
    fun getRouteById(@PathVariable id: String): Mono<ApiResponse<Route>> {
        return routeService.getRouteById(UUID.fromString(id))
            .map { route ->
                ApiResponse.success(route)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Route>("Ошибка получения маршрута: ${error.message}"))
            }
    }

    @GetMapping("/routes/{id}/versions")
    fun getAllVersionsByRouteId(@PathVariable id: String): Mono<ApiResponse<List<Route>>> {
        return routeService.getAllVersionsByRouteId(UUID.fromString(id))
            .collectList()
            .map { routes ->
                ApiResponse.success(routes)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Route>>("Ошибка получения версий маршрута: ${error.message}"))
            }
    }

    @GetMapping("/projects/{projectId}/routes")
    fun getRoutesByProject(@PathVariable projectId: String): Mono<ApiResponse<List<Route>>> {
        return routeService.getRoutesByProjectId(UUID.fromString(projectId))
            .collectList()
            .map { routes ->
                ApiResponse.success(routes)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Route>>("Ошибка получения маршрутов проекта: ${error.message}"))
            }
    }

    @GetMapping("/routes/status/{status}")
    fun getRoutesByStatus(@PathVariable status: String): Mono<ApiResponse<List<Route>>> {
        return routeService.getRoutesByStatus(status)
            .collectList()
            .map { routes ->
                ApiResponse.success(routes)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Route>>("Ошибка получения маршрутов по статусу: ${error.message}"))
            }
    }

    @GetMapping("/routes/scheme/{scheme}")
    fun getRoutesByScheme(@PathVariable scheme: String): Mono<ApiResponse<List<Route>>> {
        return routeService.getRoutesByScheme(scheme)
            .collectList()
            .map { routes ->
                ApiResponse.success(routes)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Route>>("Ошибка получения маршрутов по схеме: ${error.message}"))
            }
    }

    @PostMapping("/routes")
    fun createRoute(@RequestBody route: Route): Mono<ApiResponse<Route>> {
        return routeService.createRoute(route)
            .map { createdRoute ->
                ApiResponse.success(createdRoute)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Route>("Ошибка создания маршрута: ${error.message}"))
            }
    }

    @PutMapping("/routes/{id}")
    fun updateRoute(@PathVariable id: String, @RequestBody route: Route): Mono<ApiResponse<Route>> {
        return routeService.updateRoute(UUID.fromString(id), route)
            .map { updatedRoute ->
                ApiResponse.success(updatedRoute)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Route>("Ошибка обновления маршрута: ${error.message}"))
            }
    }

    @DeleteMapping("/routes/{id}")
    fun deleteRoute(@PathVariable id: String): Mono<ApiResponse<String>> {
        return routeService.deleteRoute(UUID.fromString(id))
            .map {
                ApiResponse.success("Маршрут успешно удален")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка удаления маршрута: ${error.message}"))
            }
    }

    @PostMapping("/routes/{id}/activate")
    fun activateRoute(@PathVariable id: String): Mono<ApiResponse<Route>> {
        return routeService.activateRoute(UUID.fromString(id))
            .map { route ->
                ApiResponse.success(route, "Маршрут активирован")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Route>("Ошибка активации маршрута: ${error.message}"))
            }
    }

    @PostMapping("/routes/{id}/deactivate")
    fun deactivateRoute(@PathVariable id: String): Mono<ApiResponse<Route>> {
        return routeService.deactivateRoute(UUID.fromString(id))
            .map { route ->
                ApiResponse.success(route, "Маршрут деактивирован")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Route>("Ошибка деактивации маршрута: ${error.message}"))
            }
    }

    @PostMapping("/routes/{id}/block")
    fun blockRoute(@PathVariable id: String): Mono<ApiResponse<Route>> {
        return routeService.blockRoute(UUID.fromString(id))
            .map { route ->
                ApiResponse.success(route, "Маршрут заблокирован")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Route>("Ошибка блокировки маршрута: ${error.message}"))
            }
    }

    @GetMapping("/routes/{id}/stations")
    fun getRouteStations(@PathVariable id: String): Mono<ApiResponse<List<RouteStation>>> {
        return routeService.getRouteStations(UUID.fromString(id))
            .collectList()
            .map { stations ->
                ApiResponse.success(stations)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<RouteStation>>("Ошибка получения станций маршрута: ${error.message}"))
            }
    }

    @GetMapping("/routes/{id}/stations/info")
    fun getRouteStationInfo(@PathVariable id: String): Mono<ApiResponse<List<RouteStationInfo>>> {
        return routeService.getRouteStationInfo(UUID.fromString(id))
            .collectList()
            .map { stations ->
                ApiResponse.success(stations)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<RouteStationInfo>>("Ошибка получения информации о станциях маршрута: ${error.message}"))
            }
    }

    @PostMapping("/routes/{id}/stations")
    fun addStationToRoute(
        @PathVariable id: String,
        @RequestBody request: Map<String, String>
    ): Mono<ApiResponse<RouteStation>> {
        val stationId = UUID.fromString(request["stationId"] ?: "")
        
        return routeService.addStationToRoute(UUID.fromString(id), stationId)
            .map { routeStation ->
                ApiResponse.success(routeStation, "Станция добавлена к маршруту")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<RouteStation>("Ошибка добавления станции к маршруту: ${error.message}"))
            }
    }

    @DeleteMapping("/routes/stations/{stationId}")
    fun removeStationFromRoute(@PathVariable stationId: String): Mono<ApiResponse<String>> {
        return routeService.removeStationFromRoute(UUID.fromString(stationId))
            .map {
                ApiResponse.success("Станция удалена из маршрута")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка удаления станции из маршрута: ${error.message}"))
            }
    }

    @PutMapping("/routes/{id}/stations/positions")
    fun updateStationPositions(
        @PathVariable id: String,
        @RequestBody stationPositions: Map<String, Int>
    ): Mono<ApiResponse<String>> {
        val positions = stationPositions.mapKeys { UUID.fromString(it.key) }
        
        return routeService.updateStationPositions(UUID.fromString(id), positions)
            .map {
                ApiResponse.success("Позиции станций обновлены")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка обновления позиций станций: ${error.message}"))
            }
    }

    @GetMapping("/routes/check-number")
    fun checkRouteNumberUnique(
        @RequestParam projectId: String,
        @RequestParam number: String,
        @RequestParam(required = false) excludeId: String?
    ): Mono<ApiResponse<Boolean>> {
        val projectUuid = UUID.fromString(projectId)
        val excludeUuid = excludeId?.let { UUID.fromString(it) }
        
        return routeService.isRouteNumberUnique(projectUuid, number, excludeUuid)
            .map { isUnique ->
                ApiResponse.success(isUnique)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Boolean>("Ошибка проверки уникальности номера маршрута: ${error.message}"))
            }
    }
} 