package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.VehicleRepository
import ru.sbertroika.tkp3.pro.model.Vehicle
import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.VehicleType
import java.sql.Timestamp
import java.util.*

@Service
class VehicleService(
    private val vehicleRepository: VehicleRepository
) {

    /**
     * Получить все транспортные средства с фильтрацией и пагинацией
     */
    fun getVehiclesWithFilters(
        projectId: UUID,
        type: VehicleType? = null,
        status: VehicleStatus? = null,
        organizationId: UUID? = null,
        search: String? = null,
        page: Int = 0,
        size: Int = 10
    ): Mono<Map<String, Any>> {
        val offset = page * size
        
        return Mono.zip(
            vehicleRepository.findAllVehiclesWithFilters(
                projectId = projectId,
                type = type,
                status = status,
                organizationId = organizationId,
                search = search,
                size = size,
                offset = offset
            ).collectList(),
            vehicleRepository.countVehiclesWithFilters(
                projectId = projectId,
                type = type,
                status = status,
                organizationId = organizationId,
                search = search
            )
        ).map { tuple ->
            val vehicles = tuple.t1
            val total = tuple.t2
            mapOf(
                "content" to vehicles,
                "totalElements" to total,
                "totalPages" to ((total + size - 1) / size).toInt(),
                "currentPage" to page,
                "size" to size
            )
        }
    }

    /**
     * Получить транспортное средство по ID
     */
    fun getVehicleById(vehicleId: UUID): Mono<Vehicle> {
        return vehicleRepository.findVehicleById(vehicleId)
            .switchIfEmpty(Mono.error(RuntimeException("Транспортное средство не найдено")))
    }

    /**
     * Получить все версии транспортного средства
     */
    fun getAllVersionsByVehicleId(vehicleId: UUID): Flux<Vehicle> {
        return vehicleRepository.findAllVersionsByVehicleId(vehicleId)
    }

    /**
     * Создать новое транспортное средство
     */
    fun createVehicle(vehicle: Vehicle, createdBy: UUID): Mono<Vehicle> {
        return vehicleRepository.existsByProjectIdAndNumber(vehicle.projectId!!, vehicle.number!!)
            .flatMap { exists ->
                if (exists) {
                    Mono.error(RuntimeException("Транспортное средство с таким номером уже существует в проекте"))
                } else {
                    val newVehicle = vehicle.copy(
                        id = UUID.randomUUID(),
                        version = 1,
                        versionCreatedAt = Timestamp(System.currentTimeMillis()),
                        versionCreatedBy = createdBy,
                        status = VehicleStatus.ACTIVE
                    )
                    vehicleRepository.save(newVehicle)
                }
            }
    }

    /**
     * Обновить транспортное средство (создать новую версию)
     */
    fun updateVehicle(vehicleId: UUID, updatedVehicle: Vehicle, updatedBy: UUID): Mono<Vehicle> {
        return vehicleRepository.findVehicleById(vehicleId)
            .switchIfEmpty(Mono.error(RuntimeException("Транспортное средство не найдено")))
            .flatMap { existingVehicle ->
                // Проверяем, не занят ли номер другим транспортным средством
                if (updatedVehicle.number != existingVehicle.number) {
                    vehicleRepository.existsByProjectIdAndNumber(updatedVehicle.projectId!!, updatedVehicle.number!!)
                        .flatMap { exists ->
                            if (exists) {
                                Mono.error(RuntimeException("Транспортное средство с таким номером уже существует в проекте"))
                            } else {
                                createNewVersion(existingVehicle, updatedVehicle, updatedBy)
                            }
                        }
                } else {
                    createNewVersion(existingVehicle, updatedVehicle, updatedBy)
                }
            }
    }

    /**
     * Удалить транспортное средство (логическое удаление)
     */
    fun deleteVehicle(vehicleId: UUID, deletedBy: UUID): Mono<Vehicle> {
        return vehicleRepository.findVehicleById(vehicleId)
            .switchIfEmpty(Mono.error(RuntimeException("Транспортное средство не найдено")))
            .flatMap { existingVehicle ->
                val deletedVehicle = existingVehicle.copy(
                    version = existingVehicle.version!! + 1,
                    versionCreatedAt = Timestamp(System.currentTimeMillis()),
                    versionCreatedBy = deletedBy,
                    status = VehicleStatus.IS_DELETED
                )
                vehicleRepository.save(deletedVehicle)
            }
    }

    /**
     * Получить транспортные средства по проекту
     */
    fun getVehiclesByProject(projectId: UUID): Flux<Vehicle> {
        return vehicleRepository.findByProjectId(projectId)
    }

    /**
     * Получить транспортные средства по организации
     */
    fun getVehiclesByOrganization(organizationId: UUID): Flux<Vehicle> {
        return vehicleRepository.findByOrganizationId(organizationId)
    }

    /**
     * Получить транспортные средства по типу
     */
    fun getVehiclesByType(type: VehicleType): Flux<Vehicle> {
        return vehicleRepository.findByType(type)
    }

    /**
     * Получить транспортные средства по статусу
     */
    fun getVehiclesByStatus(status: VehicleStatus): Flux<Vehicle> {
        return vehicleRepository.findByStatus(status)
    }

    /**
     * Создать новую версию транспортного средства
     */
    private fun createNewVersion(existingVehicle: Vehicle, updatedVehicle: Vehicle, updatedBy: UUID): Mono<Vehicle> {
        val newVehicle = existingVehicle.copy(
            version = existingVehicle.version!! + 1,
            versionCreatedAt = Timestamp(System.currentTimeMillis()),
            versionCreatedBy = updatedBy,
            type = updatedVehicle.type,
            number = updatedVehicle.number,
            status = updatedVehicle.status,
            organizationId = updatedVehicle.organizationId,
            tags = updatedVehicle.tags
        )
        return vehicleRepository.save(newVehicle)
    }
} 