package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.Product
import ru.sbertroika.tkp3.pro.model.ProductPK
import ru.sbertroika.tkp3.pro.controller.model.ProductDto
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.*

@Repository
interface ProductRepository : ReactiveCrudRepository<Product, ProductPK> {

    /**
     * Получить все продукты (только последние версии, не удаленные)
     */
    @Query("""
        SELECT 
            p.p_id as id,
            p.p_version as version,
            p.p_version_created_at as versionCreatedAt,
            p.p_version_created_by as versionCreatedBy,
            p.p_project_id as projectId,
            p.p_name as name,
            p.p_status as status,
            p.tags
        FROM product p
        INNER JOIN (
            SELECT p_id, MAX(p_version) as max_version
            FROM product
            WHERE p_status != 'IS_DELETED'
            GROUP BY p_id
        ) latest ON p.p_id = latest.p_id AND p.p_version = latest.max_version
        WHERE p.p_status != 'IS_DELETED'
        ORDER BY p.p_version_created_at DESC
    """)
    fun findAllLatestVersions(): Flux<ProductDto>

    /**
     * Получить продукты проекта с фильтрами (только последние версии, не удаленные)
     */
    @Query("""
        SELECT 
            p.p_id as id,
            p.p_version as version,
            p.p_version_created_at as versionCreatedAt,
            p.p_version_created_by as versionCreatedBy,
            p.p_project_id as projectId,
            p.p_name as name,
            p.p_status as status,
            p.tags
        FROM product p
        INNER JOIN (
            SELECT p_id, MAX(p_version) as max_version
            FROM product
            WHERE p_project_id = :projectId AND p_status != 'IS_DELETED'
            GROUP BY p_id
        ) latest ON p.p_id = latest.p_id AND p.p_version = latest.max_version
        WHERE p.p_project_id = :projectId 
        AND p.p_status != 'IS_DELETED'
        AND (:name IS NULL OR p.p_name ILIKE '%' || :name || '%')
        AND (:status IS NULL OR p.p_status = :status)
        ORDER BY p.p_version_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findByProjectIdWithFilters(
        projectId: UUID,
        name: String? = null,
        status: String? = null,
        limit: Int,
        offset: Int
    ): Flux<ProductDto>

    /**
     * Подсчитать количество продуктов проекта с фильтрами
     */
    @Query("""
        SELECT COUNT(*) FROM product p
        INNER JOIN (
            SELECT p_id, MAX(p_version) as max_version
            FROM product
            WHERE p_project_id = :projectId AND p_status != 'IS_DELETED'
            GROUP BY p_id
        ) latest ON p.p_id = latest.p_id AND p.p_version = latest.max_version
        WHERE p.p_project_id = :projectId 
        AND p.p_status != 'IS_DELETED'
        AND (:name IS NULL OR p.p_name ILIKE '%' || :name || '%')
        AND (:status IS NULL OR p.p_status = :status)
    """)
    fun countByProjectIdWithFilters(
        projectId: UUID,
        name: String? = null,
        status: String? = null
    ): Mono<Long>

    /**
     * Получить продукт по ID (последняя версия, не удаленный)
     */
    @Query("""
        SELECT 
            p.p_id as id,
            p.p_version as version,
            p.p_version_created_at as versionCreatedAt,
            p.p_version_created_by as versionCreatedBy,
            p.p_project_id as projectId,
            p.p_name as name,
            p.p_status as status,
            p.tags
        FROM product p
        WHERE p.p_id = :id 
        AND p.p_version = (
            SELECT MAX(p_version) 
            FROM product 
            WHERE p_id = :id AND p_status != 'IS_DELETED'
        )
        AND p.p_status != 'IS_DELETED'
    """)
    fun findLatestVersionById(id: UUID): Mono<ProductDto>

    /**
     * Получить продукты по списку ID (последние версии, не удаленные)
     */
    @Query("""
        SELECT 
            p.p_id as id,
            p.p_version as version,
            p.p_version_created_at as versionCreatedAt,
            p.p_version_created_by as versionCreatedBy,
            p.p_project_id as projectId,
            p.p_name as name,
            p.p_status as status,
            p.tags
        FROM product p
        INNER JOIN (
            SELECT p_id, MAX(p_version) as max_version
            FROM product
            WHERE p_id = ANY(:ids) AND p_status != 'IS_DELETED'
            GROUP BY p_id
        ) latest ON p.p_id = latest.p_id AND p.p_version = latest.max_version
        WHERE p.p_status != 'IS_DELETED'
    """)
    fun findLatestVersionsByIds(ids: List<UUID>): Flux<ProductDto>
} 