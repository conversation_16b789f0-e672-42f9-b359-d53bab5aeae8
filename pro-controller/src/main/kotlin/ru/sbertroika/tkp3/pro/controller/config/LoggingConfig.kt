package ru.sbertroika.tkp3.pro.controller.config

import org.slf4j.MDC
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.server.ServerWebExchange
import org.springframework.web.server.WebFilter
import org.springframework.web.server.WebFilterChain
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import java.util.*

@Configuration
open class LoggingConfig {

    @Bean
    open fun userLoggingFilter(): WebFilter {
        return object : WebFilter {
            override fun filter(exchange: ServerWebExchange, chain: WebFilterChain): Mono<Void> {
                return JwtUtils.getCurrentUserId()
                    .defaultIfEmpty(UUID.randomUUID()) // Если пользователь не авторизован, используем случайный UUID
                    .flatMap { userId ->
                        MDC.put("userId", userId.toString())
                        chain.filter(exchange)
                            .doFinally {
                                MDC.remove("userId")
                            }
                    }
            }
        }
    }
} 