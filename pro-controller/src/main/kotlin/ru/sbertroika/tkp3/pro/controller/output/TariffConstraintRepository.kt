package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Repository
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.TariffConstraint
import ru.sbertroika.tkp3.pro.model.TariffConstraintType
import ru.sbertroika.tkp3.pro.model.BaseRule
import java.time.LocalDateTime
import java.util.*

@Service
open class TariffConstraintRepository(
    private val template: R2dbcEntityTemplate
) {

    /**
     * Найти все ограничения
     */
    fun findAll(): Flux<TariffConstraint> {
        val sql = """
            SELECT tc.*
            FROM tariff_constraint tc
            WHERE tc.tc_version = (
                SELECT MAX(tc2.tc_version) 
                FROM tariff_constraint tc2 
                WHERE tc2.tc_id = tc.tc_id
            )
            ORDER BY tc.tc_id
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .map { row, _ ->
                TariffConstraint(
                    id = row.get("tc_id", UUID::class.java),
                    version = row.get("tc_version", Int::class.java),
                    versionCreatedAt = row.get("tc_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("tc_version_created_by", UUID::class.java),
                    tariffId = row.get("t_id", UUID::class.java),
                    type = row.get("tc_type", String::class.java)?.let { TariffConstraintType.valueOf(it) },
                    baseRule = row.get("tc_base_rule", String::class.java)?.let { BaseRule.valueOf(it) },
                    tags = row.get("tags", String::class.java)
                )
            }
            .all()
    }

    /**
     * Найти все ограничения тарифа
     */
    fun findByTariffId(tariffId: UUID): Flux<TariffConstraint> {
        val sql = """
            SELECT tc.*
            FROM tariff_constraint tc
            WHERE tc.t_id = :tariffId
            AND tc.tc_version = (
                SELECT MAX(tc2.tc_version) 
                FROM tariff_constraint tc2 
                WHERE tc2.tc_id = tc.tc_id
            )
            ORDER BY tc.tc_id
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("tariffId", tariffId)
            .map { row, _ ->
                TariffConstraint(
                    id = row.get("tc_id", UUID::class.java),
                    version = row.get("tc_version", Int::class.java),
                    versionCreatedAt = row.get("tc_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("tc_version_created_by", UUID::class.java),
                    tariffId = row.get("t_id", UUID::class.java),
                    type = row.get("tc_type", String::class.java)?.let { TariffConstraintType.valueOf(it) },
                    baseRule = row.get("tc_base_rule", String::class.java)?.let { BaseRule.valueOf(it) },
                    tags = row.get("tags", String::class.java)
                )
            }
            .all()
    }

    /**
     * Найти ограничения тарифа по типу
     */
    fun findByTariffIdAndType(tariffId: UUID, type: TariffConstraintType): Flux<TariffConstraint> {
        val sql = """
            SELECT tc.*
            FROM tariff_constraint tc
            WHERE tc.t_id = :tariffId
            AND tc.tc_type = :type
            AND tc.tc_version = (
                SELECT MAX(tc2.tc_version) 
                FROM tariff_constraint tc2 
                WHERE tc2.tc_id = tc.tc_id
            )
            ORDER BY tc.tc_id
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("tariffId", tariffId)
            .bind("type", type.name)
            .map { row, _ ->
                TariffConstraint(
                    id = row.get("tc_id", UUID::class.java),
                    version = row.get("tc_version", Int::class.java),
                    versionCreatedAt = row.get("tc_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("tc_version_created_by", UUID::class.java),
                    tariffId = row.get("t_id", UUID::class.java),
                    type = row.get("tc_type", String::class.java)?.let { TariffConstraintType.valueOf(it) },
                    baseRule = row.get("tc_base_rule", String::class.java)?.let { BaseRule.valueOf(it) },
                    tags = row.get("tags", String::class.java)
                )
            }
            .all()
    }

    /**
     * Найти ограничение по ID последней версии
     */
    fun findByIdLatestVersion(id: UUID): Mono<TariffConstraint> {
        val sql = """
            SELECT tc.*
            FROM tariff_constraint tc
            WHERE tc.tc_id = :id
            AND tc.tc_version = (
                SELECT MAX(tc2.tc_version) 
                FROM tariff_constraint tc2 
                WHERE tc2.tc_id = :id
            )
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("id", id)
            .map { row, _ ->
                TariffConstraint(
                    id = row.get("tc_id", UUID::class.java),
                    version = row.get("tc_version", Int::class.java),
                    versionCreatedAt = row.get("tc_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("tc_version_created_by", UUID::class.java),
                    tariffId = row.get("t_id", UUID::class.java),
                    type = row.get("tc_type", String::class.java)?.let { TariffConstraintType.valueOf(it) },
                    baseRule = row.get("tc_base_rule", String::class.java)?.let { BaseRule.valueOf(it) },
                    tags = row.get("tags", String::class.java)
                )
            }
            .one()
    }

    /**
     * Найти максимальную версию по ID
     */
    fun findMaxVersionById(id: UUID): Mono<Int?> {
        val sql = "SELECT MAX(tc_version) FROM tariff_constraint WHERE tc_id = :id"
        return template.databaseClient
            .sql(sql)
            .bind("id", id)
            .map { row, _ -> row.get(0, Int::class.java) }
            .one()
    }

    /**
     * Сохранить ограничение тарифа
     */
    fun save(tariffConstraint: TariffConstraint): Mono<TariffConstraint> {
        return template.insert(TariffConstraint::class.java)
            .using(tariffConstraint)
    }

    /**
     * Удалить ограничение тарифа (логическое удаление)
     */
    fun delete(id: UUID): Mono<Void> {
        return findByIdLatestVersion(id)
            .flatMap { existing ->
                findMaxVersionById(id)
                    .flatMap { maxVersion ->
                        val deleted = existing.copy(
                            version = (maxVersion ?: 0) + 1,
                            versionCreatedAt = java.sql.Timestamp(System.currentTimeMillis()),
                            tags = "DELETED"
                        )
                        save(deleted).then()
                    }
            }
    }
} 