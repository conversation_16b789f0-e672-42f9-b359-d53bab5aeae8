package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.StationService
import ru.sbertroika.tkp3.pro.controller.model.StationDto
import ru.sbertroika.tkp3.pro.model.Station
import java.util.*

@RestController
@RequestMapping("/api/v1/pro")
class StationController(
    private val stationService: StationService
) {

    @GetMapping("/stations")
    fun getAllStations(): Flux<ApiResponse<StationDto>> {
        return stationService.getAllStations()
            .map { station ->
                ApiResponse.success(station)
            }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<StationDto>("Ошибка получения станций: ${error.message}"))
            }
    }

    @GetMapping("/stations/{id}")
    fun getStationById(@PathVariable id: String): Mono<ApiResponse<StationDto>> {
        return stationService.getStationById(UUID.fromString(id))
            .map { station ->
                ApiResponse.success(station)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<StationDto>("Ошибка получения станции: ${error.message}"))
            }
    }

    @GetMapping("/projects/{projectId}/stations")
    fun getStationsByProject(
        @PathVariable projectId: String,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(required = false) name: String?,
        @RequestParam(required = false) city: String?,
        @RequestParam(required = false) region: String?,
        @RequestParam(required = false) country: String?
    ): Mono<ApiResponse<Map<String, Any>>> {
        return stationService.getStationsByProject(
            UUID.fromString(projectId), page, size, name, city, region, country
        )
            .map { result ->
                ApiResponse.success(result)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения станций проекта: ${error.message}"))
            }
    }

    @PostMapping("/stations/batch")
    fun getStationsByIds(@RequestBody request: Map<String, List<String>>): Flux<ApiResponse<StationDto>> {
        val stationIds = request["stationIds"]?.map { UUID.fromString(it) } ?: emptyList()
        return stationService.getStationsByIds(stationIds)
            .map { station ->
                ApiResponse.success(station)
            }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<StationDto>("Ошибка получения станций по ID: ${error.message}"))
            }
    }

    @PostMapping("/projects/{projectId}/stations")
    fun createStation(@PathVariable projectId: String, @RequestBody station: Station): Mono<ApiResponse<Station>> {
        return stationService.createStation(UUID.fromString(projectId), station)
            .map { createdStation ->
                ApiResponse.success(createdStation, "Станция успешно создана")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Station>("Ошибка создания станции: ${error.message}"))
            }
    }

    @PutMapping("/stations/{id}")
    fun updateStation(@PathVariable id: String, @RequestBody station: Station): Mono<ApiResponse<Station>> {
        return stationService.updateStation(UUID.fromString(id), station)
            .map { updatedStation ->
                ApiResponse.success(updatedStation, "Станция успешно обновлена")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Station>("Ошибка обновления станции: ${error.message}"))
            }
    }

    @DeleteMapping("/stations/{id}")
    fun deleteStation(@PathVariable id: String): Mono<ApiResponse<String>> {
        return stationService.deleteStation(UUID.fromString(id))
            .map {
                ApiResponse.success("Станция успешно удалена")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка удаления станции: ${error.message}"))
            }
    }

    @PostMapping("/stations/{id}/sync")
    fun syncStationWithContract(@PathVariable id: String): Mono<ApiResponse<Station>> {
        return stationService.syncStationWithContract(UUID.fromString(id))
            .map { station ->
                ApiResponse.success(station, "Станция синхронизирована с договором")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Station>("Ошибка синхронизации станции: ${error.message}"))
            }
    }
} 