package ru.sbertroika.tkp3.pro.controller.output.model

import org.springframework.data.relational.core.mapping.Column
import ru.sbertroika.tkp3.pro.model.PayMethodType
import java.sql.Timestamp
import java.util.*

data class ProductDictRowView(
    @Column("pdr_id")
    var id: UUID? = null,

    @Column("pdr_version")
    var version: Int? = null,

    @Column("pdr_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("pdr_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("pdr_project_id")
    var projectId: UUID? = null,

    @Column("p_id")
    var productId: UUID? = null,

    @Column("t_id")
    var tariffId: UUID? = null,

    @Column("pdr_method_type")
    var paymentMethodType: PayMethodType? = null,

    @Column("pdr_fix_price")
    var isFixPrice: Boolean? = null,

    @Column("pdr_price")
    var price: Long? = null,

    @Column("tags")
    var tags: String? = null,

    // Дополнительные поля для JOIN запросов
    var productName: String? = null,

    var tariffName: String? = null
)