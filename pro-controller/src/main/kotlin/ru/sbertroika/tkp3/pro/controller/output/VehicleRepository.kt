package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.Vehicle
import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.VehicleType
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.*

@Repository
interface VehicleRepository : ReactiveCrudRepository<Vehicle, UUID> {

    /**
     * Получить все транспортные средства с фильтрацией и пагинацией
     */
    @Query("""
        SELECT * FROM vehicle 
        WHERE v_project_id = :projectId 
        AND vh_status != 'IS_DELETED'
        AND (:type IS NULL OR vh_type = :type)
        AND (:status IS NULL OR vh_status = :status)
        AND (:organizationId IS NULL OR v_organization_id = :organizationId)
        AND (:search IS NULL OR vh_number ILIKE CONCAT('%', :search, '%'))
        ORDER BY vh_number
        LIMIT :size OFFSET :offset
    """)
    fun findAllVehiclesWithFilters(
        projectId: UUID,
        type: VehicleType? = null,
        status: VehicleStatus? = null,
        organizationId: UUID? = null,
        search: String? = null,
        size: Int,
        offset: Int
    ): Flux<Vehicle>

    /**
     * Подсчитать количество транспортных средств с фильтрацией
     */
    @Query("""
        SELECT COUNT(*) FROM vehicle 
        WHERE v_project_id = :projectId 
        AND vh_status != 'IS_DELETED'
        AND (:type IS NULL OR vh_type = :type)
        AND (:status IS NULL OR vh_status = :status)
        AND (:organizationId IS NULL OR v_organization_id = :organizationId)
        AND (:search IS NULL OR vh_number ILIKE CONCAT('%', :search, '%'))
    """)
    fun countVehiclesWithFilters(
        projectId: UUID,
        type: VehicleType? = null,
        status: VehicleStatus? = null,
        organizationId: UUID? = null,
        search: String? = null
    ): Mono<Long>

    /**
     * Получить транспортное средство по ID (последняя версия)
     */
    @Query("""
        SELECT * FROM vehicle 
        WHERE vh_id = :vehicleId 
        AND vh_status != 'IS_DELETED'
        ORDER BY vh_version DESC 
        LIMIT 1
    """)
    fun findVehicleById(vehicleId: UUID): Mono<Vehicle>

    /**
     * Получить все версии транспортного средства
     */
    @Query("""
        SELECT * FROM vehicle 
        WHERE vh_id = :vehicleId 
        ORDER BY vh_version DESC
    """)
    fun findAllVersionsByVehicleId(vehicleId: UUID): Flux<Vehicle>

    /**
     * Получить транспортные средства по проекту
     */
    @Query("""
        SELECT * FROM vehicle 
        WHERE v_project_id = :projectId 
        AND vh_status != 'IS_DELETED'
        ORDER BY vh_number
    """)
    fun findByProjectId(projectId: UUID): Flux<Vehicle>

    /**
     * Получить транспортные средства по организации
     */
    @Query("""
        SELECT * FROM vehicle 
        WHERE v_organization_id = :organizationId 
        AND vh_status != 'IS_DELETED'
        ORDER BY vh_number
    """)
    fun findByOrganizationId(organizationId: UUID): Flux<Vehicle>

    /**
     * Получить транспортные средства по типу
     */
    @Query("""
        SELECT * FROM vehicle 
        WHERE vh_type = :type 
        AND vh_status != 'IS_DELETED'
        ORDER BY vh_number
    """)
    fun findByType(type: VehicleType): Flux<Vehicle>

    /**
     * Получить транспортные средства по статусу
     */
    @Query("""
        SELECT * FROM vehicle 
        WHERE vh_status = :status 
        ORDER BY vh_number
    """)
    fun findByStatus(status: VehicleStatus): Flux<Vehicle>

    /**
     * Проверить существование транспортного средства по номеру в проекте
     */
    @Query("""
        SELECT EXISTS(
            SELECT 1 FROM vehicle 
            WHERE v_project_id = :projectId 
            AND vh_number = :number 
            AND vh_status != 'IS_DELETED'
        )
    """)
    fun existsByProjectIdAndNumber(projectId: UUID, number: String): Mono<Boolean>
} 