package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.City
import reactor.core.publisher.Flux
import java.util.*

@Repository
interface CityRepository : ReactiveCrudRepository<City, UUID> {

    @Query("SELECT * FROM city WHERE city_name ILIKE :name || '%' ORDER BY city_name LIMIT 10")
    fun findByNameStartingWithIgnoreCase(name: String): Flux<City>

    @Query("SELECT * FROM city WHERE city_region_id = :regionId ORDER BY city_name")
    fun findByRegionId(regionId: UUID): Flux<City>

    @Query("SELECT * FROM city WHERE city_region_id = :regionId AND city_name ILIKE :name || '%' ORDER BY city_name LIMIT 10")
    fun findByRegionIdAndNameStartingWithIgnoreCase(regionId: UUID, name: String): Flux<City>
} 