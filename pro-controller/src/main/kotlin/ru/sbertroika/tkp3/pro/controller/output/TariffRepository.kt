package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.Tariff
import java.util.*

@Repository
interface TariffRepository : ReactiveCrudRepository<Tariff, UUID> {

    /**
     * Получить тарифы по проекту (только последние версии, исключая удаленные)
     */
    @Query("""
        WITH latest_versions AS (
            SELECT t_id, MAX(t_version) as max_version
            FROM tariff
            GROUP BY t_id
        )
        SELECT t.* FROM tariff t
        INNER JOIN latest_versions lv ON t.t_id = lv.t_id AND t.t_version = lv.max_version
        WHERE t.t_project_id = :projectId 
        AND t.t_status != 'IS_DELETED'
        ORDER BY t.t_version_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findByProjectIdLatestVersions(
        projectId: UUID,
        limit: Int,
        offset: Int
    ): Flux<Tariff>

    /**
     * Получить количество тарифов по проекту (только последние версии, исключая удаленные)
     */
    @Query("""
        WITH latest_versions AS (
            SELECT t_id, MAX(t_version) as max_version
            FROM tariff
            GROUP BY t_id
        )
        SELECT COUNT(*) FROM tariff t
        INNER JOIN latest_versions lv ON t.t_id = lv.t_id AND t.t_version = lv.max_version
        WHERE t.t_project_id = :projectId 
        AND t.t_status != 'IS_DELETED'
    """)
    fun countByProjectIdLatestVersions(projectId: UUID): Mono<Long>

    /**
     * Получить все тарифы (только последние версии, исключая удаленные)
     */
    @Query("""
        WITH latest_versions AS (
            SELECT t_id, MAX(t_version) as max_version
            FROM tariff
            GROUP BY t_id
        )
        SELECT t.* FROM tariff t
        INNER JOIN latest_versions lv ON t.t_id = lv.t_id AND t.t_version = lv.max_version
        WHERE t.t_status != 'IS_DELETED'
        ORDER BY t.t_version_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findAllLatestVersions(limit: Int, offset: Int): Flux<Tariff>

    /**
     * Получить количество всех тарифов (только последние версии, исключая удаленные)
     */
    @Query("""
        WITH latest_versions AS (
            SELECT t_id, MAX(t_version) as max_version
            FROM tariff
            GROUP BY t_id
        )
        SELECT COUNT(*) FROM tariff t
        INNER JOIN latest_versions lv ON t.t_id = lv.t_id AND t.t_version = lv.max_version
        WHERE t.t_status != 'IS_DELETED'
    """)
    fun countAllLatestVersions(): Mono<Long>

    /**
     * Получить тариф по ID (последняя версия)
     */
    @Query("""
        SELECT * FROM tariff
        WHERE t_id = :id
        ORDER BY t_version DESC
        LIMIT 1
    """)
    fun findByIdLatestVersion(id: UUID): Mono<Tariff>

    /**
     * Получить максимальную версию для тарифа
     */
    @Query("SELECT MAX(t_version) FROM tariff WHERE t_id = :id")
    fun findMaxVersionById(id: UUID): Mono<Int>

    /**
     * Получить все версии тарифа по ID
     */
    @Query("SELECT * FROM tariff WHERE t_id = :id ORDER BY t_version DESC")
    fun findAllVersionsById(id: UUID): Flux<Tariff>
} 