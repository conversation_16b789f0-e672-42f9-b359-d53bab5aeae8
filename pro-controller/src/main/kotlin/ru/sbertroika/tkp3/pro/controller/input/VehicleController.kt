package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.VehicleService
import ru.sbertroika.tkp3.pro.model.Vehicle
import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.VehicleType
import java.util.*

@RestController
@RequestMapping("/api/v1/pro/vehicles")
class VehicleController(
    private val vehicleService: VehicleService
) {

    /**
     * Получить все транспортные средства с фильтрацией и пагинацией
     */
    @GetMapping
    fun getVehicles(
        @RequestParam projectId: UUID,
        @RequestParam(required = false) type: VehicleType? = null,
        @RequestParam(required = false) status: VehicleStatus? = null,
        @RequestParam(required = false) organizationId: UUID? = null,
        @RequestParam(required = false) search: String? = null,
        @RequestParam(defaultValue = "0") page: Int = 0,
        @RequestParam(defaultValue = "10") size: Int = 10
    ): Mono<ResponseEntity<ApiResponse<Map<String, Any>>>> {
        return vehicleService.getVehiclesWithFilters(
            projectId = projectId,
            type = type,
            status = status,
            organizationId = organizationId,
            search = search,
            page = page,
            size = size
        ).map { result ->
            ResponseEntity.ok(ApiResponse(
                success = true,
                data = result,
                message = "Транспортные средства получены успешно"
            ))
        }.onErrorResume { error ->
            Mono.just(ResponseEntity.badRequest().body(ApiResponse<Map<String, Any>>(
                success = false,
                message = "Ошибка получения транспортных средств: ${error.message}"
            )))
        }
    }

    /**
     * Получить транспортное средство по ID
     */
    @GetMapping("/{id}")
    fun getVehicleById(@PathVariable id: UUID): Mono<ResponseEntity<ApiResponse<Vehicle>>> {
        return vehicleService.getVehicleById(id)
            .map { vehicle ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicle,
                    message = "Транспортное средство получено успешно"
                ))
            }.onErrorResume { error ->
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<Vehicle>(
                    success = false,
                    message = "Ошибка получения транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Получить все версии транспортного средства
     */
    @GetMapping("/{id}/versions")
    fun getVehicleVersions(@PathVariable id: UUID): Mono<ResponseEntity<ApiResponse<List<Vehicle>>>> {
        return vehicleService.getAllVersionsByVehicleId(id)
            .collectList()
            .map { versions ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = versions,
                    message = "Версии транспортного средства получены успешно"
                ))
            }.onErrorResume { error ->
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<Vehicle>>(
                    success = false,
                    message = "Ошибка получения версий транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Создать новое транспортное средство
     */
    @PostMapping
    fun createVehicle(
        @RequestBody vehicle: Vehicle,
        @RequestParam createdBy: UUID
    ): Mono<ResponseEntity<ApiResponse<Vehicle>>> {
        return vehicleService.createVehicle(vehicle, createdBy)
            .map { createdVehicle ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = createdVehicle,
                    message = "Транспортное средство создано успешно"
                ))
            }.onErrorResume { error ->
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<Vehicle>(
                    success = false,
                    message = "Ошибка создания транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Обновить транспортное средство
     */
    @PutMapping("/{id}")
    fun updateVehicle(
        @PathVariable id: UUID,
        @RequestBody vehicle: Vehicle,
        @RequestParam updatedBy: UUID
    ): Mono<ResponseEntity<ApiResponse<Vehicle>>> {
        return vehicleService.updateVehicle(id, vehicle, updatedBy)
            .map { updatedVehicle ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = updatedVehicle,
                    message = "Транспортное средство обновлено успешно"
                ))
            }.onErrorResume { error ->
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<Vehicle>(
                    success = false,
                    message = "Ошибка обновления транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Удалить транспортное средство (логическое удаление)
     */
    @DeleteMapping("/{id}")
    fun deleteVehicle(
        @PathVariable id: UUID,
        @RequestParam deletedBy: UUID
    ): Mono<ResponseEntity<ApiResponse<Vehicle>>> {
        return vehicleService.deleteVehicle(id, deletedBy)
            .map { deletedVehicle ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = deletedVehicle,
                    message = "Транспортное средство удалено успешно"
                ))
            }.onErrorResume { error ->
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<Vehicle>(
                    success = false,
                    message = "Ошибка удаления транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Получить транспортные средства по проекту
     */
    @GetMapping("/project/{projectId}")
    fun getVehiclesByProject(@PathVariable projectId: UUID): Mono<ResponseEntity<ApiResponse<List<Vehicle>>>> {
        return vehicleService.getVehiclesByProject(projectId)
            .collectList()
            .map { vehicles ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicles,
                    message = "Транспортные средства проекта получены успешно"
                ))
            }.onErrorResume { error ->
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<Vehicle>>(
                    success = false,
                    message = "Ошибка получения транспортных средств проекта: ${error.message}"
                )))
            }
    }

    /**
     * Получить транспортные средства по организации
     */
    @GetMapping("/organization/{organizationId}")
    fun getVehiclesByOrganization(@PathVariable organizationId: UUID): Mono<ResponseEntity<ApiResponse<List<Vehicle>>>> {
        return vehicleService.getVehiclesByOrganization(organizationId)
            .collectList()
            .map { vehicles ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicles,
                    message = "Транспортные средства организации получены успешно"
                ))
            }.onErrorResume { error ->
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<Vehicle>>(
                    success = false,
                    message = "Ошибка получения транспортных средств организации: ${error.message}"
                )))
            }
    }

    /**
     * Получить транспортные средства по типу
     */
    @GetMapping("/type/{type}")
    fun getVehiclesByType(@PathVariable type: VehicleType): Mono<ResponseEntity<ApiResponse<List<Vehicle>>>> {
        return vehicleService.getVehiclesByType(type)
            .collectList()
            .map { vehicles ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicles,
                    message = "Транспортные средства по типу получены успешно"
                ))
            }.onErrorResume { error ->
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<Vehicle>>(
                    success = false,
                    message = "Ошибка получения транспортных средств по типу: ${error.message}"
                )))
            }
    }

    /**
     * Получить транспортные средства по статусу
     */
    @GetMapping("/status/{status}")
    fun getVehiclesByStatus(@PathVariable status: VehicleStatus): Mono<ResponseEntity<ApiResponse<List<Vehicle>>>> {
        return vehicleService.getVehiclesByStatus(status)
            .collectList()
            .map { vehicles ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicles,
                    message = "Транспортные средства по статусу получены успешно"
                ))
            }.onErrorResume { error ->
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<Vehicle>>(
                    success = false,
                    message = "Ошибка получения транспортных средств по статусу: ${error.message}"
                )))
            }
    }
} 