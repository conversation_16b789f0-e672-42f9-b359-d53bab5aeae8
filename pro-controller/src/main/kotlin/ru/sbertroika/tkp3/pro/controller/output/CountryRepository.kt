package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.Country
import reactor.core.publisher.Flux
import java.util.*

@Repository
interface CountryRepository : ReactiveCrudRepository<Country, UUID> {

    @Query("SELECT * FROM country WHERE c_name ILIKE :name || '%' ORDER BY c_name LIMIT 10")
    fun findByNameStartingWithIgnoreCase(name: String): Flux<Country>

    @Query("SELECT * FROM country ORDER BY c_name")
    fun findAllOrderByName(): Flux<Country>
} 