package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.RouteRepository
import ru.sbertroika.tkp3.pro.controller.output.RouteStationRepository
import ru.sbertroika.tkp3.pro.controller.output.RouteStationInfoRepository
import ru.sbertroika.tkp3.pro.model.Route
import ru.sbertroika.tkp3.pro.model.RouteStation
import ru.sbertroika.tkp3.pro.controller.model.RouteStationInfo
import ru.sbertroika.tkp3.pro.model.RouteStatus
import ru.sbertroika.tkp3.pro.model.RouteScheme
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Service
class RouteService(
    private val routeRepository: RouteRepository,
    private val routeStationRepository: RouteStationRepository,
    private val routeStationInfoRepository: RouteStationInfoRepository
) {

    /**
     * Получить все маршруты с фильтрацией и пагинацией
     */
    fun getAllRoutes(
        projectId: UUID?,
        status: String?,
        scheme: String?,
        page: Int,
        size: Int
    ): Flux<Route> {
        val offset = page * size
        return routeRepository.findAllRoutesWithFilters(
            projectId,
            status,
            scheme,
            size,
            offset
        )
    }

    /**
     * Получить общее количество маршрутов с фильтрацией
     */
    fun getRoutesCount(
        projectId: UUID?,
        status: String?,
        scheme: String?
    ): Mono<Long> {
        return routeRepository.countRoutesWithFilters(
            projectId,
            status,
            scheme
        ).next()
    }

    /**
     * Получить маршрут по ID
     */
    fun getRouteById(id: UUID): Mono<Route> {
        return routeRepository.findRouteById(id)
    }

    /**
     * Получить все версии маршрута по ID
     */
    fun getAllVersionsByRouteId(id: UUID): Flux<Route> {
        return routeRepository.findAllVersionsByRouteId(id)
    }

    /**
     * Получить маршруты по проекту
     */
    fun getRoutesByProjectId(projectId: UUID): Flux<Route> {
        return routeRepository.findByProjectId(projectId)
    }

    /**
     * Получить маршруты по статусу
     */
    fun getRoutesByStatus(status: String): Flux<Route> {
        return routeRepository.findByStatus(status)
    }

    /**
     * Получить маршруты по схеме
     */
    fun getRoutesByScheme(scheme: String): Flux<Route> {
        return routeRepository.findByScheme(scheme)
    }

    /**
     * Создать новый маршрут
     */
    fun createRoute(route: Route): Mono<Route> {
        val now = Timestamp.valueOf(LocalDateTime.now())
        route.id = UUID.randomUUID()
        route.version = 1
        route.versionCreatedAt = now
        route.versionCreatedBy = UUID.randomUUID() // TODO: получить из контекста безопасности
        route.activeFrom = now
        
        // Устанавливаем статус по умолчанию
        if (route.status == null) {
            route.status = RouteStatus.ACTIVE
        }
        
        // Устанавливаем схему по умолчанию
        if (route.scheme == null) {
            route.scheme = RouteScheme.DIRECTIONAL
        }
        
        // Получаем максимальный индекс для проекта и устанавливаем следующий
        return routeRepository.findMaxIndexByProjectId(route.projectId!!)
            .next()
            .flatMap { maxIndex ->
                route.index = maxIndex + 1
                routeRepository.save(route)
            }
    }

    /**
     * Обновить маршрут (создает новую версию)
     */
    fun updateRoute(id: UUID, updatedRoute: Route): Mono<Route> {
        return routeRepository.findRouteById(id)
            .flatMap { existingRoute ->
                val now = Timestamp.valueOf(LocalDateTime.now())
                updatedRoute.id = id
                updatedRoute.version = (existingRoute.version ?: 0) + 1
                updatedRoute.versionCreatedAt = now
                updatedRoute.versionCreatedBy = UUID.randomUUID() // TODO: получить из контекста безопасности
                updatedRoute.activeFrom = now
                updatedRoute.index = existingRoute.index // Сохраняем индекс
                
                routeRepository.save(updatedRoute)
            }
    }

    /**
     * Удалить маршрут (логическое удаление)
     */
    fun deleteRoute(id: UUID): Mono<Void> {
        return routeRepository.findRouteById(id)
            .flatMap { route ->
                route.status = RouteStatus.IS_DELETED
                routeRepository.save(route)
            }
            .then()
    }

    /**
     * Активировать маршрут
     */
    fun activateRoute(id: UUID): Mono<Route> {
        return routeRepository.findRouteById(id)
            .flatMap { route ->
                route.status = RouteStatus.ACTIVE
                routeRepository.save(route)
            }
    }

    /**
     * Деактивировать маршрут
     */
    fun deactivateRoute(id: UUID): Mono<Route> {
        return routeRepository.findRouteById(id)
            .flatMap { route ->
                route.status = RouteStatus.DISABLED
                routeRepository.save(route)
            }
    }

    /**
     * Заблокировать маршрут
     */
    fun blockRoute(id: UUID): Mono<Route> {
        return routeRepository.findRouteById(id)
            .flatMap { route ->
                route.status = RouteStatus.BLOCKED
                routeRepository.save(route)
            }
    }

    /**
     * Проверить уникальность номера маршрута в проекте
     */
    fun isRouteNumberUnique(projectId: UUID, number: String, excludeId: UUID? = null): Mono<Boolean> {
        return routeRepository.existsByProjectIdAndNumber(projectId, number, excludeId)
            .next()
            .map { !it }
    }

    /**
     * Получить станции маршрута
     */
    fun getRouteStations(routeId: UUID): Flux<RouteStation> {
        return routeStationRepository.findStationsByRouteId(routeId)
    }

    /**
     * Получить информацию о станциях маршрута с данными из справочника станций
     */
    fun getRouteStationInfo(routeId: UUID): Flux<RouteStationInfo> {
        return routeStationInfoRepository.findRouteStationInfoByRouteId(routeId)
    }

    /**
     * Получить информацию о станциях для нескольких маршрутов
     */
    fun getRouteStationInfoByRouteIds(routeIds: List<UUID>): Flux<RouteStationInfo> {
        return routeStationInfoRepository.findRouteStationInfoByRouteIds(routeIds)
    }

    /**
     * Получить информацию о конкретной станции маршрута
     */
    fun getRouteStationInfoById(routeStationId: UUID): Flux<RouteStationInfo> {
        return routeStationInfoRepository.findRouteStationInfoById(routeStationId)
    }

    /**
     * Добавить станцию к маршруту
     */
    fun addStationToRoute(routeId: UUID, stationId: UUID): Mono<RouteStation> {
        val now = Timestamp.valueOf(LocalDateTime.now())
        
        return routeStationRepository.findMaxPositionByRouteId(routeId)
            .next()
            .flatMap { maxPosition ->
                val routeStation = RouteStation(
                    id = UUID.randomUUID(),
                    version = 1,
                    versionCreatedAt = now,
                    versionCreatedBy = UUID.randomUUID(), // TODO: получить из контекста безопасности
                    activeFrom = now,
                    routeId = routeId,
                    stationId = stationId,
                    position = maxPosition + 1,
                    isDelete = false
                )
                
                routeStationRepository.save(routeStation)
            }
    }

    /**
     * Удалить станцию из маршрута
     */
    fun removeStationFromRoute(routeStationId: UUID): Mono<Void> {
        return routeStationRepository.findRouteStationById(routeStationId)
            .flatMap { routeStation ->
                routeStation.isDelete = true
                routeStationRepository.save(routeStation)
            }
            .then()
    }

    /**
     * Обновить позиции станций в маршруте
     */
    fun updateStationPositions(routeId: UUID, stationPositions: Map<UUID, Int>): Mono<Void> {
        return Flux.fromIterable(stationPositions.entries)
            .flatMap { entry ->
                routeStationRepository.findRouteStationById(entry.key)
                    .flatMap { routeStation ->
                        routeStation.position = entry.value
                        routeStationRepository.save(routeStation)
                    }
            }
            .then()
    }
} 