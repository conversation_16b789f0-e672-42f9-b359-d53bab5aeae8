package ru.sbertroika.tkp3.pro.controller.model

import org.springframework.data.relational.core.mapping.Column
import java.sql.Timestamp
import java.util.UUID

/**
 * Модель для передачи данных о станции маршрута
 * Содержит информацию из route_station + station
 */
data class RouteStationInfo(
    /**
     * ID записи route_station
     */
    @Column("route_station_id")
    val routeStationId: UUID? = null,

    /**
     * ID маршрута
     */
    @Column("route_id")
    val routeId: UUID? = null,

    /**
     * ID станции
     */
    @Column("station_id")
    val stationId: UUID? = null,

    /**
     * Позиция станции в маршруте
     */
    @Column("position")
    val position: Int? = null,

    /**
     * Название станции (из справочника station)
     */
    @Column("station_name")
    val stationName: String? = null,

    /**
     * Флаг удаления станции из маршрута
     */
    @Column("is_deleted")
    val isDeleted: Boolean? = null,

    /**
     * Версия записи route_station
     */
    @Column("version")
    val version: Int? = null,

    /**
     * Дата создания версии
     */
    @Column("version_created_at")
    val versionCreatedAt: Timestamp? = null,

    /**
     * Кто создал версию
     */
    @Column("version_created_by")
    val versionCreatedBy: UUID? = null,

    /**
     * Дополнительные теги
     */
    @Column("tags")
    val tags: String? = null
)