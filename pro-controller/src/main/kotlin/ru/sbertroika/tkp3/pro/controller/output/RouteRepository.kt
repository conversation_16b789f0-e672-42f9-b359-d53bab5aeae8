package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.Route
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.*

@Repository
interface RouteRepository : ReactiveCrudRepository<Route, ru.sbertroika.tkp3.pro.model.RoutePK> {

    /**
     * Получить все маршруты с фильтрацией и пагинацией
     */
    @Query("""
        SELECT * FROM route 
        WHERE r_status != 'IS_DELETED'
        AND (:projectId IS NULL OR r_project_id = :projectId)
        AND (:status IS NULL OR r_status = :status)
        AND (:scheme IS NULL OR r_scheme = :scheme)
        ORDER BY r_project_id, r_index, r_version_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findAllRoutesWithFilters(
        projectId: UUID?,
        status: String?,
        scheme: String?,
        limit: Int,
        offset: Int
    ): Flux<Route>

    /**
     * Получить общее количество маршрутов с фильтрацией
     */
    @Query("""
        SELECT COUNT(*) FROM route 
        WHERE r_status != 'IS_DELETED'
        AND (:projectId IS NULL OR r_project_id = :projectId)
        AND (:status IS NULL OR r_status = :status)
        AND (:scheme IS NULL OR r_scheme = :scheme)
    """)
    fun countRoutesWithFilters(
        projectId: UUID?,
        status: String?,
        scheme: String?
    ): Flux<Long>

    /**
     * Получить маршрут по ID (последняя версия)
     */
    @Query("""
        SELECT * FROM route 
        WHERE r_id = :id 
        AND r_status != 'IS_DELETED'
        ORDER BY r_version DESC 
        LIMIT 1
    """)
    fun findRouteById(id: UUID): Mono<Route>

    /**
     * Получить все версии маршрута по ID
     */
    @Query("""
        SELECT * FROM route 
        WHERE r_id = :id 
        ORDER BY r_version DESC
    """)
    fun findAllVersionsByRouteId(id: UUID): Flux<Route>

    /**
     * Получить маршруты по проекту
     */
    @Query("""
        SELECT * FROM route 
        WHERE r_project_id = :projectId 
        AND r_status != 'IS_DELETED'
        ORDER BY r_index, r_version_created_at DESC
    """)
    fun findByProjectId(projectId: UUID): Flux<Route>

    /**
     * Получить маршруты по статусу
     */
    @Query("""
        SELECT * FROM route 
        WHERE r_status = :status 
        ORDER BY r_project_id, r_index, r_version_created_at DESC
    """)
    fun findByStatus(status: String): Flux<Route>

    /**
     * Получить маршруты по схеме
     */
    @Query("""
        SELECT * FROM route 
        WHERE r_scheme = :scheme 
        AND r_status != 'IS_DELETED'
        ORDER BY r_project_id, r_index, r_version_created_at DESC
    """)
    fun findByScheme(scheme: String): Flux<Route>

    /**
     * Получить максимальный индекс маршрута в проекте
     */
    @Query("""
        SELECT COALESCE(MAX(r_index), 0) FROM route 
        WHERE r_project_id = :projectId 
        AND r_status != 'IS_DELETED'
    """)
    fun findMaxIndexByProjectId(projectId: UUID): Flux<Int>

    /**
     * Проверить существование маршрута с номером в проекте
     */
    @Query("""
        SELECT COUNT(*) > 0 FROM route 
        WHERE r_project_id = :projectId 
        AND r_number = :number 
        AND r_status != 'IS_DELETED'
        AND r_id != :excludeId
    """)
    fun existsByProjectIdAndNumber(
        projectId: UUID,
        number: String,
        excludeId: UUID? = null
    ): Flux<Boolean>
} 