package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.controller.model.StationDto
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.Project
import java.util.*

@Repository
interface StationDisplayRepository : ReactiveCrudRepository<StationDto, UUID> {

    @Query("""
        SELECT 
            s.st_id as id,
            s.st_version as version,
            s.st_version_created_at as versionCreatedAt,
            s.st_version_created_by as versionCreatedBy,
            s.p_id as projectId,
            s.tz_id as tariffZoneId,
            s.st_name as name,
            s.st_latin_name as latinName,
            s.st_status as status,
            s.lat as latitude,
            s.long as longitude,
            s.st_city_id as cityId,
            s.st_district_id as districtId,
            s.st_region_id as regionId,
            s.st_country_id as countryId,
            s.tags,
            s.st_last_sync_date as lastSyncDate,
            s.st_sync_status as syncStatus,
            co.c_name as country,
            r.r_name as region,
            c.city_name as city,
            d.d_name as district,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN true ELSE false END as hasCoordinates,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN s.lat || ', ' || s.long ELSE NULL END as coordinates
        FROM station s
        INNER JOIN (
            SELECT st_id, MAX(st_version) as max_version
            FROM station
            WHERE p_id = :projectId AND st_status != 'IS_DELETED'
            GROUP BY st_id
        ) latest ON s.st_id = latest.st_id AND s.st_version = latest.max_version
        LEFT JOIN country co ON s.st_country_id = co.c_id
        LEFT JOIN region r ON s.st_region_id = r.r_id
        LEFT JOIN city c ON s.st_city_id = c.city_id
        LEFT JOIN district d ON s.st_district_id = d.d_id
        WHERE s.p_id = :projectId 
        AND s.st_status != 'IS_DELETED'
        AND (:name IS NULL OR s.st_name ILIKE '%' || :name || '%')
        AND (:city IS NULL OR c.city_name ILIKE '%' || :city || '%')
        AND (:region IS NULL OR r.r_name ILIKE '%' || :region || '%')
        AND (:country IS NULL OR co.c_name ILIKE '%' || :country || '%')
        ORDER BY s.st_version_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findByProjectIdWithFilters(
        @Param("projectId") projectId: UUID,
        @Param("name") name: String? = null,
        @Param("city") city: String? = null,
        @Param("region") region: String? = null,
        @Param("country") country: String? = null,
        @Param("limit") limit: Int,
        @Param("offset") offset: Int
    ): Flux<StationDto>

    @Query("""
        SELECT 
            s.st_id as id,
            s.st_version as version,
            s.st_version_created_at as versionCreatedAt,
            s.st_version_created_by as versionCreatedBy,
            s.p_id as projectId,
            s.tz_id as tariffZoneId,
            s.st_name as name,
            s.st_latin_name as latinName,
            s.st_status as status,
            s.lat as latitude,
            s.long as longitude,
            s.st_city_id as cityId,
            s.st_district_id as districtId,
            s.st_region_id as regionId,
            s.st_country_id as countryId,
            s.tags,
            s.st_last_sync_date as lastSyncDate,
            s.st_sync_status as syncStatus,
            co.c_name as country,
            r.r_name as region,
            c.city_name as city,
            d.d_name as district,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN true ELSE false END as hasCoordinates,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN s.lat || ', ' || s.long ELSE NULL END as coordinates
        FROM station s
        LEFT JOIN country co ON s.st_country_id = co.c_id
        LEFT JOIN region r ON s.st_region_id = r.r_id
        LEFT JOIN city c ON s.st_city_id = c.city_id
        LEFT JOIN district d ON s.st_district_id = d.d_id
        WHERE s.st_id = :id 
        AND s.st_version = (
            SELECT MAX(st_version) 
            FROM station 
            WHERE st_id = :id AND st_status != 'IS_DELETED'
        )
        AND s.st_status != 'IS_DELETED'
    """)
    fun findStationById(@Param("id") id: UUID): Mono<StationDto>

    @Query("""
        SELECT 
            s.st_id as id,
            s.st_version as version,
            s.st_version_created_at as versionCreatedAt,
            s.st_version_created_by as versionCreatedBy,
            s.p_id as projectId,
            s.tz_id as tariffZoneId,
            s.st_name as name,
            s.st_latin_name as latinName,
            s.st_status as status,
            s.lat as latitude,
            s.long as longitude,
            s.st_city_id as cityId,
            s.st_district_id as districtId,
            s.st_region_id as regionId,
            s.st_country_id as countryId,
            s.tags,
            s.st_last_sync_date as lastSyncDate,
            s.st_sync_status as syncStatus,
            co.c_name as country,
            r.r_name as region,
            c.city_name as city,
            d.d_name as district,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN true ELSE false END as hasCoordinates,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN s.lat || ', ' || s.long ELSE NULL END as coordinates
        FROM station s
        INNER JOIN (
            SELECT st_id, MAX(st_version) as max_version
            FROM station
            WHERE st_id = ANY(:ids) AND st_status != 'IS_DELETED'
            GROUP BY st_id
        ) latest ON s.st_id = latest.st_id AND s.st_version = latest.max_version
        LEFT JOIN country co ON s.st_country_id = co.c_id
        LEFT JOIN region r ON s.st_region_id = r.r_id
        LEFT JOIN city c ON s.st_city_id = c.city_id
        LEFT JOIN district d ON s.st_district_id = d.d_id
        WHERE s.st_status != 'IS_DELETED'
    """)
    fun findByIds(@Param("ids") ids: List<UUID>): Flux<StationDto>

    @Query("""
        SELECT 
            s.st_id as id,
            s.st_version as version,
            s.st_version_created_at as versionCreatedAt,
            s.st_version_created_by as versionCreatedBy,
            s.p_id as projectId,
            s.tz_id as tariffZoneId,
            s.st_name as name,
            s.st_latin_name as latinName,
            s.st_status as status,
            s.lat as latitude,
            s.long as longitude,
            s.st_city_id as cityId,
            s.st_district_id as districtId,
            s.st_region_id as regionId,
            s.st_country_id as countryId,
            s.tags,
            s.st_last_sync_date as lastSyncDate,
            s.st_sync_status as syncStatus,
            co.c_name as country,
            r.r_name as region,
            c.city_name as city,
            d.d_name as district,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN true ELSE false END as hasCoordinates,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN s.lat || ', ' || s.long ELSE NULL END as coordinates
        FROM station s
        INNER JOIN (
            SELECT st_id, MAX(st_version) as max_version
            FROM station
            WHERE st_status != 'IS_DELETED'
            GROUP BY st_id
        ) latest ON s.st_id = latest.st_id AND s.st_version = latest.max_version
        LEFT JOIN country co ON s.st_country_id = co.c_id
        LEFT JOIN region r ON s.st_region_id = r.r_id
        LEFT JOIN city c ON s.st_city_id = c.city_id
        LEFT JOIN district d ON s.st_district_id = d.d_id
        WHERE s.st_status != 'IS_DELETED'
        ORDER BY s.st_version_created_at DESC
    """)
    override fun findAll(): Flux<StationDto>
} 