package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.TariffConstraintException
import java.time.LocalDateTime
import java.util.*

@Service
open class TariffConstraintExceptionRepository(
    private val template: R2dbcEntityTemplate
) {

    /**
     * Найти все исключения ограничения тарифа
     */
    fun findByTariffConstraintId(tariffConstraintId: UUID): Flux<TariffConstraintException> {
        val sql = """
            SELECT tce.*
            FROM tariff_constraint_ex tce
            WHERE tce.tc_id = :tariffConstraintId
            AND tce.tce_version = (
                SELECT MAX(tce2.tce_version) 
                FROM tariff_constraint_ex tce2 
                WHERE tce2.tce_id = tce.tce_id
            )
            ORDER BY tce.tce_id
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("tariffConstraintId", tariffConstraintId)
            .map { row, _ ->
                TariffConstraintException(
                    id = row.get("tce_id", UUID::class.java),
                    version = row.get("tce_version", Int::class.java),
                    versionCreatedAt = row.get("tce_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("tce_version_created_by", UUID::class.java),
                    tariffConstraintId = row.get("tc_id", UUID::class.java),
                    exceptionId = row.get("e_id", UUID::class.java),
                    tags = row.get("tags", String::class.java)
                )
            }
            .all()
    }

    /**
     * Найти исключение по ID последней версии
     */
    fun findByIdLatestVersion(id: UUID): Mono<TariffConstraintException> {
        val sql = """
            SELECT tce.*
            FROM tariff_constraint_ex tce
            WHERE tce.tce_id = :id
            AND tce.tce_version = (
                SELECT MAX(tce2.tce_version) 
                FROM tariff_constraint_ex tce2 
                WHERE tce2.tce_id = :id
            )
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("id", id)
            .map { row, _ ->
                TariffConstraintException(
                    id = row.get("tce_id", UUID::class.java),
                    version = row.get("tce_version", Int::class.java),
                    versionCreatedAt = row.get("tce_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("tce_version_created_by", UUID::class.java),
                    tariffConstraintId = row.get("tc_id", UUID::class.java),
                    exceptionId = row.get("e_id", UUID::class.java),
                    tags = row.get("tags", String::class.java)
                )
            }
            .one()
    }

    /**
     * Найти максимальную версию по ID
     */
    fun findMaxVersionById(id: UUID): Mono<Int?> {
        val sql = "SELECT MAX(tce_version) FROM tariff_constraint_ex WHERE tce_id = :id"
        return template.databaseClient
            .sql(sql)
            .bind("id", id)
            .map { row, _ -> row.get(0, Int::class.java) }
            .one()
    }

    /**
     * Сохранить исключение ограничения тарифа
     */
    fun save(tariffConstraintException: TariffConstraintException): Mono<TariffConstraintException> {
        return template.insert(TariffConstraintException::class.java)
            .using(tariffConstraintException)
    }

    /**
     * Удалить исключение ограничения тарифа (логическое удаление)
     */
    fun delete(id: UUID): Mono<Void> {
        return findByIdLatestVersion(id)
            .flatMap { existing ->
                findMaxVersionById(id)
                    .flatMap { maxVersion ->
                        val deleted = existing.copy(
                            version = (maxVersion ?: 0) + 1,
                            versionCreatedAt = java.sql.Timestamp(System.currentTimeMillis()),
                            tags = "DELETED"
                        )
                        save(deleted).then()
                    }
            }
    }

    /**
     * Найти исключения по ID исключения (например, ID маршрута)
     */
    fun findByExceptionId(exceptionId: UUID): Flux<TariffConstraintException> {
        val sql = """
            SELECT tce.*
            FROM tariff_constraint_ex tce
            WHERE tce.e_id = :exceptionId
            AND tce.tce_version = (
                SELECT MAX(tce2.tce_version) 
                FROM tariff_constraint_ex tce2 
                WHERE tce2.tce_id = tce.tce_id
            )
            ORDER BY tce.tce_id
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("exceptionId", exceptionId)
            .map { row, _ ->
                TariffConstraintException(
                    id = row.get("tce_id", UUID::class.java),
                    version = row.get("tce_version", Int::class.java),
                    versionCreatedAt = row.get("tce_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("tce_version_created_by", UUID::class.java),
                    tariffConstraintId = row.get("tc_id", UUID::class.java),
                    exceptionId = row.get("e_id", UUID::class.java),
                    tags = row.get("tags", String::class.java)
                )
            }
            .all()
    }
} 