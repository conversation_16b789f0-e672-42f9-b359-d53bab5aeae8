package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.pro.model.Project
import ru.sbertroika.tkp3.pro.controller.model.ProjectDto
import ru.sbertroika.tkp3.pro.model.OperatorOrganization
import java.util.*

@Component
class ProjectMapper {

    /**
     * Преобразовать Project в ProjectDto
     */
    fun toDto(project: Project): ProjectDto {
        return ProjectDto(
            id = project.id,
            version = project.version,
            versionCreatedAt = project.versionCreatedAt,
            versionCreatedBy = project.versionCreatedBy,
            startDate = project.startDate,
            endDate = project.endDate,
            name = project.name,
            code = project.code,
            projectType = project.projectType,
            status = project.status,
            contractId = project.contractId,
            contractNumber = project.contractNumber,
            contractName = project.contractName,
            description = project.description,
            region = project.region,
            totalBudget = project.totalBudget,
            spentBudget = project.spentBudget,
            progress = project.progress,
            manager = project.manager,
            lastSyncDate = project.lastSyncDate,
            syncStatus = project.syncStatus,
            index = project.index,
            tags = project.tags,
            // Вычисляемые поля заполняются отдельно
            operatorOrganization = null,
            activeTerminals = 0,
            terminals = 0,
            vehicles = 0,
            routes = emptyList(),
            monthlyTransactions = 0,
            monthlyRevenue = 0
        )
    }

    /**
     * Преобразовать ProjectDto в Project
     */
    fun toEntity(dto: ProjectDto): Project {
        return Project(
            id = dto.id,
            version = dto.version,
            versionCreatedAt = dto.versionCreatedAt,
            versionCreatedBy = dto.versionCreatedBy,
            startDate = dto.startDate,
            endDate = dto.endDate,
            name = dto.name,
            code = dto.code,
            projectType = dto.projectType,
            status = dto.status,
            contractId = dto.contractId,
            contractNumber = dto.contractNumber,
            contractName = dto.contractName,
            description = dto.description,
            region = dto.region,
            totalBudget = dto.totalBudget,
            spentBudget = dto.spentBudget,
            progress = dto.progress,
            manager = dto.manager,
            lastSyncDate = dto.lastSyncDate,
            syncStatus = dto.syncStatus,
            index = dto.index,
            tags = dto.tags
        )
    }

    /**
     * Заполнить вычисляемые поля в ProjectDto
     */
    fun fillComputedFields(dto: ProjectDto, operatorOrganization: OperatorOrganization? = null): ProjectDto {
        dto.operatorOrganization = operatorOrganization
        // Здесь можно добавить логику для заполнения других вычисляемых полей
        return dto
    }
} 