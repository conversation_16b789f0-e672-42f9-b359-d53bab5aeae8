package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.*
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.TariffService
import java.util.*

@RestController
@RequestMapping("/api/v1/pro")
class TariffController(
    private val tariffService: TariffService
) {

    @GetMapping("/tariffs")
    fun getTariffs(
        @RequestParam(required = false) projectId: String?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<Map<String, Any>>> {
        return if (projectId != null) {
            // Получаем тарифы для конкретного проекта
            Mono.zip(
                tariffService.getTariffsByProject(UUID.fromString(projectId), page, size).collectList(),
                tariffService.getTariffsCountByProject(UUID.fromString(projectId))
            ).map { result ->
                val tariffs = result.t1
                val totalCount = result.t2
                val totalPages = (totalCount + size - 1) / size
                val response = mapOf(
                    "content" to tariffs,
                    "pagination" to mapOf(
                        "page" to page,
                        "size" to size,
                        "totalElements" to totalCount,
                        "totalPages" to totalPages,
                        "hasNext" to (page < totalPages - 1),
                        "hasPrevious" to (page > 0)
                    )
                )
                ApiResponse.success(response)
            }
        } else {
            // Получаем все тарифы
            Mono.zip(
                tariffService.getAllTariffs(page, size).collectList(),
                tariffService.getTariffsCount()
            ).map { result ->
                val tariffs = result.t1
                val totalCount = result.t2
                val totalPages = (totalCount + size - 1) / size
                val response = mapOf(
                    "content" to tariffs,
                    "pagination" to mapOf(
                        "page" to page,
                        "size" to size,
                        "totalElements" to totalCount,
                        "totalPages" to totalPages,
                        "hasNext" to (page < totalPages - 1),
                        "hasPrevious" to (page > 0)
                    )
                )
                ApiResponse.success(response)
            }
        }.onErrorResume { error ->
            Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения тарифов: ${error.message}"))
        }
    }

    @GetMapping("/tariffs/{id}")
    fun getTariffById(@PathVariable id: String): Mono<ApiResponse<TariffDto>> {
        return tariffService.getTariffById(UUID.fromString(id))
            .map { tariff ->
                ApiResponse.success(tariff)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<TariffDto>("Ошибка получения тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs")
    fun createTariff(@RequestBody request: TariffCreateRequest): Mono<ApiResponse<TariffDto>> {
        return tariffService.createTariff(request)
            .map { tariff ->
                ApiResponse.success(tariff, "Тариф успешно создан")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<TariffDto>("Ошибка создания тарифа: ${error.message}"))
            }
    }

    @PutMapping("/tariffs/{id}")
    fun updateTariff(
        @PathVariable id: String,
        @RequestBody request: TariffUpdateRequest
    ): Mono<ApiResponse<TariffDto>> {
        return tariffService.updateTariff(UUID.fromString(id), request)
            .map { tariff ->
                ApiResponse.success(tariff, "Тариф успешно обновлен")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<TariffDto>("Ошибка обновления тарифа: ${error.message}"))
            }
    }

    @DeleteMapping("/tariffs/{id}")
    fun deleteTariff(@PathVariable id: String): Mono<ApiResponse<String>> {
        return tariffService.deleteTariff(UUID.fromString(id))
            .map {
                ApiResponse.success("Тариф успешно удален")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка удаления тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/{id}/activate")
    fun activateTariff(@PathVariable id: String): Mono<ApiResponse<TariffDto>> {
        return tariffService.activateTariff(UUID.fromString(id))
            .map { tariff ->
                ApiResponse.success(tariff, "Тариф успешно активирован")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<TariffDto>("Ошибка активации тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/{id}/deactivate")
    fun deactivateTariff(@PathVariable id: String): Mono<ApiResponse<TariffDto>> {
        return tariffService.deactivateTariff(UUID.fromString(id))
            .map { tariff ->
                ApiResponse.success(tariff, "Тариф успешно деактивирован")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<TariffDto>("Ошибка деактивации тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/{id}/block")
    fun blockTariff(@PathVariable id: String): Mono<ApiResponse<TariffDto>> {
        return tariffService.blockTariff(UUID.fromString(id))
            .map { tariff ->
                ApiResponse.success(tariff, "Тариф успешно заблокирован")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<TariffDto>("Ошибка блокировки тарифа: ${error.message}"))
            }
    }

    // Endpoints для работы с полной информацией о тарифе

    @GetMapping("/tariffs/{id}/full")
    fun getTariffFullInfo(@PathVariable id: String): Mono<ApiResponse<TariffFullInfoDto>> {
        return tariffService.getTariffFullInfo(UUID.fromString(id))
            .map { tariffInfo ->
                ApiResponse.success(tariffInfo)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<TariffFullInfoDto>("Ошибка получения полной информации о тарифе: ${error.message}"))
            }
    }

    // Endpoints для работы с ProductDictRow

    @GetMapping("/tariffs/{tariffId}/products")
    fun getTariffProducts(@PathVariable tariffId: String): Mono<ApiResponse<List<ProductDictRowDto>>> {
        return tariffService.getProductDictRowsByTariff(UUID.fromString(tariffId))
            .collectList()
            .map { products ->
                ApiResponse.success(products)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<ProductDictRowDto>>("Ошибка получения продуктов тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/products")
    fun createTariffProduct(@RequestBody request: ProductDictRowCreateRequest): Mono<ApiResponse<ProductDictRowDto>> {
        return tariffService.createProductDictRow(request)
            .map { product ->
                ApiResponse.success(product, "Продукт тарифа успешно создан")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<ProductDictRowDto>("Ошибка создания продукта тарифа: ${error.message}"))
            }
    }

    @PutMapping("/tariffs/products/{id}")
    fun updateTariffProduct(
        @PathVariable id: String,
        @RequestBody request: ProductDictRowUpdateRequest
    ): Mono<ApiResponse<ProductDictRowDto>> {
        return tariffService.updateProductDictRow(UUID.fromString(id), request)
            .map { product ->
                val response = ApiResponse.success(product, "Продукт тарифа успешно обновлен")
                response
            }
            .onErrorResume { error ->
                println("Controller error: ${error.message}")
                Mono.just(ApiResponse.error<ProductDictRowDto>("Ошибка обновления продукта тарифа: ${error.message}"))
            }
    }

    @DeleteMapping("/tariffs/products/{id}")
    fun deleteTariffProduct(@PathVariable id: String): Mono<ApiResponse<String>> {
        return tariffService.deleteProductDictRow(UUID.fromString(id))
            .map {
                ApiResponse.success("Продукт тарифа успешно удален")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка удаления продукта тарифа: ${error.message}"))
            }
    }

    // Endpoints для работы с тарифными матрицами

    @GetMapping("/tariffs/products/{productDictRowId}/matrices")
    fun getTariffMatrices(@PathVariable productDictRowId: String): Mono<ApiResponse<List<TariffMatrixDto>>> {
        return tariffService.getTariffMatricesByProductDictRow(UUID.fromString(productDictRowId))
            .collectList()
            .map { matrices ->
                ApiResponse.success(matrices)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<TariffMatrixDto>>("Ошибка получения тарифных матриц: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/matrices")
    fun createTariffMatrix(@RequestBody request: TariffMatrixCreateRequest): Mono<ApiResponse<TariffMatrixDto>> {
        return tariffService.createTariffMatrix(request)
            .map { matrix ->
                ApiResponse.success(matrix, "Тарифная матрица успешно создана")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<TariffMatrixDto>("Ошибка создания тарифной матрицы: ${error.message}"))
            }
    }

    @PutMapping("/tariffs/matrices/{id}")
    fun updateTariffMatrix(
        @PathVariable id: String,
        @RequestBody request: TariffMatrixUpdateRequest
    ): Mono<ApiResponse<TariffMatrixDto>> {
        return tariffService.updateTariffMatrix(UUID.fromString(id), request)
            .map { matrix ->
                ApiResponse.success(matrix, "Тарифная матрица успешно обновлена")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<TariffMatrixDto>("Ошибка обновления тарифной матрицы: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/products/{productDictRowId}/matrix")
    fun updateTariffMatrixForProduct(
        @PathVariable productDictRowId: String,
        @RequestBody request: TariffMatrixBulkUpdateRequest
    ): Mono<ApiResponse<List<TariffMatrixDto>>> {
        return tariffService.updateTariffMatrixForProduct(UUID.fromString(productDictRowId), request)
            .collectList()
            .map { matrices ->
                ApiResponse.success(matrices, "Тарифная матрица успешно обновлена")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<TariffMatrixDto>>("Ошибка обновления тарифной матрицы: ${error.message}"))
            }
    }

    @DeleteMapping("/tariffs/matrices/{id}")
    fun deleteTariffMatrix(@PathVariable id: String): Mono<ApiResponse<String>> {
        return tariffService.deleteTariffMatrix(UUID.fromString(id))
            .map {
                ApiResponse.success("Тарифная матрица успешно удалена")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка удаления тарифной матрицы: ${error.message}"))
            }
    }

    // Endpoints для работы с маршрутами и тарифами

    @GetMapping("/routes/tariffs")
    fun getRouteTariffs(@RequestParam(required = false) projectId: String?): Mono<ApiResponse<Map<String, List<RouteTariffsResponse>>>> {
        val projectUuid = projectId?.let { UUID.fromString(it) }
        return tariffService.getRouteTariffs(projectUuid)
            .collectList()
            .map { routeTariffs ->
                val response = mapOf(
                    "content" to routeTariffs
                )
                ApiResponse.success(response)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Map<String, List<RouteTariffsResponse>>>("Ошибка получения тарифов по маршрутам: ${error.message}"))
            }
    }

    @PostMapping("/routes/tariffs/assign")
    fun assignTariffToRoute(@RequestBody request: RouteTariffAssignmentCreateRequest): Mono<ApiResponse<RouteTariffAssignmentDto>> {
        return tariffService.assignTariffToRoute(request)
            .map { assignment ->
                ApiResponse.success(assignment, "Тариф успешно назначен на маршрут")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<RouteTariffAssignmentDto>("Ошибка назначения тарифа на маршрут: ${error.message}"))
            }
    }

    @DeleteMapping("/routes/{routeId}/tariffs/{tariffId}")
    fun removeTariffFromRoute(
        @PathVariable routeId: String,
        @PathVariable tariffId: String
    ): Mono<ApiResponse<String>> {
        return tariffService.removeTariffFromRoute(UUID.fromString(routeId), UUID.fromString(tariffId))
            .map {
                ApiResponse.success("Тариф успешно удален с маршрута")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка удаления тарифа с маршрута: ${error.message}"))
            }
    }
} 