package ru.sbertroika.tkp3.pro.controller.config

import io.r2dbc.spi.ConnectionFactories
import io.r2dbc.spi.ConnectionFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.r2dbc.core.DefaultReactiveDataAccessStrategy
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.data.r2dbc.dialect.PostgresDialect
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories
import org.springframework.r2dbc.core.DatabaseClient

@Configuration
@EnableR2dbcRepositories(basePackages = ["ru.sbertroika.tkp3.pro.controller.output"])
open class DatabaseConfig(
    @Value("\${spring.r2dbc.url}")
    private val url: String
) {

    @Bean
    @Primary
    open fun connectionFactory(): ConnectionFactory = ConnectionFactories.get(url)

    @Bean
    @Primary
    open fun r2dbcEntityOperations(connectionFactory: ConnectionFactory): R2dbcEntityOperations {
        val strategy = DefaultReactiveDataAccessStrategy(PostgresDialect.INSTANCE)
        val databaseClient = DatabaseClient.builder()
            .connectionFactory(connectionFactory)
            .build()

        return R2dbcEntityTemplate(databaseClient, strategy)
    }

    @Bean
    open fun r2dbcEntityTemplate(connectionFactory: ConnectionFactory): R2dbcEntityTemplate {
        return R2dbcEntityTemplate(connectionFactory)
    }

    @Bean
    @Primary
    open fun databaseClient(connectionFactory: ConnectionFactory): DatabaseClient {
        return DatabaseClient.builder()
            .connectionFactory(connectionFactory)
            .bindMarkers(PostgresDialect.INSTANCE.bindMarkersFactory)
            .namedParameters(true)
            .build()
    }
}