package ru.sbertroika.tkp3.pro.controller.model

import org.springframework.data.relational.core.mapping.Column
import java.sql.Timestamp
import java.util.*

/**
 * DTO для отображения продуктов в UI
 */
data class ProductDto(
    /** Уникальный идентификатор продукта */
    @Column("id")
    val id: UUID? = null,

    /** Версия записи продукта */
    @Column("version")
    val version: Int? = null,

    /** Дата и время создания версии */
    @Column("versionCreatedAt")
    val versionCreatedAt: Timestamp? = null,

    /** Идентификатор пользователя, создавшего версию */
    @Column("versionCreatedBy")
    val versionCreatedBy: UUID? = null,

    /** Идентификатор проекта */
    @Column("projectId")
    val projectId: UUID? = null,

    /** Наименование продукта */
    @Column("name")
    val name: String? = null,

    /** Статус продукта */
    @Column("status")
    val status: String? = null,

    /** Теги продукта */
    @Column("tags")
    val tags: String = ""
) 