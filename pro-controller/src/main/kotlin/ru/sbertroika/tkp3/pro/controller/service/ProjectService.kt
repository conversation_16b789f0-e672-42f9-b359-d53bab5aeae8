package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.ProjectRepository
import ru.sbertroika.tkp3.pro.controller.output.ProjectOperatorRepository
import ru.sbertroika.tkp3.pro.model.Project
import ru.sbertroika.tkp3.pro.controller.model.ProjectDto
import ru.sbertroika.tkp3.pro.model.ProjectStatus
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Service
class ProjectService(
    private val projectRepository: ProjectRepository,
    private val projectOperatorRepository: ProjectOperatorRepository,
    private val projectMapper: ProjectMapper
) {

    /**
     * Получить все проекты с фильтрацией и пагинацией
     */
    fun getAllProjects(
        status: String?,
        region: String?,
        projectType: String?,
        page: Int,
        size: Int
    ): Flux<ProjectDto> {
        val offset = page * size
        return projectRepository.findAllProjectsWithFilters(
            status,
            region,
            projectType,
            size,
            offset
        ).flatMap { project ->
            // Преобразуем в DTO и заполняем операторскую организацию
            val dto = projectMapper.toDto(project)
            projectOperatorRepository.findLatestByProjectId(project.id!!)
                .next()
                .map { operator ->
                    val operatorOrg = ru.sbertroika.tkp3.pro.model.OperatorOrganization(
                        id = operator.operatorId,
                        name = operator.operatorName,
                        role = operator.operatorRole
                    )
                    projectMapper.fillComputedFields(dto, operatorOrg)
                }
                .defaultIfEmpty(projectMapper.fillComputedFields(dto))
        }
    }

    /**
     * Получить общее количество проектов с фильтрацией
     */
    fun getProjectsCount(
        status: String?,
        region: String?,
        projectType: String?
    ): Mono<Long> {
        return projectRepository.countProjectsWithFilters(
            status,
            region,
            projectType
        ).next()
    }

    /**
     * Получить проект по ID
     */
    fun getProjectById(id: UUID): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                // Преобразуем в DTO и заполняем операторскую организацию
                val dto = projectMapper.toDto(project)
                projectOperatorRepository.findLatestByProjectId(project.id!!)
                    .next()
                    .map { operator ->
                        val operatorOrg = ru.sbertroika.tkp3.pro.model.OperatorOrganization(
                            id = operator.operatorId,
                            name = operator.operatorName,
                            role = operator.operatorRole
                        )
                        projectMapper.fillComputedFields(dto, operatorOrg)
                    }
                    .defaultIfEmpty(projectMapper.fillComputedFields(dto))
            }
    }

    /**
     * Создать новый проект
     */
    fun createProject(projectDto: ProjectDto): Mono<ProjectDto> {
        val project = projectMapper.toEntity(projectDto)
        val now = Timestamp.valueOf(LocalDateTime.now())
        project.version = 1
        project.versionCreatedAt = now
        project.versionCreatedBy = UUID.randomUUID() // TODO: получить из контекста безопасности
        project.syncStatus = "PENDING"

        return projectRepository.save(project)
            .map { savedProject -> projectMapper.toDto(savedProject) }
    }

    /**
     * Обновить проект
     */
    fun updateProject(id: UUID, updatedProjectDto: ProjectDto): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { existingProject ->
                val updatedProject = projectMapper.toEntity(updatedProjectDto)
                val now = Timestamp.valueOf(LocalDateTime.now())
                updatedProject.id = id
                updatedProject.version = (existingProject.version ?: 0) + 1
                updatedProject.versionCreatedAt = now
                updatedProject.versionCreatedBy = UUID.randomUUID() // TODO: получить из контекста безопасности
                updatedProject.lastSyncDate = now
                
                projectRepository.save(updatedProject)
                    .map { savedProject -> projectMapper.toDto(savedProject) }
            }
    }

    /**
     * Удалить проект (логическое удаление)
     */
    fun deleteProject(id: UUID): Mono<Void> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                project.status = ProjectStatus.IS_DELETED
                projectRepository.save(project)
            }
            .then()
    }

    /**
     * Синхронизировать проект с договором
     */
    fun syncProjectWithContract(id: UUID): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                val now = Timestamp.valueOf(LocalDateTime.now())
                project.lastSyncDate = now
                project.syncStatus = "SYNCED"
                projectRepository.save(project)
                    .map { savedProject -> projectMapper.toDto(savedProject) }
            }
    }

    /**
     * Активировать проект
     */
    fun activateProject(id: UUID): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                project.status = ProjectStatus.ACTIVE
                projectRepository.save(project)
                    .map { savedProject -> projectMapper.toDto(savedProject) }
            }
    }

    /**
     * Завершить проект
     */
    fun completeProject(id: UUID): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                project.status = ProjectStatus.COMPLETED
                projectRepository.save(project)
                    .map { savedProject -> projectMapper.toDto(savedProject) }
            }
    }

    /**
     * Получить проекты по статусу
     */
    fun getProjectsByStatus(status: String): Flux<ProjectDto> {
        return projectRepository.findByStatus(status)
            .map { project -> projectMapper.toDto(project) }
    }

    /**
     * Получить проекты по региону
     */
    fun getProjectsByRegion(region: String): Flux<ProjectDto> {
        return projectRepository.findByRegion(region)
            .map { project -> projectMapper.toDto(project) }
    }

    /**
     * Получить проекты по типу
     */
    fun getProjectsByType(projectType: String): Flux<ProjectDto> {
        return projectRepository.findByProjectType(projectType)
            .map { project -> projectMapper.toDto(project) }
    }

    /**
     * Получить проекты по договору
     */
    fun getProjectsByContract(contractId: UUID): Flux<ProjectDto> {
        return projectRepository.findByContractId(contractId)
            .map { project -> projectMapper.toDto(project) }
    }

    /**
     * Получить организации проекта
     */
    fun getProjectOrganizations(id: UUID): Mono<List<Map<String, Any>>> {
        return projectRepository.findProjectById(id)
            .map { 
                // TODO: Реализовать получение организаций из базы данных
                // Пока возвращаем пустой список
                emptyList<Map<String, Any>>()
            }
    }
} 