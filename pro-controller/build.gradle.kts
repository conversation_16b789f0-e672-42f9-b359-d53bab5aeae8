import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    idea
    kotlin("jvm")
    id("org.springframework.boot") version "3.5.4"
    id("io.spring.dependency-management") version "1.1.0"
    `maven-publish`
}

group = "ru.sbertroika.pro"
version = rootProject.version

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    // Модели
    implementation(project(":pro-model"))
    
    // Spring Boot WebFlux (заменяем spring-boot-starter-web)
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-data-r2dbc")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    
    // Spring Security для WebFlux
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.security:spring-security-oauth2-resource-server")
    implementation("org.springframework.security:spring-security-oauth2-jose")

    //Logging
    implementation("org.springframework.boot:spring-boot-starter-logging")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.8.1")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor:1.8.1")
    implementation("org.zalando:logbook-spring-boot-webflux-autoconfigure:3.10.0")
    implementation("org.zalando:logbook-okhttp:3.10.0")
    
    // База данных (R2DBC вместо JDBC)
    implementation("org.postgresql:r2dbc-postgresql")
    implementation("org.flywaydb:flyway-core")
    
    // OpenAPI для WebFlux
    implementation("org.springdoc:springdoc-openapi-starter-webflux-ui:2.5.0")
    
    // Kotlin
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib)
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.0")

    // PostgreSQL driver for Flyway
    runtimeOnly("org.postgresql:postgresql:42.7.1")

    implementation("org.flywaydb:flyway-core:11.10.5")
    implementation("org.flywaydb:flyway-database-postgresql:11.10.5")

    // Database migrations
    implementation(project(":pro-migrations"))
    
    // Тестирование
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("io.projectreactor:reactor-test")
    testImplementation("org.testcontainers:postgresql")
    testImplementation("org.testcontainers:r2dbc")
    testImplementation("org.testcontainers:junit-jupiter")
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

// Настройка публикации в Nexus
publishing {
    publications {
        create<MavenPublication>("maven") {
            from(components["java"])

            pom {
                name.set("PRO Controller")
                description.set("REST API controller for PRO reference data management")
                url.set("https://github.com/your-org/pro-domain")

                licenses {
                    license {
                        name.set("The Apache License, Version 2.0")
                        url.set("http://www.apache.org/licenses/LICENSE-2.0.txt")
                    }
                }

                developers {
                    developer {
                        id.set("sbertroika")
                        name.set("Sbertroika Team")
                        email.set("<EMAIL>")
                    }
                }
            }
        }
    }

    repositories {
        maven {
            name = "nexusReleases"
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            name = "nexusSnapshots"
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }
}

// Задача для публикации в Nexus (выбирает правильный репозиторий в зависимости от версии)
tasks.register("publishToNexus") {
    dependsOn("publishMavenPublicationToNexusReleasesRepository")
    onlyIf {
        !version.toString().endsWith("-SNAPSHOT")
    }

    doLast {
        println("Published ${project.name} version ${version} to Nexus Releases")
    }
    group = "publishing"
    description = "Публикует релизную версию в Nexus"
}

tasks.register("publishToNexusSnapshot") {
    dependsOn("publishMavenPublicationToNexusSnapshotsRepository")
    onlyIf {
        version.toString().endsWith("-SNAPSHOT")
    }

    doLast {
        println("Published ${project.name} version ${version} to Nexus Snapshots")
    }
    group = "publishing"
    description = "Публикует SNAPSHOT версию в Nexus"
} 