package ru.sbertroika.tkp3.manifest.model

import java.time.ZonedDateTime
import java.util.*

data class Service(
    val pasiv: ManifestPasiv? = null,

    val pro: ManifestPro? = null,
    val proEmv: ManifestProEmv? = null,
    val proCash: ManifestProCash? = null,
    val proTroika: ManifestProTroika? = null,
    val proAbt: ManifestProAbt? = null,

    val tms: ManifestTms? = null,

    val agentGate: ManifestAgentGate? = null,
)

data class Manifest(
    val id: String,
    val version: Int,
    val projectId: UUID,
    val createdAt: ZonedDateTime,
    val projectIndex: Int,
    val validFrom: ZonedDateTime,
    val validTill: ZonedDateTime,
    val service: Service
)