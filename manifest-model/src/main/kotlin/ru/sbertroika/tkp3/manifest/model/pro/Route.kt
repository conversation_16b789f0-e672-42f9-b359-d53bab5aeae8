package ru.sbertroika.tkp3.manifest.model.pro

import java.util.*

data class Route(
    val id: UUID,
    val name: String,
    val scheme: RouteScheme,
    val routeIndex: Int,
    val number: String,
    val station: List<RouteStation>,
    val constraint: List<Constraint>,
    val version: Int
)

data class RouteStation(
    val id: UUID,
    val pos: Int,
    val version: Int
)

enum class RouteScheme {
    DIRECTIONAL, CIRCLE
}