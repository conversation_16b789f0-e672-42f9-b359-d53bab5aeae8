package ru.sbertroika.tkp3.manifest.model.pro

import java.util.*

data class ProductMenu(
    val productId: UUID,
    val tariffId: UUID,
    val priceRules: List<PriceRule>,
    val version: Int
)

data class Product(
    val id: UUID,
    val name: String,
    val version: Int
)

data class Tariff(
    val id: UUID,
    val name: String,
    val constraint: List<Constraint>,
    val version: Int
)

data class PriceRuleMatrixItem(
    val stationFrom: UUID,
    val stationTo: UUID,
    val price: Int,
    val version: Int
)

data class PriceRule(
    val paymentType: TPaymentType,
    val price: Int,
    val matrix: List<PriceRuleMatrixItem>,
    val version: Int
)

enum class TPaymentType {
    CASH, EMV, TROIKA_TICKET, TROIKA_WALLET, ABT_TICKET, ABT_WALLET, PROSTOR_TICKET, QR_TICKET, QR_WALLET
}