package ru.sbertroika.tkp3.manifest.model

import ru.sbertroika.tkp3.manifest.model.pro.*

data class ManifestProComposite(
    val pro: ManifestPro,
    val abt: ManifestProAbt = ManifestProAbt(),
    val troika: ManifestProTroika = ManifestProTroika()
)

data class ManifestPro(
    val features: List<TkpFeature>,
    val dict: ManifestProDict
)

data class ManifestProDict(
    val route: List<Route>,
    val station: List<Station>,
    val transport: List<Transport>,
    val product: List<Product>,
    val tariff: List<Tariff>,
    val menu: List<ProductMenu>,
    val employee: List<Employee>
)

data class ManifestProEmv(
    val features: List<TkpFeature>
)

data class ManifestProCash(
    val features: List<TkpFeature>
)

data class ManifestProTroika(
    val features: List<TkpFeature> = emptyList(),
    val dict: ManifestProTroikaDict = ManifestProTroikaDict()
)

data class ManifestProTroikaDict(
    val templates: List<TroikaTemplate> = emptyList()
)

data class ManifestProAbtDict(
    val templates: List<AbtTemplate> = emptyList()
)

data class ManifestProAbt(
    val features: List<TkpFeature> = emptyList(),
    val dict: ManifestProAbtDict = ManifestProAbtDict()
)