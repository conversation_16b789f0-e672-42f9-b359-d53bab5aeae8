package ru.sbertroika.tkp3.manifest.model.pro

import java.util.*

data class Employee(
    val id: UUID,
    val role: Employee<PERSON><PERSON>,
    val surname: String,
    val name: String,
    val middleName: String
)

enum class EmployeeRole(val alias: String) {
    /**
     * Супер Админ
     */
    SUPER_ADMIN("tkp3_super_admin"),

    /**
     * Админ
     */
    ADMIN("tkp3_admin"),

    /**
     * Оператор
     */
    OPERATOR("tkp3_operator"),

    /**
     * Перевозчик
     */
    CARRIER("tkp3_carrier"),

    /**
     * Администратор
     */
    TERMINAL_ADMIN("terminal_user_admin"),

    /**
     * Кассир
     */
    TERMINAL_CASHIER("terminal_user_cashier"),

    /**
     * Инкассатор
     */
    TERMINAL_COLLECTOR("terminal_user_collector"),

    /**
     * Кондуктор
     */
    TERMINAL_CONDUCTOR("terminal_user_conductor"),

    /**
     * Контролер
     */
    TERMINAL_CONTROLER("terminal_user_controler"),

    /**
     * Водитель
     */
    TERMINAL_DRIVER("terminal_user_driver"),

    UNDEFINED("");

    companion object {
        fun findByAlias(alias: String): EmployeeRole {
            for (value in values()) {
                if (value.alias == alias) return value
            }

            return UNDEFINED
        }
    }
}