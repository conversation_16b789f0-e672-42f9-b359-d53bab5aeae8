package ru.sbertroika.tkp3.manifest.model.pro

import java.time.ZonedDateTime
import java.util.*

data class AbtPassRule(
    val id: UUID,
    val version: Int,
    val index: Int,
    val action: String
)

data class AbtTemplateCounter(
    val id: UUID,
    val version: Int,
    val type: SubscriptionCounterType,
    val value: Int,
    val isBus: Boolean,
    val isTrolleybus: Boolean,
    val isTram: Boolean,
    val isMetro: Boolean
)

data class AbtTemplate(
    val id: UUID,
    val version: Int,
    val appCode: Int,
    val crdCode: Int,
    val name: String,
    val type: AbonementType,
    val isSocial: <PERSON>olean,
    val rules: List<AbtPassRule>,
    val counters: List<AbtTemplateCounter>,
    val cardTypes: List<AbtCardType>,
    val validTimeType: ValidTimeType,
    val validTimeStart: ZonedDateTime,
    val validTimeEnd: ZonedDateTime,
    val validTimeDays: Int
)

enum class ValidTimeType {
    /**
     * С периодом на календарный месяц
     */
    INTERVAL,

    /**
     * Списание в период(дней) от первой поездки
     */
    DAYS,

    /**
     * Списание в период(дней) от первой поездки, но не позднее даты
     */
    INTERVAL_AND_DAYS
}


data class AbtCardType(
    val id: UUID
)