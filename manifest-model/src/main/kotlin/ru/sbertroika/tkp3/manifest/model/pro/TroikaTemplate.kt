package ru.sbertroika.tkp3.manifest.model.pro

import java.time.ZonedDateTime
import java.util.*

data class TroikaTemplate(
    val id: UUID,
    val version: Int,
    val appCode: Int,
    val crdCode: Int,
    val name: String,
    val type: AbonementType,
    val limit: Int,
    val prolongType: ProlongType,
    val prolongStartDate: ZonedDateTime,
    val prolongEndDate: ZonedDateTime,
    val prolongDays: Int,
    val prolongCardCode: Int?
)
