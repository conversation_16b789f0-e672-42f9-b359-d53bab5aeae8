package ru.sbertroika.tkp3.manifest.model.pro

import java.util.*

data class Constraint(
    val type: ConstraintType,
    val baseRule: ConstraintBaseRule,
    val exception: List<ConstraintException>
)

data class ConstraintException(
    val id: UUID
)

enum class ConstraintType {
    TARIFF,
    ROUTE,
    TRANSPORT,
    SERVICE,
    ORGANIZATION
}

enum class ConstraintBaseRule {
    ALLOW, DENY
}