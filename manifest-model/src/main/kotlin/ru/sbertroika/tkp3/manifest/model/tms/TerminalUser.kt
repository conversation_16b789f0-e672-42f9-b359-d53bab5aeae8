package ru.sbertroika.tkp3.manifest.model.tms

import java.util.*

data class TerminalUser(
    val id: UUID,
    val role: TerminalUserRole,
    val surname: String,
    val name: String,
    val middleName: String,
    val personalNumber: String,
    val pinHash: String
)

enum class TerminalUserRole(val alias: String) {
    /**
     * Администратор
     */
    TERMINAL_ADMIN("terminal_user_admin"),

    /**
     * Кассир
     */
    TERMINAL_CASHIER("terminal_user_cashier"),

    /**
     * Инкассатор
     */
    TERMINAL_COLLECTOR("terminal_user_collector"),

    /**
     * Кондуктор
     */
    TERMINAL_CONDUCTOR("terminal_user_conductor"),

    /**
     * Контролер
     */
    TERMINAL_CONTROLER("terminal_user_controler"),

    /**
     * Водитель
     */
    TERMINAL_DRIVER("terminal_user_driver"),
}