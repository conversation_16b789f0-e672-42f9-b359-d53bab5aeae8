docker-build-and-run: docker-build-gate docker-build-gate-private docker-build-processing docker-build-ui
	docker-compose up -d

docker-run-infra:
	docker-compose up -d pro_db keycloak_db keycloak zoo1 s3 kafka kafka-ui

docker-build: docker-build-gate docker-build-gate-private docker-build-processing docker-build-ui

start:
	docker-compose up -d

restart:
	docker-compose stop
	docker-compose rm -vf pro_gate
	docker-compose rm -vf pro_gate_private
	docker-compose rm -vf pro_processing
	docker-compose rm -vf pro_ui
	docker-compose up -d

restart-all:
	docker-compose stop && docker-compose rm -vf && docker-compose up -d

docker-rm:
	docker-compose stop && docker-compose rm -vf

docker-rm-all:
	docker-compose rm -vf

stop:
	docker-compose stop

rebuild-and-restart: stop docker-rm docker-build start

rebuild-and-restart-all: stop docker-rm-all docker-build start

docker-build-gate:
	@echo "[INFO] Сборка docker-образа pro-gate с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t pro-gate:local \
		-f pro-gate/Dockerfile .
	rm -rf .ci-gradle

docker-build-gate-private:
	@echo "[INFO] Сборка docker-образа pro-gate-private с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t pro-gate-private:local \
		-f pro-gate-private/Dockerfile .
	rm -rf .ci-gradle

docker-build-processing:
	@echo "[INFO] Сборка docker-образа pro-processing с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t pro-processing:local \
		-f pro-processing/Dockerfile .
	rm -rf .ci-gradle

docker-build-ui:
	@echo "[INFO] Сборка docker-образа pro-ui"
	docker build -t pro-ui:local -f pro-ui/Dockerfile pro-ui/

rebuild-and-restart: docker-build restart

stop:
	docker-compose stop

docker-rm:
	docker-compose rm -vf

logs:
	docker-compose logs -f

logs-gate:
	docker-compose logs -f pro_gate

logs-gate-private:
	docker-compose logs -f pro_gate_private

logs-ui:
	docker-compose logs -f pro_ui

logs-processing:
	docker-compose logs -f pro_processing