package ru.sbertroika.tkp3.manifest.starter.worker

import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit

@Component
class ManifestClearLocalCashWorker {

    @Scheduled(fixedDelay = 12, timeUnit = TimeUnit.HOURS)
    fun run() {
        //TODO сделать очистку локальной директории с кэшом. Удалять все что старше двух недель
    }
}