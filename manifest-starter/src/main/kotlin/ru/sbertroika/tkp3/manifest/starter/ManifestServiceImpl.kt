package ru.sbertroika.tkp3.manifest.starter

import arrow.core.Either
import arrow.core.computations.ResultEffect.bind
import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.apache.curator.RetryPolicy
import org.apache.curator.framework.CuratorFramework
import org.apache.curator.framework.CuratorFrameworkFactory
import org.apache.curator.framework.recipes.cache.TreeCache
import org.apache.curator.framework.recipes.cache.TreeCacheListener
import org.apache.curator.retry.ExponentialBackoffRetry
import org.apache.zookeeper.KeeperException.NodeExistsException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.manifest.model.Manifest
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import java.io.ByteArrayOutputStream
import java.io.File
import java.util.zip.CRC32


@Service
class ManifestServiceImpl(
    private val s3Client: S3Client,

    @Value("\${zookeeper.nodes}")
    private val nodes: String,
    @Value("\${zookeeper.namespace:tkp3}")
    private val namespace: String,
    @Value("\${zookeeper.manifest.path:/manifest}")
    private val rootManifestPath: String,
    @Value("\${zookeeper.manifest.path:/manifest-cash}")
    private val rootCashManifestPath: String,
    @Value("\${zookeeper.max-retries:3}")
    private val maxRetries: Int,

    @Value("\${s3.bucket}")
    private val bucket: String,

    @Value("\${manifest.tmp_path:/tmp}")
    private val tmpPath: String
) : ManifestService {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    private val callbacks = mutableListOf<ManifestChangeListener>()

    private val client: CuratorFramework

    private val watcher: TreeCache

    private val mapper = mapper()

    init {
        val retryPolicy: RetryPolicy = ExponentialBackoffRetry(1000, maxRetries)
        client = CuratorFrameworkFactory.builder()
            .connectString(nodes)
            .sessionTimeoutMs(5000)
            .connectionTimeoutMs(5000)
            .retryPolicy(retryPolicy)
            .namespace(namespace)
            .build()
        client.start()

        client.createContainers(rootManifestPath)

        watcher = TreeCache(client, rootManifestPath)
        watcher.listenable.addListener(TreeCacheListener { _, event ->
            if (rootManifestPath.equals(event?.data?.path)) return@TreeCacheListener
            if (callbacks.isNotEmpty() && event != null && event.data != null) callbacks.forEach {
                it.onChange(
                    event.data.path
                        .replace("$rootManifestPath/", "")
                        .replace("$rootCashManifestPath/", ""),
                    String(event.data.data)
                )
            }
        })
    }

    override fun runWatcher(listener: ManifestChangeListener) {
        callbacks.add(listener)
        watcher.start()
    }

    override suspend fun setManifestPath(manifestPath: String, projectId: String) {
        client.blockUntilConnected()

        try {
            client.create()
                .creatingParentsIfNeeded()
                .forPath("$rootManifestPath/$projectId", manifestPath.toByteArray())
        } catch (e: NodeExistsException) {
            client.setData()
                .forPath("$rootManifestPath/$projectId", manifestPath.toByteArray())
        }

        try {
            client.create()
                .creatingParentsIfNeeded()
                .forPath("$rootCashManifestPath/$projectId", manifestPath.toByteArray())
        } catch (e: NodeExistsException) {
            client.setData()
                .forPath("$rootCashManifestPath/$projectId", manifestPath.toByteArray())
        }
    }

    override suspend fun getManifestPath(projectId: String): Either<Throwable, String> = Either.catch {
        val bytes = client.data.forPath("$rootManifestPath/$projectId")
        if (bytes.isEmpty()) "" else String(bytes)
    }

    override suspend fun getManifest(manifestPath: String): Either<Throwable, Manifest> = Either.catch {
        val buffer = s3Client.getObject(
            GetObjectRequest.builder()
                .bucket(bucket)
                .key(manifestPath)
                .build()
        )

        mapper.readValue<Manifest>(buffer)
    }

    override suspend fun getManifestByProject(projectId: String): Either<Throwable, Manifest?> = Either.catch {
        val root = getManifestPath(projectId).bind()
        if (root.isEmpty()) {
            val cash = getCashManifestPath(projectId).bind()
            if (cash.isEmpty()) null else getManifest(cash).bind()
        } else {
            getManifest(root).bind()
        }
    }

    override suspend fun getManifest(id: String, version: Int): Either<Throwable, Manifest> = Either.catch {
        val manifestPath = "$id-$version.json"
        val file = File(tmpPath, manifestPath)
        if (file.exists()) {
            mapper.readValue<Manifest>(file)
        } else {
            getManifest(manifestPath).fold(
                {
                    throw it
                },
                { manifest ->
                    coroutineScope {
                        async {
                            mapper.writeValue(file, manifest)
                        }
                    }.await()
                    manifest
                }
            )
        }
    }

    override suspend fun saveManifest(manifest: Manifest): Either<Throwable, String> = Either.catch {
        val baos = ByteArrayOutputStream()
        mapper.writeValue(baos, manifest)
        val buffer = baos.toByteArray()

        val crc = CRC32()
        crc.update(buffer)
        val crc32 = java.lang.Long.toHexString(crc.value).uppercase()

        val fileName = "${manifest.id}-${manifest.version}.json"
        s3Client.putObject(
            PutObjectRequest.builder()
                .bucket(bucket)
                .key(fileName)
                .checksumCRC32(crc32)
                .build(),
            RequestBody.fromBytes(buffer)
        )

        fileName
    }

    override suspend fun resetManifest(projectId: String) {
        setManifestPath("", projectId)
    }

    private suspend fun getCashManifestPath(projectId: String): Either<Throwable, String> = Either.catch {
        val bytes = client.data.forPath("$rootCashManifestPath/$projectId")
        if (bytes.isEmpty()) "" else String(bytes)
    }
}
