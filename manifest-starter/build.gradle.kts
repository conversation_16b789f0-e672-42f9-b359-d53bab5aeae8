import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.springframework.boot.gradle.tasks.bundling.BootJar

plugins {
    idea
    `java-library`
    kotlin("jvm")
    id("org.springframework.boot") version "3.5.4"
    id("io.spring.dependency-management") version "1.1.0"
    kotlin("plugin.spring") version libs.versions.kotlin.get() apply false
    `maven-publish`
}

group = "ru.sbertroika.pro"
version = rootProject.version

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

dependencies {
    api(project(":manifest-model"))

    compileOnly("org.springframework.boot:spring-boot")
    compileOnly("org.springframework.boot:spring-boot-autoconfigure")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    //Kotlin
    compileOnly(libs.kotlin.reflect)
    compileOnly(libs.kotlin.stdlib)
    compileOnly("io.arrow-kt:arrow-core:1.2.1")

    api("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.0")
    api("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.0")
    api("com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.15.0")

    api("ru.gildor.coroutines:kotlin-coroutines-okhttp:1.0")

    compileOnly("org.apache.curator:curator-framework:5.5.0")
    compileOnly("org.apache.curator:curator-recipes:5.5.0")

    compileOnly("software.amazon.awssdk:s3:2.22.0")
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.named<BootJar>("bootJar") {
    enabled = false
}

// Настройка публикации в Nexus
publishing {
    publications {
        create<MavenPublication>("maven") {
            from(components["java"])

            pom {
                name.set("PRO Model")
                description.set("Model library for pro functionality")
                url.set("https://github.com/your-org/pro-domain")

                licenses {
                    license {
                        name.set("The Apache License, Version 2.0")
                        url.set("http://www.apache.org/licenses/LICENSE-2.0.txt")
                    }
                }

                developers {
                    developer {
                        id.set("sbertroika")
                        name.set("SBertroika Team")
                        email.set("<EMAIL>")
                    }
                }
            }
        }
    }

    repositories {
        maven {
            name = "nexusReleases"
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            name = "nexusSnapshots"
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }
}

// Задача для публикации в Nexus (выбирает правильный репозиторий в зависимости от версии)
tasks.register("publishToNexus") {
    dependsOn("publishMavenPublicationToNexusReleasesRepository")
    onlyIf {
        !version.toString().endsWith("-SNAPSHOT")
    }

    doLast {
        println("Published ${project.name} version ${version} to Nexus Releases")
    }
    group = "publishing"
    description = "Публикует релизную версию в Nexus"
}

tasks.register("publishToNexusSnapshot") {
    dependsOn("publishMavenPublicationToNexusSnapshotsRepository")
    onlyIf {
        version.toString().endsWith("-SNAPSHOT")
    }

    doLast {
        println("Published ${project.name} version ${version} to Nexus Snapshots")
    }
    group = "publishing"
    description = "Публикует SNAPSHOT версию в Nexus"
}