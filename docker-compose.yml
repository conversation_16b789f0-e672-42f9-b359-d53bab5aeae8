version: '3.7'

services:
  pro_gate:
    image: pro-gate:local
    container_name: pro-gate
    ports:
      - 5010:5000
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      DB_URL: postgresql://pro-db:5432/pro
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_MIGRATION_ENABLE: "true"
      R2DB_URL: ******************************************/pro

      TMS_KEYCLOAK_HOST: https://dev-auth.sbertroika.tech
      TMS_KEYCLOAK_REALM: test-asop
      TMS_KEYCLOAK_SECRET: test-asop
      TMS_KEYCLOAK_USERNAME: test
      TMS_KEYCLOAK_PASSWORD: test
      TMS_KEYCLOAK_CLIENT_ID: test-client
      TMS_KEYCLOAK_USER_REALM: test-asop
    depends_on:
      - pro_db
      - kafka
    networks:
      - pro-domain_default

  pro_db:
    image: postgres:14
    container_name: pro-db
    restart: always
    ports:
      - 5437:5432
    volumes:
      - ./data/pro:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: pro
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - pro-domain_default

  pro_gate_private:
    image: pro-gate-private:local
    container_name: pro-gate-private
    ports:
      - 5015:5005
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      DB_URL: postgresql://pro-db:5432/pro
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_MIGRATION_ENABLE: "false"
      R2DB_URL: ******************************************/pro

      TMS_KEYCLOAK_HOST: https://dev-auth.sbertroika.tech
      TMS_KEYCLOAK_REALM: test-asop
      TMS_KEYCLOAK_SECRET: test-asop
      TMS_KEYCLOAK_USERNAME: test
      TMS_KEYCLOAK_PASSWORD: test
      TMS_KEYCLOAK_CLIENT_ID: test-client
      TMS_KEYCLOAK_USER_REALM: test-asop
    depends_on:
      - pro_db
      - kafka
    networks:
      - pro-domain_default

  pro_ui:
    image: pro-ui:local
    container_name: pro-ui
    ports:
      - 3000:80
    depends_on:
      - pro_gate
      - pro_gate_private
    networks:
      - pro-domain_default

  pro_processing:
    image: pro-processing:local
    container_name: pro-processing
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - pro_db
      - kafka
    environment:
      DB_URL: postgresql://pro-db:5432/pro
      DB_USER: postgres
      DB_PASSWORD: postgres
      R2DB_URL: ******************************************/pro
      ZOOKEEPER_NODES: zoo1:2181,zoo2:2181,zoo3:2181
      S3_URL: http://s3:9000
      S3_ACCESS_KEY_ID: s3__user
      S3_SECRET_ACCESS_KEY: s3__pass
      S3_BUCKET: tkp3-manifest
      KAFKA_SERVERS: kafka:29092
    networks:
      - pro-domain_default

  keycloak_db:
    image: docker.io/bitnami/postgresql:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - POSTGRESQL_USERNAME=bn_keycloak
      - POSTGRESQL_DATABASE=bitnami_keycloak
      - POSTGRESQL_PASSWORD=bn_keycloak
    networks:
      - keycloak_net

  keycloak:
    image: docker.io/bitnami/keycloak:latest
    container_name: keycloak
    ports:
      - "8081:8080"
    environment:
      - KEYCLOAK_CREATE_ADMIN_USER=true
      - KEYCLOAK_ADMIN_USER=user
      - KEYCLOAK_ADMIN_PASSWORD=12345
      - KEYCLOAK_DATABASE_HOST=keycloak_db
      - KEYCLOAK_DATABASE_USER=bn_keycloak
      - KEYCLOAK_DATABASE_PASSWORD=bn_keycloak
    depends_on:
      - keycloak_db
    networks:
      - keycloak_net

  zoo1:
    image: zookeeper
    restart: always
    hostname: zoo1
    ports:
      - "2181:2181"
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181
    networks:
      - pro-domain_default

  s3:
    image: minio/minio:RELEASE.2025-05-24T17-08-30Z
    container_name: s3
    environment:
      MINIO_ROOT_USER: s3__user
      MINIO_ROOT_PASSWORD: s3__pass
      MINIO_DOMAIN: s3 # переключение в режим virtual-hosts-style
    ports:
      - 9001:9001 # WebUI
    volumes:
      - ./data/minio:/data
    command: server --console-address ":9001" /data
    networks:
      - pro-domain_default

  kafka:
    image: confluentinc/cp-kafka:7.5.9
    container_name: kafka
    depends_on:
      - zoo1
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zoo1:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    networks:
      - pro-domain_default

  kafka-ui:
    image: provectuslabs/kafka-ui:v0.7.2
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - 8086:8080
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zoo1:2181
    networks:
      - pro-domain_default

#  swagger-ui:
#    image: swaggerapi/swagger-ui:v5.10.3
#    container_name: swagger-ui
#    ports:
#      - 8087:8080
#    environment:
#      SWAGGER_JSON: /app/docs/api/console-swagger.yaml
#    volumes:
#      - ./docs/api:/app/docs/api:ro
#    networks:
#      - pro-domain_default

networks:
  pro-domain_default:
    driver: bridge
  keycloak_net:
    driver: bridge