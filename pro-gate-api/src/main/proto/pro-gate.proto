syntax = "proto3";

package ru.sbertroika.pro.gate.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common.proto";
import "common-pro.proto";
import "common-manifest.proto";
import "common-manifest-pro.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.pro.gate.v1";

service PROGateService {
  // Получение манифеста
  rpc getManifest(google.protobuf.Empty) returns (ManifestResponse);
  // Запрос справочника станций
  rpc getStationList(Manifest) returns (common.pro.StationListResponse);
  // Запрос справочника тарифов
  rpc getTariffList(Manifest) returns (common.pro.TariffListResponse);
  // Запрос справочника маршрутов
  rpc getRouteList(Manifest) returns (common.pro.RouteListResponse);
  // Запрос справочника продуктов
  rpc getProductList(Manifest) returns (common.pro.ProductListResponse);
  // Запрос справочника Т/С
  rpc getTransportList(Manifest) returns (common.pro.TransportListResponse);
  // Запрос билетного меню
  rpc getProductMenu(Manifest) returns (ProductMenuResponse);
  // Получение манифеста
  rpc getManifestByProject(common.manifest.v1.ManifestRequest) returns (ManifestByProjectResponse);
}

message ManifestResult {
  common.manifest.v1.pro.ManifestPro manifest = 1;
  common.manifest.v1.pro.ManifestProAbt manifestAbt = 2;
  common.manifest.v1.pro.ManifestProTroika manifestTroika = 3;
}

message ManifestByProjectResponse {
  oneof response {
    common.v1.OperationError error = 1;
    ManifestResult result = 2;
  }
}

message Organization {
  string id = 1;                    // Идентификатор организации
  string name = 2;                  // Полное наименование организации
  string inn = 3;                   // ИНН
  string address = 4;               // Адрес организации
  string paymentPlace = 5;          // Платежное место
}

message Manifest {
  string id = 1;
  uint32 version = 2;
  string sign = 3;
  google.protobuf.Timestamp validFrom = 4;
  google.protobuf.Timestamp validTill = 5;
  uint32 projectIndex = 6;
  Organization organization = 7;
}

message ManifestResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Manifest manifest = 2;
  }
}

message PriceRuleMatrixItem {
  string stationFrom = 1;
  string stationTo = 2;
  uint32 price = 3;
}

message PriceRule {
  enum TPaymentType {
    CASH = 0;
    EMV = 1;
    EMV_MIR = 2;
    TROIKA_WALLET = 3;
  }
  TPaymentType paymentType = 1;
  uint32 price = 2;
  repeated PriceRuleMatrixItem matrix = 3;
}

message ProductMenu {
  string productId = 1;
  string tariffId = 2;
  repeated PriceRule priceRules = 3;
}

message ProductMenuResult {
  repeated ProductMenu menu = 1;
}

message ProductMenuResponse {
  oneof response {
    common.v1.OperationError error = 1;
    ProductMenuResult result = 2;
  }
}