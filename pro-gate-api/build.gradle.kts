import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import com.google.protobuf.gradle.*
import org.gradle.kotlin.dsl.named
import org.springframework.boot.gradle.tasks.bundling.BootJar

plugins {
    idea
    `java-library`
    `maven-publish`
    kotlin("jvm")
    id("com.google.protobuf") version libs.versions.protobufPlugin.get()
    id("org.springframework.boot") version "3.5.4"
    id("io.spring.dependency-management") version "1.1.0"
}

group = "ru.sbertroika.pro"
version = rootProject.version

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    implementation(project(":pro-common"))
    implementation("ru.sbertroika.common:common-api:1.0.6")
    implementation("ru.sbertroika.common:common-manifest:1.0.6")


    implementation("io.grpc:grpc-kotlin-stub:1.4.3")
    implementation("io.grpc:grpc-protobuf:1.71.0")
    implementation("io.grpc:grpc-stub:1.71.0")
    implementation("io.grpc:grpc-protobuf-lite:1.71.0")
    implementation("io.grpc:grpc-netty:1.71.0")
    implementation("com.google.protobuf:protobuf-java:3.21.7")
    implementation("com.google.protobuf:protobuf-kotlin:3.21.7")

    //Kotlin
    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlin.reflect)

    api("org.springframework.data:spring-data-relational")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.0")
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${libs.versions.protoC.get()}"
    }

    plugins {
        id("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:${libs.versions.grpc.get()}"
        }
        id("grpckt") {
            artifact = "io.grpc:protoc-gen-grpc-kotlin:${libs.versions.grpcKotlin.get()}:jdk8@jar"
        }
    }

    generateProtoTasks {
        all().forEach { task ->
            task.builtins {
                id("kotlin")
            }
            task.plugins {
                id("grpc") {
                    option("lite")
                }
                id("grpckt") {
                    option("lite")
                }
            }
        }
    }

    generatedFilesBaseDir = "$projectDir/src/generated"
}

tasks.register<Delete>("cleanGeneratedProto") {
    delete(file("$projectDir/src/generated"))
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.named<BootJar>("bootJar") {
    enabled = false
}

// Настройка публикации в Nexus
publishing {
    publications {
        create<MavenPublication>("maven") {
            from(components["java"])

            pom {
                name.set("PRO API")
                description.set("API library for pro functionality")
                url.set("https://github.com/your-org/pro-domain")

                licenses {
                    license {
                        name.set("The Apache License, Version 2.0")
                        url.set("http://www.apache.org/licenses/LICENSE-2.0.txt")
                    }
                }

                developers {
                    developer {
                        id.set("sbertroika")
                        name.set("SBertroika Team")
                        email.set("<EMAIL>")
                    }
                }
            }
        }
    }

    repositories {
        maven {
            name = "nexusReleases"
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            name = "nexusSnapshots"
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }
}

// Задача для публикации в Nexus (выбирает правильный репозиторий в зависимости от версии)
tasks.register("publishToNexus") {
    dependsOn("publishMavenPublicationToNexusReleasesRepository")
    onlyIf {
        !version.toString().endsWith("-SNAPSHOT")
    }

    doLast {
        println("Published ${project.name} version ${version} to Nexus Releases")
    }
    group = "publishing"
    description = "Публикует релизную версию в Nexus"
}

tasks.register("publishToNexusSnapshot") {
    dependsOn("publishMavenPublicationToNexusSnapshotsRepository")
    onlyIf {
        version.toString().endsWith("-SNAPSHOT")
    }

    doLast {
        println("Published ${project.name} version ${version} to Nexus Snapshots")
    }
    group = "publishing"
    description = "Публикует SNAPSHOT версию в Nexus"
}