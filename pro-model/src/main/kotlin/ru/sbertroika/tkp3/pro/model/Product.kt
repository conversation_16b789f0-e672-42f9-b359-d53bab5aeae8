package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("product")
data class Product(

    @Column("p_id")
    var id: UUID? = null,

    @Column("p_version")
    var version: Int? = null,

    @Column("p_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("p_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("p_project_id")
    var projectId: UUID? = null,

    @Column("p_name")
    var name: String? = null,

    /**
     * Статус продукта
     */
    @Column("p_status")
    var status: String? = null,

    @Column("tags")
    var tags: String? = null
)

data class ProductPK(
    val pId: UUID? = null,
    val pVersion: Int? = null,
) : Serializable


enum class ProductStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}