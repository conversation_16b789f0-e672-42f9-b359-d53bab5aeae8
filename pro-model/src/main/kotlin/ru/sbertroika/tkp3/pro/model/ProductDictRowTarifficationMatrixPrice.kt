package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("product_dict_row_tariff_matrix_price")
data class ProductDictRowTariffMatrixPrice(

    @Column("pdrtmp_id")
    var id: UUID? = null,

    @Column("pdrtmp_version")
    var version: Int? = null,

    @Column("pdrtmp_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("pdrtmp_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("pdr_id")
    var productDicRowId: UUID? = null,

    @Column("st_from_id")
    var stationFromId: UUID? = null,

    @Column("st_to_id")
    var stationToId: UUID? = null,

    @Column("amount")
    var amount: Long? = null,

    @Column("tags")
    var tags: String? = null
)

data class ProductDictRowTariffMatrixPricePK(
    val pdrtmpId: UUID? = null,
    val pdrtmpVersion: Int? = null,
) : Serializable