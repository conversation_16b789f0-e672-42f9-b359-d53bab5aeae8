package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("tariff")
data class Tariff(

    @Column("t_id")
    var id: UUID? = null,

    @Column("t_version")
    var version: Int? = null,

    @Column("t_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("t_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("t_project_id")
    var projectId: UUID? = null,

    @Column("t_name")
    var name: String? = null,

    /**
     * Статус тарифа
     * @see TariffStatus
     */
    @Column("t_status")
    var status: TariffStatus? = null,

    @Column("tags")
    var tags: String? = null
)

data class TariffPK(
    val tId: UUID? = null,
    val tVersion: Int? = null,
) : Serializable


enum class TariffStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}
