package ru.sbertroika.tkp3.pro.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("country")
data class Country(

    /** Уникальный идентификатор страны */
    @Id
    @Column("c_id")
    var id: UUID? = null,

    /** Наименование страны */
    @Column("c_name")
    var name: String? = null,

    /** Код страны (ISO 3166-1 alpha-2) */
    @Column("c_code")
    var code: String? = null,

    /** Дата и время создания записи */
    @Column("c_created_at")
    var createdAt: Timestamp? = null,

    /** Дата и время последнего обновления записи */
    @Column("c_updated_at")
    var updatedAt: Timestamp? = null
) 