package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("route_constraint")
data class RouteConstraint(

    @Column("rc_id")
    var id: UUID? = null,

    @Column("rc_version")
    var version: Int? = null,

    @Column("rc_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("rc_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("r_id")
    var routeId: UUID? = null,

    @Column("rc_type")
    var type: RouteConstraintType? = null,

    @Column("rc_base_rule")
    var baseRule: BaseRule? = null,

    @Column("tags")
    var tags: String? = null
)

data class RouteConstraintPK(
    val rcId: UUID? = null,
    val rcVersion: Int? = null,
) : Serializable

enum class RouteConstraintType {
    TRANSPORT
}
