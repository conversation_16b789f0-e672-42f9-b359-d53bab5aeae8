package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class ProjectFunctionPK(
    val pfId: UUID? = null,
    val pfVersion: Int? = null
)

@Table("project_function")
data class ProjectFunction(

    @Column("pf_id")
    var id: UUID? = null,

    @Column("pf_version")
    var version: Int? = null,

    @Column("pf_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("pf_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("pf_type")
    var type: String? = null,

    @Column("pf_project_id")
    var projectId: UUID? = null,

    @Column("pf_project_version")
    var projectVersion: Int? = null,

    @Column("pf_active_from")
    var activeFrom: Timestamp? = null,

    @Column("pf_active_till")
    var activeTill: Timestamp? = null,

    @Column("pf_status")
    var status: ProjectFunctionStatus? = null
)

enum class ProjectFunctionStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}

enum class ProjectFunctionType {
    PRO, PASIV
}