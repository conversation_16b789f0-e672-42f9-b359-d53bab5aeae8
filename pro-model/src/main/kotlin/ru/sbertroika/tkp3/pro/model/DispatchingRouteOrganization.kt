package ru.sbertroika.tkp3.pro.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class DispatchingRouteOrganizationPK(
    val roId: UUID? = null,
    val roVersion: Int? = null
)

//TODO Переименовать диспетчеризацию -> Control
/**
 * Диспетчеризация
 */
@Table(value = "dispatching_route_organization")
data class DispatchingRouteOrganization(
    @Id
    @Column("ro_id")
    var id: String? = null,

    @Column("ro_version")
    var version: UInt? = null,

    @Column("ro_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("ro_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("ro_organization_id")
    var organizationId: UUID? = null,

    @Column("ro_route_id")
    var routeId: UUID? = null,

    @Column("ro_active_from")
    var activeFrom: Timestamp? = null,

    @Column("ro_active_till")
    var activeTill: Timestamp? = null,

    @Column("ro_status")
    var status: DispatchingRouteOrganizationStatus? = null,
)

enum class DispatchingRouteOrganizationStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}