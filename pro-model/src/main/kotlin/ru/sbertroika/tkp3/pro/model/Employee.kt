package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*


data class EmployeePK(
    var profileId: String,
    var version: Int
)

@Table("employee")
data class Employee(
    @Column("e_profile_id")
    var profileId: UUID,

    @Column("e_version")
    var version: Int,

    @Column("e_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("e_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("e_organization_id")
    var organizationId: UUID,

    @Column("e_role")
    var role: String,

    @Column("e_name")
    var name: String,

    @Column("e_surname")
    var surname: String,

    @Column("e_middle_name")
    var middleName: String? = null,

    @Column("e_phone")
    var phone: String? = null,

    @Column("e_email")
    var email: String? = null,

    @Column("e_birth_day")
    var birthDay: Timestamp? = null,

    @Column("e_series_and_number_passport")
    var seriesAndNumberPassport: String? = null,

    @Column("e_issue_date_passport")
    var issueDatePassport: Timestamp? = null,

    @Column("e_gender")
    var gender: Boolean? = null,

    @Column("e_photo_id")
    var photoId: String? = null,

    @Column("e_language")
    var language: String? = null,

    @Column("e_personal_number")
    var personalNumber: String? = null,

    @Column("e_pin_hash")
    var pinHash: String? = null,

    @Column("e_enabled")
    var enabled: Boolean = true,

    @Column("e_is_deleted")
    var isDeleted: Boolean = true,

    @Column("e_login")
    var login: String? = null,

    @Column("e_password")
    var password: String? = null
) {

    fun toFIO(): String {
        val sb = StringBuilder()
        if (surname.isNotEmpty()) {
            sb.append(surname)
        }

        if (name.isNotEmpty()) {
            sb.append(" ")
            sb.append(name)
        }

        if (middleName?.isNotEmpty() == true) {
            sb.append(" ")
            sb.append(middleName)
        }
        return sb.toString()
    }
}
