package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("tariff_constraint")
data class TariffConstraint(

    @Column("tc_id")
    var id: UUID? = null,

    @Column("tc_version")
    var version: Int? = null,

    @Column("tc_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("tc_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("t_id")
    var tariffId: UUID? = null,

    @Column("tc_type")
    var type: TariffConstraintType? = null,

    @Column("tc_base_rule")
    var baseRule: BaseRule? = null,

    @Column("tags")
    var tags: String? = null
)

data class TariffConstraintPK(
    val tcId: UUID? = null,
    val tcVersion: Int? = null,
) : Serializable

enum class TariffConstraintType {
    ROUTE, TRANSPORT
}
