package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class ManifestPK(
    val mId: UUID? = null,
    val mVersion: Int? = null
)

@Table("manifest")
data class Manifest(

    @Column("m_id")
    var id: UUID? = null,

    @Column("m_version")
    var version: Int? = null,

    @Column("m_version_created_at")
    var createdAt: Timestamp? = null,

    @Column("m_version_created_by")
    var createdBy: UUID? = null,

    @Column("m_project_id")
    var projectId: UUID? = null,

    @Column("m_project_version")
    var projectVersion: Int? = null,

    @Column("m_active_from")
    var activeFrom: Timestamp? = null,

    @Column("m_active_till")
    var activeTill: Timestamp? = null,

    @Column("m_path")
    var path: String? = null
)
