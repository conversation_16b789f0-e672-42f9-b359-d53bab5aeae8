package ru.sbertroika.tkp3.pro.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("region")
data class Region(

    /** Уникальный идентификатор региона */
    @Id
    @Column("r_id")
    var id: UUID? = null,

    /** Наименование региона */
    @Column("r_name")
    var name: String? = null,

    /** Идентификатор страны, к которой относится регион */
    @Column("r_country_id")
    var countryId: UUID? = null,

    /** Код региона */
    @Column("r_code")
    var code: String? = null,

    /** Дата и время создания записи */
    @Column("r_created_at")
    var createdAt: Timestamp? = null,

    /** Дата и время последнего обновления записи */
    @Column("r_updated_at")
    var updatedAt: Timestamp? = null
) 