package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("vehicle")
data class Vehicle(

    @Column("vh_id")
    var id: UUID? = null,

    @Column("vh_version")
    var version: Int? = null,

    @Column("vh_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("vh_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("v_project_id")
    var projectId: UUID? = null,

    @Column("v_organization_id")
    var organizationId: UUID? = null,

    @Column("vh_type")
    var type: VehicleType? = null,

    @Column("vh_number")
    var number: String? = null,

    /**
     * Статус транспорта
     * @see VehicleStatus
     */
    @Column("vh_status")
    var status: VehicleStatus? = null,

    @Column("tags")
    var tags: String? = null
)

data class VehiclePK(
    val vhId: UUID? = null,
    val vhVersion: Int? = null,
) : Serializable

enum class VehicleType {
    BUS, TRAM, TROLLEYBUS, METRO
}

enum class VehicleStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}
