package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("vehicle_constraint_ex")
data class VehicleConstraintException(

    @Column("vhce_id")
    var id: UUID? = null,

    @Column("vhce_version")
    var version: Int? = null,

    @Column("vhce_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("vhce_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("vhc_id")
    var vehicleConstraintId: UUID? = null,

    @Column("e_id")
    var exceptionId: UUID? = null,

    @Column("tags")
    var tags: String? = null
)

data class VehicleConstraintExceptionPK(
    val vhceId: UUID? = null,
    val vhceVersion: Int? = null,
) : Serializable
