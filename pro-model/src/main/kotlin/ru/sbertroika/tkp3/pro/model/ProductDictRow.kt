package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("product_dict_row")
data class ProductDictRow(

    @Column("pdr_id")
    var id: UUID? = null,

    @Column("pdr_version")
    var version: Int? = null,

    @Column("pdr_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("pdr_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("pdr_project_id")
    var projectId: UUID? = null,

    @Column("p_id")
    var productId: UUID? = null,

    @Column("t_id")
    var tariffId: UUID? = null,

    @Column("pdr_method_type")
    var paymentMethodType: PayMethodType? = null,

    @Column("pdr_fix_price")
    var isFixPrice: Boolean? = null,

    @Column("pdr_price")
    var price: Long? = null,

    @Column("tags")
    var tags: String? = null
)

data class ProductDictRowPK(
    val pdrId: UUID? = null,
    val pdrVersion: Int? = null,
) : Serializable

enum class PayMethodType {
    CASH, EMV, TROIKA_TICKET, TROIKA_WALLET, ABT_TICKET, ABT_WALLET, PROSTOR_TICKET, QR_TICKET, QR_WALLET
}