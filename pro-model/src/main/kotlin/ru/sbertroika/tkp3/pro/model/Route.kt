package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("route")
data class Route(

    @Column("r_id")
    var id: UUID? = null,

    @Column("r_version")
    var version: Int? = null,

    @Column("r_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("r_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("r_active_from")
    var activeFrom: Timestamp? = null,

    @Column("r_active_till")
    var activeTill: Timestamp? = null,

    @Column("r_project_id")
    var projectId: UUID? = null,

    /**
     * Порядковый номер маршрута в рамках проекта
     */
    @Column("r_index")
    var index: Int? = null,

    /**
     * Название маршрута
     */
    @Column("r_name")
    var name: String? = null,

    /**
     * Номер маршрута
     */
    @Column("r_number")
    var number: String? = null,

    /**
     * Сзема маршрута
     * @see RouteScheme
     */
    @Column("r_scheme")
    var scheme: RouteScheme? = null,

    /**
     * Статус маршрута
     * @see RouteStatus
     */
    @Column("r_status")
    var status: RouteStatus? = null,

    @Column("tags")
    var tags: String? = null
)

data class RoutePK(
    val rId: UUID? = null,
    val rVersion: Int? = null,
) : Serializable

enum class RouteStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}

enum class RouteScheme {
    DIRECTIONAL, CIRCLE
}
