package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("participant_organization")
data class ParticipantOrganization(

    /** Уникальный идентификатор записи участника */
    @Column("po_id")
    var id: UUID? = null,

    /** Идентификатор проекта */
    @Column("po_project_id")
    var projectId: UUID? = null,

    /** Версия проекта */
    @Column("po_project_version")
    var projectVersion: Int? = null,

    /** Идентификатор организации-участника */
    @Column("po_organization_id")
    var organizationId: UUID? = null,

    /** Роль организации в проекте (operator, carrier, processing_center, contractor, customer, partner, coordinator) */
    @Column("po_role")
    var role: String? = null,

    /** Дата и время создания записи */
    @Column("po_created_at")
    var createdAt: Timestamp? = null,

    /** Дата и время последнего обновления записи */
    @Column("po_updated_at")
    var updatedAt: Timestamp? = null
) 