package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("vehicle_constraint")
data class VehicleConstraint(

    @Column("vhc_id")
    var id: UUID? = null,

    @Column("vhc_version")
    var version: Int? = null,

    @Column("vhc_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("vhc_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("vh_id")
    var vehicleId: UUID? = null,

    @Column("vhc_type")
    var type: VehicleConstraintType? = null,

    @Column("vhc_base_rule")
    var baseRule: BaseRule? = null,

    @Column("tags")
    var tags: String? = null
)

data class VehicleConstraintPK(
    val vhcId: UUID? = null,
    val vhcVersion: Int? = null,
) : Serializable

enum class VehicleConstraintType {
    ROUTE
}
