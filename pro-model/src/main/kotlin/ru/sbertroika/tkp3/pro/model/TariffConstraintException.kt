package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("tariff_constraint_ex")
data class TariffConstraintException(

    @Column("tce_id")
    var id: UUID? = null,

    @Column("tce_version")
    var version: Int? = null,

    @Column("tce_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("tce_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("tc_id")
    var tariffConstraintId: UUID? = null,

    @Column("e_id")
    var exceptionId: UUID? = null,

    @Column("tags")
    var tags: String? = null
)

data class TariffConstraintExceptionPK(
    val tceId: UUID? = null,
    val tceVersion: Int? = null,
) : Serializable
