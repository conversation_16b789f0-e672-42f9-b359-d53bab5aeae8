package ru.sbertroika.tkp3.pro.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("city")
data class City(

    /** Уникальный идентификатор города */
    @Id
    @Column("city_id")
    var id: UUID? = null,

    /** Наименование города */
    @Column("city_name")
    var name: String? = null,

    /** Идентификатор региона, к которому относится город */
    @Column("city_region_id")
    var regionId: UUID? = null,

    /** Код города */
    @Column("city_code")
    var code: String? = null,

    /** Дата и время создания записи */
    @Column("city_created_at")
    var createdAt: Timestamp? = null,

    /** Дата и время последнего обновления записи */
    @Column("city_updated_at")
    var updatedAt: Timestamp? = null
) 