package ru.sbertroika.tkp3.pro.model

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.ZonedDateTime
import java.util.*

@Table("ticket")
data class Ticket(

    /**
     * Идентификатор билета
     */
    @Column("ticket_id")
    var ticketId: UUID? = null,

    /**
     * Идентификатор проекта
     */
    @Column("project_id")
    var projectId: UUID? = null,

    /**
     * Идентификатор организации
     */
    @Column("org_id")
    var orgId: UUID? = null,

    /**
     * Время формирования транзакции на терминале
     */
    @Column("created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var createdAt: ZonedDateTime? = null,

    /**
     * Время формирования транзакции на сервере
     */
    @Column("record_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var recordAt: ZonedDateTime? = null,

    /**
     * Идентификатор проекта
     */
    @Column("ticket_series")
    var ticketSeries: String? = null,

    /**
     * Идентификатор проекта
     */
    @Column("ticket_number")
    var ticketNumber: String? = null,

    /**
     * Идентификатор продукта
     */
    @Column("pr_id")
    var productId: UUID? = null,

    /**
     * Версия записи о продукте
     */
    @Column("pr_ver")
    var productVersion: Int? = null,

    /**
     * Идентификатор тарифа
     */
    @Column("t_id")
    var tariffId: UUID? = null,

    /**
     * Версия записи о тарифе
     */
    @Column("t_ver")
    var tariffVersion: Int? = null,

    /**
     * Идентификатор маршрута
     */
    @Column("r_id")
    var routeId: UUID? = null,

    /**
     * Версия записи о маршруте
     */
    @Column("r_ver")
    var routeVersion: Int? = null,

    /**
     * Идентификатор Т/С
     */
    @Column("v_id")
    var vehicleId: UUID? = null,

    /**
     * Версия записи о Т/С
     */
    @Column("v_ver")
    var vehicleVersion: Int? = null,

    /**
     * Идентификатор водителя
     */
    @Column("driver_id")
    var driverId: UUID? = null,

    /**
     * Идентификатор кондуктора
     */
    @Column("conductor_id")
    var conductorId: UUID? = null,

    /**
     * Идентификатор кассира
     */
    @Column("cashier_id")
    var cashierId: UUID? = null,

    /**
     * Станция входа
     */
    @Column("st_from_id")
    var stationFromId: UUID? = null,

    /**
     * Версия записи о станции входа
     */
    @Column("st_from_ver")
    var stationFromVersion: Int? = null,

    /**
     * Станция выхода
     */
    @Column("st_to_id")
    var stationToId: UUID? = null,

    /**
     * Версия записи о станции выхода
     */
    @Column("st_from_ver")
    var stationToVersion: Int? = null,

    /**
     * Идентификатор абонемента
     */
    @Column("a_id")
    var abonementId: UUID? = null,

    /**
     * Версия записи об абонементе
     */
    @Column("a_ver")
    var abonementVersion: Int? = null,

    /**
     * Идентификатор кошелька
     */
    @Column("wa_id")
    var walletId: UUID? = null,

    /**
     * Версия записи о кошельке
     */
    @Column("wa_ver")
    var walletVersion: Int? = null,

    /**
     * Cтоимость билета
     */
    @Column("amount")
    var amount: Int? = null,

    /**
     * Идентификатор транзакции (заказа)
     */
    @Column("trx_id")
    var trxId: UUID? = null,

    /**
     * Идентификатор терминала
     */
    @Column("terminal_id")
    var terminalId: UUID? = null,

    /**
     * Заводской номер терминала
     */
    @Column("terminal_serial")
    var terminalSerial: String? = null,

    /**
     * Банковский идентификатор терминала
     */
    @Column("tid")
    var tid: String? = null,

    /**
     * Номер смены на терминале
     */
    @Column("shift_num")
    var shiftNum: Int? = null,

    /**
     * Единый номер операции на терминале (уникальный в рамках смены)
     */
    @Column("ern")
    var ern: Int? = null,

    /**
     * Тип способа оплаты
     */
    @Column("pay_method_type")
    var payMethodType: PayMethodType? = null,


    @Column("tags")
    var tags: String? = null
)
