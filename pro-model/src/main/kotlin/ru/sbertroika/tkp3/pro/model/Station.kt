package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import org.springframework.data.annotation.Id
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("station")
data class Station(

    /** Уникальный идентификатор станции */
    @Column("st_id")
    var id: UUID? = null,

    /** Версия записи станции для поддержки версионирования */
    @Column("st_version")
    var version: Int? = null,

    /** Дата и время создания версии */
    @Column("st_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    /** Идентификатор пользователя, создавшего версию */
    @Column("st_version_created_by")
    var versionCreatedBy: UUID? = null,

    /** Идентификатор проекта, к которому относится станция */
    @Column("p_id")
    var projectId: UUID? = null,

    /** Идентификатор тарифной зоны */
    @Column("tz_id")
    var tariffZoneId: UUID? = null,

    /** Наименование станции */
    @Column("st_name")
    var name: String? = null,

    /** Латинское наименование станции */
    @Column("st_latin_name")
    var latinName: String? = null,

    /** Статус станции */
    @Column("st_status")
    var status: String? = null,

    /** Широта координат станции */
    @Column("lat")
    var latitude: Double? = null,

    /** Долгота координат станции */
    @Column("long")
    var longitude: Double? = null,

    /** Идентификатор города */
    @Column("st_city_id")
    var cityId: UUID? = null,

    /** Идентификатор района */
    @Column("st_district_id")
    var districtId: UUID? = null,

    /** Идентификатор региона */
    @Column("st_region_id")
    var regionId: UUID? = null,

    /** Идентификатор страны */
    @Column("st_country_id")
    var countryId: UUID? = null,

    /** Теги станции в формате JSON */
    @Column("tags")
    var tags: String = "",

    /** Дата последней синхронизации */
    @Column("st_last_sync_date")
    var lastSyncDate: Timestamp? = null,

    /** Статус синхронизации */
    @Column("st_sync_status")
    var syncStatus: String? = null,


)

data class StationPK(
    val stId: UUID? = null,
    val stVersion: Int? = null,
) : Serializable

enum class StationStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}