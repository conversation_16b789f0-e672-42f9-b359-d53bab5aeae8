package ru.sbertroika.tkp3.pro.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("district")
data class District(

    /** Уникальный идентификатор района */
    @Id
    @Column("d_id")
    var id: UUID? = null,

    /** Наименование района */
    @Column("d_name")
    var name: String? = null,

    /** Идентификатор города, к которому относится район */
    @Column("d_city_id")
    var cityId: UUID? = null,

    /** Код района */
    @Column("d_code")
    var code: String? = null,

    /** Дата и время создания записи */
    @Column("d_created_at")
    var createdAt: Timestamp? = null,

    /** Дата и время последнего обновления записи */
    @Column("d_updated_at")
    var updatedAt: Timestamp? = null
) 