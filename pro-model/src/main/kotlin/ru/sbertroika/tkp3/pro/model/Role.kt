package ru.sbertroika.tkp3.pro.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.util.*

@Table("role")
data class Role(
    @Column("r_id")
    @Id var id: UUID,

    @Column("r_name")
    var name: String,

    @Column("r_description")
    var description: String?
)

enum class TerminalRole(val role: String) {
    /**
     * Администратор
     */
    ADMIN("terminal_user_admin"),

    /**
     * Водитель
     */
    DRIVER("terminal_user_driver"),

    /**
     * Кассир
     */
    CASHIER("terminal_user_cashier"),

    /**
     * Инкассатор
     */
    COLLECTOR("terminal_user_collector"),

    /**
     * Кондуктор
     */
    CONDUCTOR("terminal_user_conductor"),

    /**
     * Контролер
     */
    CONTROLER("terminal_user_controler");


    companion object {
        fun findByRole(role: String): TerminalRole? = values().find { it.role == role }
    }
}