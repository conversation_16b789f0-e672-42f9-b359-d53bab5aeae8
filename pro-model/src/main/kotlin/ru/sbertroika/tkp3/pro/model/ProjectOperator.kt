package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("project_operator")
data class ProjectOperator(

    /** Уникальный идентификатор записи связи */
    @Column("po_id")
    var id: UUID? = null,

    /** Идентификатор проекта */
    @Column("po_project_id")
    var projectId: UUID? = null,

    /** Версия проекта */
    @Column("po_project_version")
    var projectVersion: Int? = null,

    /** Дата и время создания связи */
    @Column("po_created_at")
    var createdAt: Timestamp? = null,

    /** Идентификатор операторской организации */
    @Column("po_operator_id")
    var operatorId: UUID? = null,

    /** Наименование операторской организации */
    @Column("po_operator_name")
    var operatorName: String? = null,

    /** Роль оператора в проекте (operator, coordinator и т.д.) */
    @Column("po_operator_role")
    var operatorRole: String? = null
) 