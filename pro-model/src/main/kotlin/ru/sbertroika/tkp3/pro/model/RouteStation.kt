package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("route_station")
data class RouteStation(

    @Column("rs_id")
    var id: UUID? = null,

    @Column("rs_version")
    var version: Int? = null,

    @Column("rs_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("rs_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("rs_active_from")
    var activeFrom: Timestamp? = null,

    @Column("rs_active_till")
    var activeTill: Timestamp? = null,

    @Column("r_id")
    var routeId: UUID? = null,

    @Column("st_id")
    var stationId: UUID? = null,

    @Column("rs_pos")
    var position: Int? = null,

    @Column("rs_is_del")
    var isDelete: Boolean? = null,

    @Column("tags")
    var tags: String? = null
)

data class RouteStationPK(
    val rsId: UUID? = null,
    val rsVersion: Int? = null,
) : Serializable
