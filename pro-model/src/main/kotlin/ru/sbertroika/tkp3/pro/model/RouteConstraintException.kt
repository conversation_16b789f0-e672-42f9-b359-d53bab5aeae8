package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("route_constraint_ex")
data class RouteConstraintException(

    @Column("rce_id")
    var id: UUID? = null,

    @Column("rce_version")
    var version: Int? = null,

    @Column("rce_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("rce_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("rc_id")
    var routeConstraintId: UUID? = null,

    @Column("e_id")
    var exceptionId: UUID? = null,

    @Column("tags")
    var tags: String? = null
)

data class RouteConstraintExceptionPK(
    val rceId: UUID? = null,
    val rceVersion: Int? = null,
) : Serializable
